[{"_debug_info": {"total_claims": 8, "filtering_rule": "Only logs with typeOfLog: \"Claim\" will be processed", "alteration_timestamp": "2025-06-13T21:40:51.544960"}}, {"claimNo": 127762978, "logs": [{"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "preExecutionApprovedAmount": 214559.73, "preExecutionTotalBillAmount": 236928.95, "preExectionTotalDeductionAmount": 22369.27, "postExecutionApprovedAmount": 214559.73, "postExecutionTotalBillAmount": 236928.95, "postExectionTotalDeductionAmount": 22369.27, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "preExecutionApprovedAmount": 214559.73, "preExecutionTotalBillAmount": 236928.95, "preExectionTotalDeductionAmount": 22369.27, "postExecutionApprovedAmount": 214559.73, "postExecutionTotalBillAmount": 236928.95, "postExectionTotalDeductionAmount": 22369.27, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 214559.73 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 214559.73 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 259133.55, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 24002.29"}, {"message": "HMOU Discount Calculated: 22369.28"}, {"message": "MAX Discount Calculated: 24002.29"}, {"message": "Discount Calculation Is Goving with HMOU"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Consultant Charges: 5958.03,Investigation & Lab Charges: 6095.29,Miscellaneous Charges: 2779.84,Pharmacy & Medicine Charges: 2356.71,Hospital Charges: 5179.40"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 239928.95, "preExectionTotalDeductionAmount": 22369.27, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 236928.95, "postExectionTotalDeductionAmount": 22369.27, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9500.0"}, {"message": "Tariff ICU Room Rent From Master:9500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 3000.000"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 214559.73, "preExecutionTotalBillAmount": 236928.95, "preExectionTotalDeductionAmount": 22369.27, "postExecutionApprovedAmount": 214559.73, "postExecutionTotalBillAmount": 236928.95, "postExectionTotalDeductionAmount": 22369.27, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer room rent"}, {"message": "ICU : Room Rent based on insurer "}, {"message": "Normal Room Rent Lenght of Stay : 6.00"}, {"message": "ICU Room Rent Lenght of Stay : 0"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 6.00"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 9500.0"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 57000.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 239928.95, "preExectionTotalDeductionAmount": 22369.27, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 239928.95, "postExectionTotalDeductionAmount": 22369.27, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}], "_cleaning_metadata": {"fields_removed": ["logCreatedDate (from logs)", "data (from logs)", "timeOfOccurrence (from data entries and rule logs)", "loggerType (from data entries and rule logs)", "configRuleLogObject (from rule engine logs)", "matchingConfig (from rule engine logs)"], "fields_renamed": ["logMessages → reasons (in rule engine logs)"], "filtering_applied": ["Only rule engine logs with non-empty matchingConfig arrays are included", "Only rule engine logs with non-zero postExecutionTotalBillAmount are included"], "grouping_applied": ["Rule engine logs grouped by category as dictionary structure with metadata support"], "cleaning_timestamp": "2025-06-13T21:40:51.406314", "purpose": "Remove unnecessary fields, rename for clarity, filter for relevant rule logs, and group by category"}, "_processing_metadata": {"filtered_for_claim_logs_only": true, "processing_timestamp": "2025-06-13T21:40:51.544906", "total_logs": 1, "claim_logs_count": 1}}, {"claimNo": 127787972, "logs": [{"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "preExecutionApprovedAmount": 249428.16, "preExecutionTotalBillAmount": 275660.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 249428.16, "postExecutionTotalBillAmount": 275660.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "preExecutionApprovedAmount": 249428.16, "preExecutionTotalBillAmount": 275660.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 249428.16, "postExecutionTotalBillAmount": 275660.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 249428.16 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 249428.16 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 294657.45, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 26231.84"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 26231.84"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Miscellaneous Charges: 1380.00,Investigation & Lab Charges: 7684.00,Consultant Charges: 7896.80,Pharmacy & Medicine Charges: 1386.04,Hospital Charges: 7885.00"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 278660.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 275660.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9500.0"}, {"message": "Tariff ICU Room Rent From Master:9500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.000"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 249428.16, "preExecutionTotalBillAmount": 275660.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 249428.16, "postExecutionTotalBillAmount": 275660.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer nursing,room rent"}, {"message": "ICU : Room Rent based on insurer icu charges,icu monitoring charges"}, {"message": "Normal Room Rent Lenght of Stay : 5.50"}, {"message": "ICU Room Rent Lenght of Stay : 2.00"}, {"message": "Nursing Charges PerDay : 0.0"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 2.00"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 11800"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 5.50"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 9499.99"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 52250.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 23600.00"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 278660.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 278660.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}, {"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "preExecutionApprovedAmount": 229628.16, "preExecutionTotalBillAmount": 255860.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 229628.16, "postExecutionTotalBillAmount": 255860.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "preExecutionApprovedAmount": 229628.16, "preExecutionTotalBillAmount": 255860.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 229628.16, "postExecutionTotalBillAmount": 255860.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 229628.16 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 229628.16 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 294657.45, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 26231.84"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 26231.84"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Miscellaneous Charges: 1380.00,Investigation & Lab Charges: 7684.00,Consultant Charges: 7896.80,Pharmacy & Medicine Charges: 1386.04,Hospital Charges: 7885.00"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 258860.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 255860.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9500.0"}, {"message": "Tariff ICU Room Rent From Master:9500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.000"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 229628.16, "preExecutionTotalBillAmount": 255860.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 229628.16, "postExecutionTotalBillAmount": 255860.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer nursing,room rent"}, {"message": "ICU : Room Rent based on insurer icu charges,icu monitoring charges"}, {"message": "Normal Room Rent Lenght of Stay : 5.50"}, {"message": "ICU Room Rent Lenght of Stay : 2.00"}, {"message": "Nursing Charges PerDay : 0.0"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 2.00"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 11800"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 5.50"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 9499.99"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 52250.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 23600.00"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 258860.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 258860.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}, {"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "preExecutionApprovedAmount": 229628.16, "preExecutionTotalBillAmount": 255860.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 229628.16, "postExecutionTotalBillAmount": 255860.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "preExecutionApprovedAmount": 229628.16, "preExecutionTotalBillAmount": 255860.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 229628.16, "postExecutionTotalBillAmount": 255860.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 229628.16 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 229628.16 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 294657.45, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 26231.84"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 26231.84"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Miscellaneous Charges: 1380.00,Investigation & Lab Charges: 7684.00,Consultant Charges: 7896.80,Pharmacy & Medicine Charges: 1386.04,Hospital Charges: 7885.00"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 258860.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 255860.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9500.0"}, {"message": "Tariff ICU Room Rent From Master:9500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.000"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 229628.16, "preExecutionTotalBillAmount": 255860.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 229628.16, "postExecutionTotalBillAmount": 255860.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer nursing,room rent"}, {"message": "ICU : Room Rent based on insurer icu charges,icu monitoring charges"}, {"message": "Normal Room Rent Lenght of Stay : 5.50"}, {"message": "ICU Room Rent Lenght of Stay : 2.00"}, {"message": "Nursing Charges PerDay : 0.0"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 2.00"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 11800"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 5.50"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 9499.99"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 52250.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 23600.00"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 258860.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 258860.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}, {"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "preExecutionApprovedAmount": 229628.16, "preExecutionTotalBillAmount": 255860.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 229628.16, "postExecutionTotalBillAmount": 255860.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "preExecutionApprovedAmount": 229628.16, "preExecutionTotalBillAmount": 255860.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 229628.16, "postExecutionTotalBillAmount": 255860.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 229628.16 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 229628.16 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 294657.45, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 26231.84"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 26231.84"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Miscellaneous Charges: 1380.00,Investigation & Lab Charges: 7684.00,Consultant Charges: 7896.80,Pharmacy & Medicine Charges: 1386.04,Hospital Charges: 7885.00"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 258860.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 255860.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9500.0"}, {"message": "Tariff ICU Room Rent From Master:9500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.000"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 229628.16, "preExecutionTotalBillAmount": 255860.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 229628.16, "postExecutionTotalBillAmount": 255860.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer nursing,room rent"}, {"message": "ICU : Room Rent based on insurer icu charges,icu monitoring charges"}, {"message": "Normal Room Rent Lenght of Stay : 5.50"}, {"message": "ICU Room Rent Lenght of Stay : 2.00"}, {"message": "Nursing Charges PerDay : 0.0"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 2.00"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 11800"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 5.50"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 9499.99"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 52250.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 23600.00"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 258860.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 258860.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}, {"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "preExecutionApprovedAmount": 228028.16, "preExecutionTotalBillAmount": 254260.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 228028.16, "postExecutionTotalBillAmount": 254260.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "preExecutionApprovedAmount": 228028.16, "preExecutionTotalBillAmount": 254260.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 228028.16, "postExecutionTotalBillAmount": 254260.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 228028.16 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 228028.16 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 294657.45, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 26231.84"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 26231.84"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Hospital Charges: 7885.00,Pharmacy & Medicine Charges: 1386.04,Investigation & Lab Charges: 7684.00,Miscellaneous Charges: 1380.00,Consultant Charges: 7896.80"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 258860.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 254260.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9500.0"}, {"message": "Tariff ICU Room Rent From Master:9500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.000"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "ICU Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 800.000"}, {"message": "ICU Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 800.00"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 228028.16, "preExecutionTotalBillAmount": 254260.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 228028.16, "postExecutionTotalBillAmount": 254260.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer room rent,nursing"}, {"message": "ICU : Room Rent based on insurer icu charges,icu nursing charges"}, {"message": "Normal Room Rent Lenght of Stay : 5.50"}, {"message": "ICU Room Rent Lenght of Stay : 2.00"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Nursing Charges PerDay : 0.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 2.00"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 11000.0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 5.50"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 9499.99"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 52250.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 22000.000"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 258860.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 258860.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}], "_cleaning_metadata": {"fields_removed": ["logCreatedDate (from logs)", "data (from logs)", "timeOfOccurrence (from data entries and rule logs)", "loggerType (from data entries and rule logs)", "configRuleLogObject (from rule engine logs)", "matchingConfig (from rule engine logs)"], "fields_renamed": ["logMessages → reasons (in rule engine logs)"], "filtering_applied": ["Only rule engine logs with non-empty matchingConfig arrays are included", "Only rule engine logs with non-zero postExecutionTotalBillAmount are included"], "grouping_applied": ["Rule engine logs grouped by category as dictionary structure with metadata support"], "cleaning_timestamp": "2025-06-13T21:40:51.452259", "purpose": "Remove unnecessary fields, rename for clarity, filter for relevant rule logs, and group by category"}, "_processing_metadata": {"filtered_for_claim_logs_only": true, "processing_timestamp": "2025-06-13T21:40:51.544937", "total_logs": 5, "claim_logs_count": 5}}, {"claimNo": 127791207, "logs": [{"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "preExecutionApprovedAmount": 209439.95, "preExecutionTotalBillAmount": 259801.2, "preExectionTotalDeductionAmount": 50361.05, "postExecutionApprovedAmount": 209439.95, "postExecutionTotalBillAmount": 259801.2, "postExectionTotalDeductionAmount": 50361.05, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "preExecutionApprovedAmount": 209439.95, "preExecutionTotalBillAmount": 259801.2, "preExectionTotalDeductionAmount": 50361.05, "postExecutionApprovedAmount": 209439.95, "postExecutionTotalBillAmount": 259801.2, "postExectionTotalDeductionAmount": 50361.05, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 209439.95 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 209439.95 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 292520.8, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 27177.62"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 27177.62"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Investigation & Lab Charges: 12690.185955487609769971913110,Consultant Charges: 15132.125244782593237814199880,Hospital Charges: 13263.598678138443488463042630,Pharmacy & Medicine Charges: 3400.6437938635474810680129419,Miscellaneous Charges: 5874.4963277278060226828314438"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 270801.2, "preExectionTotalDeductionAmount": 50361.05, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 259801.2, "postExectionTotalDeductionAmount": 50361.05, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9500.0"}, {"message": "Tariff ICU Room Rent From Master:9500.0"}, {"message": "ICU Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 11000.000"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 209439.95, "preExecutionTotalBillAmount": 259801.2, "preExectionTotalDeductionAmount": 50361.05, "postExecutionApprovedAmount": 209439.95, "postExecutionTotalBillAmount": 259801.2, "postExectionTotalDeductionAmount": 50361.05, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer room rent"}, {"message": "ICU : Room Rent based on insurer icu charges"}, {"message": "Normal Room Rent Lenght of Stay : 6.00"}, {"message": "ICU Room Rent Lenght of Stay : 1.00"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 1.00"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 11000.0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 6.00"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 8708.33"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 57000.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 11000.000"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 270801.2, "preExectionTotalDeductionAmount": 50361.05, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 270801.2, "postExectionTotalDeductionAmount": 50361.05, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}, {"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "preExecutionApprovedAmount": 181449.63, "preExecutionTotalBillAmount": 208841.2, "preExectionTotalDeductionAmount": 27391.37, "postExecutionApprovedAmount": 181449.63, "postExecutionTotalBillAmount": 208841.2, "postExectionTotalDeductionAmount": 27391.37, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "preExecutionApprovedAmount": 181449.63, "preExecutionTotalBillAmount": 208841.2, "preExectionTotalDeductionAmount": 27391.37, "postExecutionApprovedAmount": 181449.63, "postExecutionTotalBillAmount": 208841.2, "postExectionTotalDeductionAmount": 27391.37, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 181449.63 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 181449.63 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 294658.3, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 27391.37"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 27391.37"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Consultant Charges: 8471.00,Investigation & Lab Charges: 7104.00,Miscellaneous Charges: 3288.56,Pharmacy & Medicine Charges: 889.06,Hospital Charges: 7638.75"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 275728.7, "preExectionTotalDeductionAmount": 27391.37, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 208841.2, "postExectionTotalDeductionAmount": 27391.37, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9500.0"}, {"message": "Tariff ICU Room Rent From Master:9500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 66887.500"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 181449.63, "preExecutionTotalBillAmount": 208841.2, "preExectionTotalDeductionAmount": 27391.37, "postExecutionApprovedAmount": 181449.63, "postExecutionTotalBillAmount": 208841.2, "postExectionTotalDeductionAmount": 27391.37, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer room rent"}, {"message": "ICU : Room Rent based on insurer "}, {"message": "Normal Room Rent Lenght of Stay : 1.00"}, {"message": "ICU Room Rent Lenght of Stay : 0"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 1.00"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 9500.0"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 9500.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 275728.7, "preExectionTotalDeductionAmount": 27391.37, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 275728.7, "postExectionTotalDeductionAmount": 27391.37, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}, {"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "preExecutionApprovedAmount": 181449.63, "preExecutionTotalBillAmount": 208841.2, "preExectionTotalDeductionAmount": 27391.37, "postExecutionApprovedAmount": 181449.63, "postExecutionTotalBillAmount": 208841.2, "postExectionTotalDeductionAmount": 27391.37, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "preExecutionApprovedAmount": 181449.63, "preExecutionTotalBillAmount": 208841.2, "preExectionTotalDeductionAmount": 27391.37, "postExecutionApprovedAmount": 181449.63, "postExecutionTotalBillAmount": 208841.2, "postExectionTotalDeductionAmount": 27391.37, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 181449.63 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 181449.63 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 294658.3, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 27391.37"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 27391.37"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Consultant Charges: 8471.00,Investigation & Lab Charges: 7104.00,Miscellaneous Charges: 3288.56,Pharmacy & Medicine Charges: 889.06,Hospital Charges: 7638.75"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 275728.7, "preExectionTotalDeductionAmount": 27391.37, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 208841.2, "postExectionTotalDeductionAmount": 27391.37, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9500.0"}, {"message": "Tariff ICU Room Rent From Master:9500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 66887.500"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 181449.63, "preExecutionTotalBillAmount": 208841.2, "preExectionTotalDeductionAmount": 27391.37, "postExecutionApprovedAmount": 181449.63, "postExecutionTotalBillAmount": 208841.2, "postExectionTotalDeductionAmount": 27391.37, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer room rent"}, {"message": "ICU : Room Rent based on insurer "}, {"message": "Normal Room Rent Lenght of Stay : 1.00"}, {"message": "ICU Room Rent Lenght of Stay : 0"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 1.00"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 9500.0"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 9500.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 275728.7, "preExectionTotalDeductionAmount": 27391.37, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 275728.7, "postExectionTotalDeductionAmount": 27391.37, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}], "_cleaning_metadata": {"fields_removed": ["logCreatedDate (from logs)", "data (from logs)", "timeOfOccurrence (from data entries and rule logs)", "loggerType (from data entries and rule logs)", "configRuleLogObject (from rule engine logs)", "matchingConfig (from rule engine logs)"], "fields_renamed": ["logMessages → reasons (in rule engine logs)"], "filtering_applied": ["Only rule engine logs with non-empty matchingConfig arrays are included", "Only rule engine logs with non-zero postExecutionTotalBillAmount are included"], "grouping_applied": ["Rule engine logs grouped by category as dictionary structure with metadata support"], "cleaning_timestamp": "2025-06-13T21:40:51.479059", "purpose": "Remove unnecessary fields, rename for clarity, filter for relevant rule logs, and group by category"}, "_processing_metadata": {"filtered_for_claim_logs_only": true, "processing_timestamp": "2025-06-13T21:40:51.544941", "total_logs": 3, "claim_logs_count": 3}}, {"claimNo": 127700847, "logs": [{"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "preExecutionApprovedAmount": 144204.34, "preExecutionTotalBillAmount": 163672.97, "preExectionTotalDeductionAmount": 19468.66, "postExecutionApprovedAmount": 144204.34, "postExecutionTotalBillAmount": 163672.97, "postExectionTotalDeductionAmount": 19468.66, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "preExecutionApprovedAmount": 144204.34, "preExecutionTotalBillAmount": 163672.97, "preExectionTotalDeductionAmount": 19468.66, "postExecutionApprovedAmount": 144204.34, "postExecutionTotalBillAmount": 163672.97, "postExectionTotalDeductionAmount": 19468.66, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 144204.34 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 144204.34 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 211364.07, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 19468.66"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 19468.66"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Consultant Charges: 5349.00,Investigation & Lab Charges: 6449.00,Miscellaneous Charges: 2855.91,Pharmacy & Medicine Charges: 714.75,Hospital Charges: 4100.00"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 195172.97, "preExectionTotalDeductionAmount": 19468.66, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 163672.97, "postExectionTotalDeductionAmount": 19468.66, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9500.0"}, {"message": "Tariff ICU Room Rent From Master:9500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 31500.000"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 144204.34, "preExecutionTotalBillAmount": 163672.97, "preExectionTotalDeductionAmount": 19468.66, "postExecutionApprovedAmount": 144204.34, "postExecutionTotalBillAmount": 163672.97, "postExectionTotalDeductionAmount": 19468.66, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer room rent"}, {"message": "ICU : Room Rent based on insurer "}, {"message": "Normal Room Rent Lenght of Stay : 1.00"}, {"message": "ICU Room Rent Lenght of Stay : 0"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 1.00"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 9500.0"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 9500.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 195172.97, "preExectionTotalDeductionAmount": 19468.66, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 195172.97, "postExectionTotalDeductionAmount": 19468.66, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}], "_cleaning_metadata": {"fields_removed": ["logCreatedDate (from logs)", "data (from logs)", "timeOfOccurrence (from data entries and rule logs)", "loggerType (from data entries and rule logs)", "configRuleLogObject (from rule engine logs)", "matchingConfig (from rule engine logs)"], "fields_renamed": ["logMessages → reasons (in rule engine logs)"], "filtering_applied": ["Only rule engine logs with non-empty matchingConfig arrays are included", "Only rule engine logs with non-zero postExecutionTotalBillAmount are included"], "grouping_applied": ["Rule engine logs grouped by category as dictionary structure with metadata support"], "cleaning_timestamp": "2025-06-13T21:40:51.487899", "purpose": "Remove unnecessary fields, rename for clarity, filter for relevant rule logs, and group by category"}, "_processing_metadata": {"filtered_for_claim_logs_only": true, "processing_timestamp": "2025-06-13T21:40:51.544944", "total_logs": 1, "claim_logs_count": 1}}, {"claimNo": 127669234, "logs": [{"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "preExecutionApprovedAmount": 111214.85, "preExecutionTotalBillAmount": 125826.25, "preExectionTotalDeductionAmount": 14611.15, "postExecutionApprovedAmount": 111214.85, "postExecutionTotalBillAmount": 125826.25, "postExectionTotalDeductionAmount": 14611.15, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "preExecutionApprovedAmount": 111214.85, "preExecutionTotalBillAmount": 125826.25, "preExectionTotalDeductionAmount": 14611.15, "postExecutionApprovedAmount": 111214.85, "postExecutionTotalBillAmount": 125826.25, "postExectionTotalDeductionAmount": 14611.15, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 111214.85 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 111214.85 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 161349.25, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 14611.15"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 14611.15"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Consultant Charges: 3799.00,Investigation & Lab Charges: 5616.00,Miscellaneous Charges: 1393.10,Pharmacy & Medicine Charges: 653.05,Hospital Charges: 3150.00"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 150826.25, "preExectionTotalDeductionAmount": 14611.15, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 125826.25, "postExectionTotalDeductionAmount": 14611.15, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SemiPrivateRoom"}, {"message": "Tariff Room Rent From Master:6500.0"}, {"message": "Tariff ICU Room Rent From Master:6500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 25000.000"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 111214.85, "preExecutionTotalBillAmount": 125826.25, "preExectionTotalDeductionAmount": 14611.15, "postExecutionApprovedAmount": 111214.85, "postExecutionTotalBillAmount": 125826.25, "postExectionTotalDeductionAmount": 14611.15, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SemiPrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer room rent"}, {"message": "ICU : Room Rent based on insurer "}, {"message": "Normal Room Rent Lenght of Stay : 1.00"}, {"message": "ICU Room Rent Lenght of Stay : 0"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 1.00"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 6500.0"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 9500.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 150826.25, "preExectionTotalDeductionAmount": 14611.15, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 150826.25, "postExectionTotalDeductionAmount": 14611.15, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}], "_cleaning_metadata": {"fields_removed": ["logCreatedDate (from logs)", "data (from logs)", "timeOfOccurrence (from data entries and rule logs)", "loggerType (from data entries and rule logs)", "configRuleLogObject (from rule engine logs)", "matchingConfig (from rule engine logs)"], "fields_renamed": ["logMessages → reasons (in rule engine logs)"], "filtering_applied": ["Only rule engine logs with non-empty matchingConfig arrays are included", "Only rule engine logs with non-zero postExecutionTotalBillAmount are included"], "grouping_applied": ["Rule engine logs grouped by category as dictionary structure with metadata support"], "cleaning_timestamp": "2025-06-13T21:40:51.496512", "purpose": "Remove unnecessary fields, rename for clarity, filter for relevant rule logs, and group by category"}, "_processing_metadata": {"filtered_for_claim_logs_only": true, "processing_timestamp": "2025-06-13T21:40:51.544946", "total_logs": 1, "claim_logs_count": 1}}, {"claimNo": 127732179, "logs": [{"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "preExecutionApprovedAmount": 151429.19, "preExecutionTotalBillAmount": 172724.74, "preExectionTotalDeductionAmount": 21295.81, "postExecutionApprovedAmount": 151429.19, "postExecutionTotalBillAmount": 172724.74, "postExectionTotalDeductionAmount": 21295.81, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "preExecutionApprovedAmount": 151429.19, "preExecutionTotalBillAmount": 172724.74, "preExectionTotalDeductionAmount": 21295.81, "postExecutionApprovedAmount": 151429.19, "postExecutionTotalBillAmount": 172724.74, "postExectionTotalDeductionAmount": 21295.81, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 151429.19 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 151429.19 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 231190.84, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 21295.81"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 21295.81"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Consultant Charges: 5673.00,Investigation & Lab Charges: 6808.00,Miscellaneous Charges: 2983.41,Pharmacy & Medicine Charges: 781.40,Hospital Charges: 5050.00"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 213724.74, "preExectionTotalDeductionAmount": 21295.81, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 172724.74, "postExectionTotalDeductionAmount": 21295.81, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9500.0"}, {"message": "Tariff ICU Room Rent From Master:9500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 41000.000"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 151429.19, "preExecutionTotalBillAmount": 172724.74, "preExectionTotalDeductionAmount": 21295.81, "postExecutionApprovedAmount": 151429.19, "postExecutionTotalBillAmount": 172724.74, "postExectionTotalDeductionAmount": 21295.81, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer room rent"}, {"message": "ICU : Room Rent based on insurer "}, {"message": "Normal Room Rent Lenght of Stay : 1.00"}, {"message": "ICU Room Rent Lenght of Stay : 0"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 1.00"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 9500.0"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 9500.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 213724.74, "preExectionTotalDeductionAmount": 21295.81, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 213724.74, "postExectionTotalDeductionAmount": 21295.81, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}], "_cleaning_metadata": {"fields_removed": ["logCreatedDate (from logs)", "data (from logs)", "timeOfOccurrence (from data entries and rule logs)", "loggerType (from data entries and rule logs)", "configRuleLogObject (from rule engine logs)", "matchingConfig (from rule engine logs)"], "fields_renamed": ["logMessages → reasons (in rule engine logs)"], "filtering_applied": ["Only rule engine logs with non-empty matchingConfig arrays are included", "Only rule engine logs with non-zero postExecutionTotalBillAmount are included"], "grouping_applied": ["Rule engine logs grouped by category as dictionary structure with metadata support"], "cleaning_timestamp": "2025-06-13T21:40:51.505167", "purpose": "Remove unnecessary fields, rename for clarity, filter for relevant rule logs, and group by category"}, "_processing_metadata": {"filtered_for_claim_logs_only": true, "processing_timestamp": "2025-06-13T21:40:51.544948", "total_logs": 1, "claim_logs_count": 1}}, {"claimNo": 127629175, "logs": [{"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "preExecutionApprovedAmount": 66935.0, "preExecutionTotalBillAmount": 79105.0, "preExectionTotalDeductionAmount": 12170.0, "postExecutionApprovedAmount": 66935.0, "postExecutionTotalBillAmount": 79105.0, "postExectionTotalDeductionAmount": 12170.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "preExecutionApprovedAmount": 66935.0, "preExecutionTotalBillAmount": 79105.0, "preExectionTotalDeductionAmount": 12170.0, "postExecutionApprovedAmount": 66935.0, "postExecutionTotalBillAmount": 79105.0, "postExectionTotalDeductionAmount": 12170.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 66935.00 and flat amount 12500 is 8366.875"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 8366.875"}, {"message": "Copay deduction amount is 8366.875"}, {"message": "Copay deduction amount is: 8366.875"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 66935.00 and flat amount 12500 is 8366.875 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 121700.0, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 12170.00"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 12170.00"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Package: 12170.00"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "NonMedicalExpense": {"rules": [{"category": "NonMedicalExpense", "subCategory": "HandelNMEByPreAuthType", "subCategoryDisplayName": "Estimate For Initial", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 121700.0, "preExectionTotalDeductionAmount": 12170.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 79105.0, "postExectionTotalDeductionAmount": 12170.0, "reasons": [{"message": "*****Calulating CalculateNMEDeduction Started*****"}, {"message": "Matching Config Count: 1"}, {"message": "Estimate For Initial - Execution starts"}, {"message": "CalculateNMEDeduction isInitialPA: TRUE"}, {"message": "NMEDeduction percentage: 35"}, {"message": "*****Calulating CalculateNMEDeduction End*****"}, {"message": "Estimate For Initial - Execution ends"}]}], "count": 1, "category_name": "NonMedicalExpense"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 79105.0, "preExectionTotalDeductionAmount": 12170.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 79105.0, "postExectionTotalDeductionAmount": 12170.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :ICU"}, {"message": "Tariff ICU Room Rent From Master:0"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 66935.0, "preExecutionTotalBillAmount": 79105.0, "preExectionTotalDeductionAmount": 12170.0, "postExecutionApprovedAmount": 66935.0, "postExecutionTotalBillAmount": 79105.0, "postExectionTotalDeductionAmount": 12170.0, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category ICU"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer "}, {"message": "ICU : Room Rent based on insurer "}, {"message": "Due to Initial PA LengthOfStay calculated on DOD - DOA "}, {"message": "Normal Room Rent Lenght of Stay : 1"}, {"message": "ICU Room Rent Lenght of Stay : 1"}, {"message": "Tariff Bill Price as per insurer :0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Due to Initial PA LengthOfStay calculated on DOD - DOA "}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 1"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 1"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 9500.0"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 79105.0, "preExectionTotalDeductionAmount": 12170.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 79105.0, "postExectionTotalDeductionAmount": 12170.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}], "_cleaning_metadata": {"fields_removed": ["logCreatedDate (from logs)", "data (from logs)", "timeOfOccurrence (from data entries and rule logs)", "loggerType (from data entries and rule logs)", "configRuleLogObject (from rule engine logs)", "matchingConfig (from rule engine logs)"], "fields_renamed": ["logMessages → reasons (in rule engine logs)"], "filtering_applied": ["Only rule engine logs with non-empty matchingConfig arrays are included", "Only rule engine logs with non-zero postExecutionTotalBillAmount are included"], "grouping_applied": ["Rule engine logs grouped by category as dictionary structure with metadata support"], "cleaning_timestamp": "2025-06-13T21:40:51.514027", "purpose": "Remove unnecessary fields, rename for clarity, filter for relevant rule logs, and group by category"}, "_processing_metadata": {"filtered_for_claim_logs_only": true, "processing_timestamp": "2025-06-13T21:40:51.544950", "total_logs": 1, "claim_logs_count": 1}}, {"claimNo": 127629176, "logs": [{"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "preExecutionApprovedAmount": 252799.1, "preExecutionTotalBillAmount": 278358.45, "preExectionTotalDeductionAmount": 25558.9, "postExecutionApprovedAmount": 252799.1, "postExecutionTotalBillAmount": 278358.45, "postExectionTotalDeductionAmount": 25558.9, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Copay applicable by: PecentageOfAdmissibleAmount on each claim basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "10% of copay applied on 252799.10 and capped value is 25279.91"}, {"message": "Copay applicable based on each claim basis"}, {"message": "Copay value: 25279.91"}, {"message": "Copay deduction amount is 25279.91"}, {"message": "Copay deduction amount is: 25279.91"}, {"message": "Copay Deduction Remarks: 10% of copay applied on 252799.10 and capped value is 25279.91 (Intimation Copay)"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "preExecutionApprovedAmount": 227519.19, "preExecutionTotalBillAmount": 278358.45, "preExectionTotalDeductionAmount": 50838.81, "postExecutionApprovedAmount": 227519.19, "postExecutionTotalBillAmount": 278358.45, "postExectionTotalDeductionAmount": 50838.81, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 227519.19 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 227519.19 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 294658.45, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 26231.87"}, {"message": "HMOU Discount Calculated: 25559"}, {"message": "MAX Discount Calculated: 26231.87"}, {"message": "Discount Calculation Is Goving with HMOU"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Hospital Charges: 6839.50,Investigation & Lab Charges: 6665.13,Miscellaneous Charges: 1197.01,Consultant Charges: 6849.76,Pharmacy & Medicine Charges: 4007.50"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 281358.45, "preExectionTotalDeductionAmount": 25558.9, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 278358.45, "postExectionTotalDeductionAmount": 25558.9, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9500.0"}, {"message": "Tariff ICU Room Rent From Master:9500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.000"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 252799.1, "preExecutionTotalBillAmount": 278358.45, "preExectionTotalDeductionAmount": 25558.9, "postExecutionApprovedAmount": 252799.1, "postExecutionTotalBillAmount": 278358.45, "postExectionTotalDeductionAmount": 25558.9, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer nursing,room rent"}, {"message": "ICU : Room Rent based on insurer icu charges,icu monitoring charges"}, {"message": "Normal Room Rent Lenght of Stay : 5.50"}, {"message": "ICU Room Rent Lenght of Stay : 2.00"}, {"message": "Nursing Charges PerDay : 0.0"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 2.00"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 11800"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 5.50"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 9499.99"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 52250.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 23600.00"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 281358.45, "preExectionTotalDeductionAmount": 25558.9, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 281358.45, "postExectionTotalDeductionAmount": 25558.9, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}, {"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "preExecutionApprovedAmount": 252799.1, "preExecutionTotalBillAmount": 278358.45, "preExectionTotalDeductionAmount": 25558.9, "postExecutionApprovedAmount": 252799.1, "postExecutionTotalBillAmount": 278358.45, "postExectionTotalDeductionAmount": 25558.9, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Copay applicable by: PecentageOfAdmissibleAmount on each claim basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "10% of copay applied on 252799.10 and capped value is 25279.91"}, {"message": "Copay applicable based on each claim basis"}, {"message": "Copay value: 25279.91"}, {"message": "Copay deduction amount is 25279.91"}, {"message": "Copay deduction amount is: 25279.91"}, {"message": "Copay Deduction Remarks: 10% of copay applied on 252799.10 and capped value is 25279.91 (Intimation Copay)"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "preExecutionApprovedAmount": 227519.19, "preExecutionTotalBillAmount": 278358.45, "preExectionTotalDeductionAmount": 50838.81, "postExecutionApprovedAmount": 227519.19, "postExecutionTotalBillAmount": 278358.45, "postExectionTotalDeductionAmount": 50838.81, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 227519.19 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 227519.19 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 294658.45, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 26231.87"}, {"message": "HMOU Discount Calculated: 25559"}, {"message": "MAX Discount Calculated: 26231.87"}, {"message": "Discount Calculation Is Goving with HMOU"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Hospital Charges: 6839.50,Investigation & Lab Charges: 6665.13,Miscellaneous Charges: 1197.01,Consultant Charges: 6849.76,Pharmacy & Medicine Charges: 4007.50"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 281358.45, "preExectionTotalDeductionAmount": 25558.9, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 278358.45, "postExectionTotalDeductionAmount": 25558.9, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9500.0"}, {"message": "Tariff ICU Room Rent From Master:9500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.000"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 252799.1, "preExecutionTotalBillAmount": 278358.45, "preExectionTotalDeductionAmount": 25558.9, "postExecutionApprovedAmount": 252799.1, "postExecutionTotalBillAmount": 278358.45, "postExectionTotalDeductionAmount": 25558.9, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer nursing,room rent"}, {"message": "ICU : Room Rent based on insurer icu charges,icu monitoring charges"}, {"message": "Normal Room Rent Lenght of Stay : 5.50"}, {"message": "ICU Room Rent Lenght of Stay : 2.00"}, {"message": "Nursing Charges PerDay : 0.0"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 2.00"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 11800"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 5.50"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 9499.99"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 52250.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 23600.00"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 281358.45, "preExectionTotalDeductionAmount": 25558.9, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 281358.45, "postExectionTotalDeductionAmount": 25558.9, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}], "_cleaning_metadata": {"fields_removed": ["logCreatedDate (from logs)", "data (from logs)", "timeOfOccurrence (from data entries and rule logs)", "loggerType (from data entries and rule logs)", "configRuleLogObject (from rule engine logs)", "matchingConfig (from rule engine logs)"], "fields_renamed": ["logMessages → reasons (in rule engine logs)"], "filtering_applied": ["Only rule engine logs with non-empty matchingConfig arrays are included", "Only rule engine logs with non-zero postExecutionTotalBillAmount are included"], "grouping_applied": ["Rule engine logs grouped by category as dictionary structure with metadata support"], "cleaning_timestamp": "2025-06-13T21:40:51.533011", "purpose": "Remove unnecessary fields, rename for clarity, filter for relevant rule logs, and group by category"}, "_processing_metadata": {"filtered_for_claim_logs_only": true, "processing_timestamp": "2025-06-13T21:40:51.544953", "total_logs": 2, "claim_logs_count": 2}}]