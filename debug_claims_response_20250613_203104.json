[{"_debug_info": {"total_claims": 1, "filtering_rule": "Only logs with typeOfLog: \"Claim\" will be processed", "alteration_timestamp": "2025-06-13T20:31:04.649635"}}, {"claimNo": 131420765, "logs": [{"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 145307.6, "postExectionTotalDeductionAmount": 9402.04, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 8905.18"}, {"message": "HMOU Discount Calculated: 9402.08"}, {"message": "MAX Discount Calculated: 9402.08"}, {"message": "Discount Calculation Is Goving with HMOU"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Pharmacy & Medicine Charges: 2760.22,Hospital Charges: 1827.11,Consultant Charges: 1299.90,Surgery Charges: 94.74,Investigation & Lab Charges: 2460.50,Miscellaneous Charges: 959.57"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}], "_deduction_calculation": {"original_value": 0.0, "next_value": 9402.04, "incremental_difference": 9402.04, "calculation_method": "next_minus_current", "position_in_sequence": 1, "total_rules_in_sequence": 8}}, {"category": "NonMedicalExpense", "subCategory": "OverrideNMEParticularsAmount", "subCategoryDisplayName": "NME limit by Subcharge", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 119565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Limit applicable subcharge head: 1"}, {"message": "Matching Config Count: 1"}, {"message": "Matching Config Count: 1"}, {"message": "NME limit by Subcharge - Execution starts"}, {"message": "NME limit by Subcharge - Execution ends"}], "_deduction_calculation": {"original_value": 9402.04, "next_value": 9402.04, "incremental_difference": 0.0, "calculation_method": "next_minus_current", "position_in_sequence": 2, "total_rules_in_sequence": 8}}, {"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 119565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}], "_deduction_calculation": {"original_value": 9402.04, "next_value": 9402.04, "incremental_difference": 0.0, "calculation_method": "next_minus_current", "position_in_sequence": 3, "total_rules_in_sequence": 8}}, {"category": "Repudiation", "subCategory": "RepudiationByHospital", "subCategoryDisplayName": "Repudiation By Hospital And Claim Type", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 119565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Matching Config Count: 1"}, {"message": "Repudiation By Hospital And Claim Type - Execution starts"}, {"message": "Repudiation By Hospital And Claim Type - Execution ends"}], "_deduction_calculation": {"original_value": 9402.04, "next_value": 9402.04, "incremental_difference": 0.0, "calculation_method": "next_minus_current", "position_in_sequence": 4, "total_rules_in_sequence": 8}}, {"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 119565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 2"}, {"message": "Matching Config Count: 2"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:11100.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:6500.0"}, {"message": "Tariff ICU Room Rent From Master:6500.0"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}], "_deduction_calculation": {"original_value": 9402.04, "next_value": 9402.04, "incremental_difference": 0.0, "calculation_method": "next_minus_current", "position_in_sequence": 5, "total_rules_in_sequence": 8}}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 110163.96, "preExecutionTotalBillAmount": 119565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 110163.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category SemiPvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:11100.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category Found Under Sharing Category"}, {"message": "Found room rent details under SHARING Category"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category not found Under PRIVATE Category"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer nursing,room,room rent,room rent (per day),associated room charges,duty doctor,injection / iv administration charge,gst on room rent"}, {"message": "ICU : Room Rent based on insurer icu charges,icu nursing charges,icu room rent,icu injection / iv administration charge,intencivist charges,icu monitoring charges"}, {"message": "Normal Room Rent Lenght of Stay : 4.00"}, {"message": "ICU Room Rent Lenght of Stay : 0"}, {"message": "Non-Tariff bill as insurer Perday ChargeType :gst on room rent , Units : 4.00, PerDay Price : 604"}, {"message": "Non-Tariff bill as insurer Perday ChargeType :gst on room rent , Units : 4.00, PerDay Price : 604"}, {"message": "Tariff Bill Price as per insurer :5500.0"}, {"message": "Non-Tariff bill Price as per insurer :302"}, {"message": "Total Room Price as per insurer :5802.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 5802.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 5802.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 5802.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 4.00"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 6802.26"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 23208.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 85.30%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 85.30%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}], "_deduction_calculation": {"original_value": 9402.04, "next_value": 9402.04, "incremental_difference": 0.0, "calculation_method": "next_minus_current", "position_in_sequence": 6, "total_rules_in_sequence": 8}}, {"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "preExecutionApprovedAmount": 103439.96, "preExecutionTotalBillAmount": 112842.58, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 103439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}], "_deduction_calculation": {"original_value": 9402.04, "next_value": 9402.04, "incremental_difference": 0.0, "calculation_method": "next_minus_current", "position_in_sequence": 7, "total_rules_in_sequence": 8}}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "preExecutionApprovedAmount": 103439.96, "preExecutionTotalBillAmount": 112842.58, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 103439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 9402.04, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 10%  of admissible amount 103439.96 and flat amount 10000 is 10000"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 10000"}, {"message": "Copay deduction amount is 10000"}, {"message": "Copay deduction amount is: 10000"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 10%  of admissible amount 103439.96 and flat amount 10000 is 10000 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}], "_deduction_calculation": {"original_value": 9402.04, "next_value": null, "incremental_difference": 9402.04, "calculation_method": "final_value", "position_in_sequence": 8, "total_rules_in_sequence": 8}}], "_will_be_processed": true}, {"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 145307.6, "postExectionTotalDeductionAmount": 9402.04, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 8905.18"}, {"message": "HMOU Discount Calculated: 9402.08"}, {"message": "MAX Discount Calculated: 9402.08"}, {"message": "Discount Calculation Is Goving with HMOU"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Pharmacy & Medicine Charges: 2760.22,Hospital Charges: 1827.11,Consultant Charges: 1299.90,Surgery Charges: 94.74,Investigation & Lab Charges: 2460.50,Miscellaneous Charges: 959.57"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}], "_deduction_calculation": {"original_value": 0.0, "next_value": 9402.04, "incremental_difference": 9402.04, "calculation_method": "next_minus_current", "position_in_sequence": 1, "total_rules_in_sequence": 8}}, {"category": "NonMedicalExpense", "subCategory": "OverrideNMEParticularsAmount", "subCategoryDisplayName": "NME limit by Subcharge", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 119565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Limit applicable subcharge head: 1"}, {"message": "Matching Config Count: 1"}, {"message": "Matching Config Count: 1"}, {"message": "NME limit by Subcharge - Execution starts"}, {"message": "NME limit by Subcharge - Execution ends"}], "_deduction_calculation": {"original_value": 9402.04, "next_value": 9402.04, "incremental_difference": 0.0, "calculation_method": "next_minus_current", "position_in_sequence": 2, "total_rules_in_sequence": 8}}, {"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 119565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}], "_deduction_calculation": {"original_value": 9402.04, "next_value": 9402.04, "incremental_difference": 0.0, "calculation_method": "next_minus_current", "position_in_sequence": 3, "total_rules_in_sequence": 8}}, {"category": "Repudiation", "subCategory": "RepudiationByHospital", "subCategoryDisplayName": "Repudiation By Hospital And Claim Type", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 119565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Matching Config Count: 1"}, {"message": "Repudiation By Hospital And Claim Type - Execution starts"}, {"message": "Repudiation By Hospital And Claim Type - Execution ends"}], "_deduction_calculation": {"original_value": 9402.04, "next_value": 9402.04, "incremental_difference": 0.0, "calculation_method": "next_minus_current", "position_in_sequence": 4, "total_rules_in_sequence": 8}}, {"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 119565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 2"}, {"message": "Matching Config Count: 2"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:11100.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SemiPrivateRoom"}, {"message": "Tariff Room Rent From Master:5500.0"}, {"message": "Tariff ICU Room Rent From Master:5500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 4000.000"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}], "_deduction_calculation": {"original_value": 9402.04, "next_value": 9402.04, "incremental_difference": 0.0, "calculation_method": "next_minus_current", "position_in_sequence": 5, "total_rules_in_sequence": 8}}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 106163.96, "preExecutionTotalBillAmount": 115565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 106163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category SemiPvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SemiPrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:11100.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category Found Under Sharing Category"}, {"message": "Found room rent details under SHARING Category"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category not found Under PRIVATE Category"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer nursing,room,room rent,room rent (per day),associated room charges,duty doctor,injection / iv administration charge,gst on room rent"}, {"message": "ICU : Room Rent based on insurer icu charges,icu nursing charges,icu room rent,icu injection / iv administration charge,intencivist charges,icu monitoring charges"}, {"message": "Normal Room Rent Lenght of Stay : 4.00"}, {"message": "ICU Room Rent Lenght of Stay : 0"}, {"message": "Non-Tariff bill as insurer Perday ChargeType :gst on room rent , Units : 4.00, PerDay Price : 604"}, {"message": "Non-Tariff bill as insurer Perday ChargeType :gst on room rent , Units : 4.00, PerDay Price : 604"}, {"message": "Tariff Bill Price as per insurer :5500.0"}, {"message": "Non-Tariff bill Price as per insurer :302"}, {"message": "Total Room Price as per insurer :5802.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 5802.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 5802.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 5802.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 4.00"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 5802.26"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 23208.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}], "_deduction_calculation": {"original_value": 9402.04, "next_value": 9402.04, "incremental_difference": 0.0, "calculation_method": "next_minus_current", "position_in_sequence": 6, "total_rules_in_sequence": 8}}, {"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "preExecutionApprovedAmount": 106163.96, "preExecutionTotalBillAmount": 115565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 106163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}], "_deduction_calculation": {"original_value": 9402.04, "next_value": 9402.04, "incremental_difference": 0.0, "calculation_method": "next_minus_current", "position_in_sequence": 7, "total_rules_in_sequence": 8}}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "preExecutionApprovedAmount": 106163.96, "preExecutionTotalBillAmount": 115565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 106163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 9402.04, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 10%  of admissible amount 106163.96 and flat amount 10000 is 10000"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 10000"}, {"message": "Copay deduction amount is 10000"}, {"message": "Copay deduction amount is: 10000"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 10%  of admissible amount 106163.96 and flat amount 10000 is 10000 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}], "_deduction_calculation": {"original_value": 9402.04, "next_value": null, "incremental_difference": 9402.04, "calculation_method": "final_value", "position_in_sequence": 8, "total_rules_in_sequence": 8}}], "_will_be_processed": true}], "_cleaning_metadata": {"fields_removed": ["logCreatedDate (from logs)", "data (from logs)", "timeOfOccurrence (from data entries and rule logs)", "loggerType (from data entries and rule logs)", "configRuleLogObject (from rule engine logs)", "matchingConfig (from rule engine logs)"], "fields_renamed": ["logMessages → reasons (in rule engine logs)"], "filtering_applied": ["Only rule engine logs with non-empty matchingConfig arrays are included"], "calculations_applied": ["postExectionTotalDeductionAmount converted to incremental differences (next - current)"], "cleaning_timestamp": "2025-06-13T20:31:04.647720", "purpose": "Remove unnecessary fields, rename for clarity, filter for relevant rule logs, and calculate incremental deductions"}, "_processing_metadata": {"filtered_for_claim_logs_only": true, "processing_timestamp": "2025-06-13T20:31:04.649598", "total_logs": 2, "claim_logs_count": 2}}]