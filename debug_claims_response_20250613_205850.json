[{"_debug_info": {"total_claims": 1, "filtering_rule": "Only logs with typeOfLog: \"Claim\" will be processed", "alteration_timestamp": "2025-06-13T20:58:50.637640"}}, {"claimNo": 43040734, "logs": [{"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": [{"category": "BaseConfiguration", "subCategory": "DefautChecks", "subCategoryDisplayName": "Default Configuration", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 0.0, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Default Configuration - Execution starts"}, {"message": "Claim types considered for discount override: PreHosp,PostHosp,HospBenefit,HealthCheckUp,RI,ReOpenClaim"}, {"message": "Override Discount By <PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON> not executed as no matching rule found."}, {"message": "Default Configuration - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 0.0, "next_bill_amount": 0.0, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 1, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 0.0 to 0.0"}}, {"category": "BaseConfiguration", "subCategory": "OverideRoomRentHead", "subCategoryDisplayName": "Override Room Rent Inclusion", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 0.0, "postExectionTotalDeductionAmount": -112621.8, "reasons": [{"message": "*****Overide Room category Started*****"}, {"message": "Matching Config Count: 1"}, {"message": "Override Room Rent Inclusion - Execution starts"}, {"message": "ProportinateType:Normal"}, {"message": "*****Overide Room category Ended*****"}, {"message": "Override Room Rent Inclusion - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 0.0, "next_bill_amount": 112621.8, "bill_reduction": -112621.8, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 2, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 0.0 to 112621.8"}}, {"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 112621.8, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 3722.10"}, {"message": "HMOU Discount Calculated: 1433"}, {"message": "MAX Discount Calculated: 3722.10"}, {"message": "Discount Calculation Is Goving with HMOU"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Investigation & Lab Charges: 420.03,Miscellaneous Charges: 916.71,Consultant Charges: 96.25"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 112621.8, "next_bill_amount": 112621.8, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 3, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 112621.8 to 112621.8"}}, {"category": "NonMedicalExpense", "subCategory": "MarkAllNMEsAsPayable", "subCategoryDisplayName": "Pay All NMEs", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 111711.8, "preExectionTotalDeductionAmount": 1432.99, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 112621.8, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Pay All NMEs - Execution starts"}, {"message": "Overide Bills Based On ConfigType:Particulars PayableSubType:bio-medical waste fee, OverideNMEByPercenatage MaxPayable Percentage:100"}, {"message": "NME Deduction type: "}, {"message": "Calculated Payable Amt: 200.00"}, {"message": "Calculated Non-Payable Amt: 0"}, {"message": "Overide Bills Based On ConfigType:Particulars PayableSubType:nutritional and functional assessment charges, OverideNMEByPercenatage MaxPayable Percentage:100"}, {"message": "NME Deduction type: "}, {"message": "Calculated Payable Amt: 710.00"}, {"message": "Calculated Non-Payable Amt: 0"}, {"message": "Pay All NMEs - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 112621.8, "next_bill_amount": 112621.8, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 4, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 112621.8 to 112621.8"}}, {"category": "NonMedicalExpense", "subCategory": "OverideNMEByParticulars", "subCategoryDisplayName": "Override NME by Particulars", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 112621.8, "preExectionTotalDeductionAmount": 1432.99, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 112621.8, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Override NME by Particulars - Execution starts"}, {"message": "NME categories overridden to allow in the claim: SURCHARGES,DOCUMENTATION CHARGES / ADMINISTRATIVE EXPENSES,Admission/Registration Charges,Service Charges Where Nursing Charge Also Charged"}, {"message": "Total Payble NME Particulars to Override 4"}, {"message": "Override NME by Particulars - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 112621.8, "next_bill_amount": 112621.8, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 5, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 112621.8 to 112621.8"}}, {"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 112621.8, "preExectionTotalDeductionAmount": 1432.99, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 112621.8, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:4800.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:11800.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:7200.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiDeluxeRoom ,Room Rent PerDay:18700.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3200.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:34100.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:20900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9400.0"}, {"message": "Tariff ICU Room Rent From Master:9400.0"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Excess Of Traiff Package : Opted Room Category : PRIVATE"}, {"message": "Package Id : 1829550"}, {"message": "Package Master Details : Package Id : 1829550 General:61900,Sharing:65000,Private:74800,Deluxe:80800"}, {"message": "DIALYSIS COUNT :0"}, {"message": "Relevant Quantity :1"}, {"message": "Package Amount :74800"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 112621.8, "next_bill_amount": 112621.8, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 6, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 112621.8 to 112621.8"}}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 111189.01, "preExecutionTotalBillAmount": 112621.8, "preExectionTotalDeductionAmount": 1432.99, "postExecutionApprovedAmount": 111189.01, "postExecutionTotalBillAmount": 112621.8, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:4800.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:11800.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:7200.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiDeluxeRoom ,Room Rent PerDay:18700.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3200.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:34100.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:20900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:2"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer nursing,room,room rent,room rent (per day),associated room charges,duty doctor,injection / iv administration charge,gst on room rent"}, {"message": "ICU : Room Rent based on insurer icu charges,icu nursing charges,icu room rent,icu injection / iv administration charge,intencivist charges,icu monitoring charges"}, {"message": "Normal Room Rent Lenght of Stay : 0"}, {"message": "ICU Room Rent Lenght of Stay : 0"}, {"message": "Tariff Bill Price as per insurer :8900.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :8900.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 8900.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 8900.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Excess Of Eligible Package : Eligible Room Category : PRIVATE"}, {"message": "Package Id : 1829550"}, {"message": "Package Master Details : Package Id : 1829550 General:61900,Sharing:65000,Private:74800,Deluxe:80800"}, {"message": "DIALYSIS COUNT :0"}, {"message": "Relevant Quantity :1"}, {"message": "Package Amount :74800"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 8900.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 0.0"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 0%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 112621.8, "next_bill_amount": null, "bill_reduction": 0.0, "calculation_method": "final_rule_no_reduction", "position_in_sequence": 7, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 112621.8 to final"}}], "_will_be_processed": true}, {"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": [{"category": "BaseConfiguration", "subCategory": "DefautChecks", "subCategoryDisplayName": "Default Configuration", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 0.0, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Default Configuration - Execution starts"}, {"message": "Claim types considered for discount override: PreHosp,PostHosp,HospBenefit,HealthCheckUp,RI,ReOpenClaim"}, {"message": "Override Discount By <PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON> not executed as no matching rule found."}, {"message": "Default Configuration - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 0.0, "next_bill_amount": 0.0, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 1, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 0.0 to 0.0"}}, {"category": "BaseConfiguration", "subCategory": "OverideRoomRentHead", "subCategoryDisplayName": "Override Room Rent Inclusion", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 0.0, "postExectionTotalDeductionAmount": -112621.8, "reasons": [{"message": "*****Overide Room category Started*****"}, {"message": "Matching Config Count: 1"}, {"message": "Override Room Rent Inclusion - Execution starts"}, {"message": "ProportinateType:Normal"}, {"message": "*****Overide Room category Ended*****"}, {"message": "Override Room Rent Inclusion - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 0.0, "next_bill_amount": 112621.8, "bill_reduction": -112621.8, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 2, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 0.0 to 112621.8"}}, {"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 112621.8, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 2633.60"}, {"message": "HMOU Discount Calculated: 1433"}, {"message": "MAX Discount Calculated: 2633.60"}, {"message": "Discount Calculation Is Goving with HMOU"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Investigation & Lab Charges: 593.64,Miscellaneous Charges: 703.33,Consultant Charges: 136.03"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 112621.8, "next_bill_amount": 112621.8, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 3, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 112621.8 to 112621.8"}}, {"category": "NonMedicalExpense", "subCategory": "MarkAllNMEsAsPayable", "subCategoryDisplayName": "Pay All NMEs", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 111711.8, "preExectionTotalDeductionAmount": 1433.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 112621.8, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Pay All NMEs - Execution starts"}, {"message": "Overide Bills Based On ConfigType:Particulars PayableSubType:bio-medical waste fee, OverideNMEByPercenatage MaxPayable Percentage:100"}, {"message": "NME Deduction type: "}, {"message": "Calculated Payable Amt: 200.00"}, {"message": "Calculated Non-Payable Amt: 0"}, {"message": "Overide Bills Based On ConfigType:Particulars PayableSubType:nutritional and functional assessment charges, OverideNMEByPercenatage MaxPayable Percentage:100"}, {"message": "NME Deduction type: "}, {"message": "Calculated Payable Amt: 710.00"}, {"message": "Calculated Non-Payable Amt: 0"}, {"message": "Pay All NMEs - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 112621.8, "next_bill_amount": 112621.8, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 4, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 112621.8 to 112621.8"}}, {"category": "NonMedicalExpense", "subCategory": "OverideNMEByParticulars", "subCategoryDisplayName": "Override NME by Particulars", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 112621.8, "preExectionTotalDeductionAmount": 1433.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 112621.8, "postExectionTotalDeductionAmount": 10885.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Override NME by Particulars - Execution starts"}, {"message": "NME categories overridden to allow in the claim: SURCHARGES,DOCUMENTATION CHARGES / ADMINISTRATIVE EXPENSES,Admission/Registration Charges,Service Charges Where Nursing Charge Also Charged"}, {"message": "Total Payble NME Particulars to Override 4"}, {"message": "Override NME by Particulars - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 112621.8, "next_bill_amount": 101736.8, "bill_reduction": 10885.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 5, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 112621.8 to 101736.8"}}, {"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 112621.8, "preExectionTotalDeductionAmount": 1433.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 101736.8, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:4800.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:11800.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:7200.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiDeluxeRoom ,Room Rent PerDay:18700.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3200.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:34100.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:20900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9400.0"}, {"message": "Tariff ICU Room Rent From Master:9400.0"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Excess Of Traiff Package : Opted Room Category : PRIVATE"}, {"message": "Package Id : 1829550"}, {"message": "Package Master Details : Package Id : 1829550 General:61900,Sharing:65000,Private:74800,Deluxe:80800"}, {"message": "DIALYSIS COUNT :0"}, {"message": "Relevant Quantity :1"}, {"message": "Package Amount :74800"}, {"message": "Excess Of Tariff Package Calculated ; Package Amount: 85685.00 , Contracted Package Amount: 74800 "}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 101736.8, "next_bill_amount": 101736.8, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 6, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 101736.8 to 101736.8"}}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 100304.0, "preExecutionTotalBillAmount": 101736.8, "preExectionTotalDeductionAmount": 1433.0, "postExecutionApprovedAmount": 100304.0, "postExecutionTotalBillAmount": 101736.8, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:4800.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:11800.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:7200.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiDeluxeRoom ,Room Rent PerDay:18700.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3200.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:34100.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:20900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:2"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer nursing,room,room rent,room rent (per day),associated room charges,duty doctor,injection / iv administration charge,gst on room rent"}, {"message": "ICU : Room Rent based on insurer icu charges,icu nursing charges,icu room rent,icu injection / iv administration charge,intencivist charges,icu monitoring charges"}, {"message": "Normal Room Rent Lenght of Stay : 0"}, {"message": "ICU Room Rent Lenght of Stay : 0"}, {"message": "Tariff Bill Price as per insurer :8900.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :8900.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 8900.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 8900.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Excess Of Eligible Package : Eligible Room Category : PRIVATE"}, {"message": "Package Id : 1829550"}, {"message": "Package Master Details : Package Id : 1829550 General:61900,Sharing:65000,Private:74800,Deluxe:80800"}, {"message": "DIALYSIS COUNT :0"}, {"message": "Relevant Quantity :1"}, {"message": "Package Amount :74800"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 8900.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 0.0"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 0%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 101736.8, "next_bill_amount": null, "bill_reduction": 0.0, "calculation_method": "final_rule_no_reduction", "position_in_sequence": 7, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 101736.8 to final"}}], "_will_be_processed": true}, {"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": [{"category": "BaseConfiguration", "subCategory": "DefautChecks", "subCategoryDisplayName": "Default Configuration", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 0.0, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Default Configuration - Execution starts"}, {"message": "Claim types considered for discount override: PreHosp,PostHosp,HospBenefit,HealthCheckUp,RI,ReOpenClaim"}, {"message": "Override Discount By <PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON> not executed as no matching rule found."}, {"message": "Default Configuration - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 0.0, "next_bill_amount": 0.0, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 1, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 0.0 to 0.0"}}, {"category": "BaseConfiguration", "subCategory": "OverideRoomRentHead", "subCategoryDisplayName": "Override Room Rent Inclusion", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 0.0, "postExectionTotalDeductionAmount": -112621.8, "reasons": [{"message": "*****Overide Room category Started*****"}, {"message": "Matching Config Count: 1"}, {"message": "Override Room Rent Inclusion - Execution starts"}, {"message": "ProportinateType:Normal"}, {"message": "*****Overide Room category Ended*****"}, {"message": "Override Room Rent Inclusion - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 0.0, "next_bill_amount": 112621.8, "bill_reduction": -112621.8, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 2, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 0.0 to 112621.8"}}, {"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 112621.8, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 2633.60"}, {"message": "HMOU Discount Calculated: 1433"}, {"message": "MAX Discount Calculated: 2633.60"}, {"message": "Discount Calculation Is Goving with HMOU"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Investigation & Lab Charges: 593.64,Miscellaneous Charges: 703.33,Consultant Charges: 136.03"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 112621.8, "next_bill_amount": 112621.8, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 3, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 112621.8 to 112621.8"}}, {"category": "NonMedicalExpense", "subCategory": "MarkAllNMEsAsPayable", "subCategoryDisplayName": "Pay All NMEs", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 111711.8, "preExectionTotalDeductionAmount": 1433.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 112621.8, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Pay All NMEs - Execution starts"}, {"message": "Overide Bills Based On ConfigType:Particulars PayableSubType:bio-medical waste fee, OverideNMEByPercenatage MaxPayable Percentage:100"}, {"message": "NME Deduction type: "}, {"message": "Calculated Payable Amt: 200.00"}, {"message": "Calculated Non-Payable Amt: 0"}, {"message": "Overide Bills Based On ConfigType:Particulars PayableSubType:nutritional and functional assessment charges, OverideNMEByPercenatage MaxPayable Percentage:100"}, {"message": "NME Deduction type: "}, {"message": "Calculated Payable Amt: 710.00"}, {"message": "Calculated Non-Payable Amt: 0"}, {"message": "Pay All NMEs - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 112621.8, "next_bill_amount": 112621.8, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 4, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 112621.8 to 112621.8"}}, {"category": "NonMedicalExpense", "subCategory": "OverideNMEByParticulars", "subCategoryDisplayName": "Override NME by Particulars", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 112621.8, "preExectionTotalDeductionAmount": 1433.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 112621.8, "postExectionTotalDeductionAmount": 10885.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Override NME by Particulars - Execution starts"}, {"message": "NME categories overridden to allow in the claim: SURCHARGES,DOCUMENTATION CHARGES / ADMINISTRATIVE EXPENSES,Admission/Registration Charges,Service Charges Where Nursing Charge Also Charged"}, {"message": "Total Payble NME Particulars to Override 4"}, {"message": "Override NME by Particulars - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 112621.8, "next_bill_amount": 101736.8, "bill_reduction": 10885.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 5, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 112621.8 to 101736.8"}}, {"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 112621.8, "preExectionTotalDeductionAmount": 1433.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 101736.8, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:4800.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:11800.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:7200.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiDeluxeRoom ,Room Rent PerDay:18700.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3200.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:34100.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:20900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9400.0"}, {"message": "Tariff ICU Room Rent From Master:9400.0"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Excess Of Traiff Package : Opted Room Category : PRIVATE"}, {"message": "Package Id : 1829550"}, {"message": "Package Master Details : Package Id : 1829550 General:61900,Sharing:65000,Private:74800,Deluxe:80800"}, {"message": "DIALYSIS COUNT :0"}, {"message": "Relevant Quantity :1"}, {"message": "Package Amount :74800"}, {"message": "Excess Of Tariff Package Calculated ; Package Amount: 85685.00 , Contracted Package Amount: 74800 "}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 101736.8, "next_bill_amount": 101736.8, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 6, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 101736.8 to 101736.8"}}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 100304.0, "preExecutionTotalBillAmount": 101736.8, "preExectionTotalDeductionAmount": 1433.0, "postExecutionApprovedAmount": 100304.0, "postExecutionTotalBillAmount": 101736.8, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:4800.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:11800.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:7200.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiDeluxeRoom ,Room Rent PerDay:18700.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3200.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:34100.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:20900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:2"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer nursing,room,room rent,room rent (per day),associated room charges,duty doctor,injection / iv administration charge,gst on room rent"}, {"message": "ICU : Room Rent based on insurer icu charges,icu nursing charges,icu room rent,icu injection / iv administration charge,intencivist charges,icu monitoring charges"}, {"message": "Normal Room Rent Lenght of Stay : 0"}, {"message": "ICU Room Rent Lenght of Stay : 0"}, {"message": "Tariff Bill Price as per insurer :8900.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :8900.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 8900.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 8900.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Excess Of Eligible Package : Eligible Room Category : PRIVATE"}, {"message": "Package Id : 1829550"}, {"message": "Package Master Details : Package Id : 1829550 General:61900,Sharing:65000,Private:74800,Deluxe:80800"}, {"message": "DIALYSIS COUNT :0"}, {"message": "Relevant Quantity :1"}, {"message": "Package Amount :74800"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 8900.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 0.0"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 0%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 101736.8, "next_bill_amount": null, "bill_reduction": 0.0, "calculation_method": "final_rule_no_reduction", "position_in_sequence": 7, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 101736.8 to final"}}], "_will_be_processed": true}, {"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": [{"category": "BaseConfiguration", "subCategory": "DefautChecks", "subCategoryDisplayName": "Default Configuration", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 0.0, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Default Configuration - Execution starts"}, {"message": "Claim types considered for discount override: PreHosp,PostHosp,HospBenefit,HealthCheckUp,RI,ReOpenClaim"}, {"message": "Override Discount By <PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON> not executed as no matching rule found."}, {"message": "Default Configuration - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 0.0, "next_bill_amount": 0.0, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 1, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 0.0 to 0.0"}}, {"category": "BaseConfiguration", "subCategory": "OverideRoomRentHead", "subCategoryDisplayName": "Override Room Rent Inclusion", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 0.0, "postExectionTotalDeductionAmount": -112621.8, "reasons": [{"message": "*****Overide Room category Started*****"}, {"message": "Matching Config Count: 1"}, {"message": "Override Room Rent Inclusion - Execution starts"}, {"message": "ProportinateType:Normal"}, {"message": "*****Overide Room category Ended*****"}, {"message": "Override Room Rent Inclusion - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 0.0, "next_bill_amount": 112621.8, "bill_reduction": -112621.8, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 2, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 0.0 to 112621.8"}}, {"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 112621.8, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 1432.00"}, {"message": "HMOU Discount Calculated: 1433.0"}, {"message": "MAX Discount Calculated: 1433.0"}, {"message": "Discount Calculation Is Goving with HMOU"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Consultant Charges: 250.17,Investigation & Lab Charges: 1091.76,Miscellaneous Charges: 91.06"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 112621.8, "next_bill_amount": 112621.8, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 3, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 112621.8 to 112621.8"}}, {"category": "NonMedicalExpense", "subCategory": "MarkAllNMEsAsPayable", "subCategoryDisplayName": "Pay All NMEs", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 111711.8, "preExectionTotalDeductionAmount": 1432.99, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 112621.8, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Pay All NMEs - Execution starts"}, {"message": "Overide Bills Based On ConfigType:Particulars PayableSubType:bio-medical waste fee, OverideNMEByPercenatage MaxPayable Percentage:100"}, {"message": "NME Deduction type: "}, {"message": "Calculated Payable Amt: 200.00"}, {"message": "Calculated Non-Payable Amt: 0"}, {"message": "Overide Bills Based On ConfigType:Particulars PayableSubType:nutritional and functional assessment charges, OverideNMEByPercenatage MaxPayable Percentage:100"}, {"message": "NME Deduction type: "}, {"message": "Calculated Payable Amt: 710.00"}, {"message": "Calculated Non-Payable Amt: 0"}, {"message": "Pay All NMEs - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 112621.8, "next_bill_amount": 112621.8, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 4, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 112621.8 to 112621.8"}}, {"category": "NonMedicalExpense", "subCategory": "OverideNMEByParticulars", "subCategoryDisplayName": "Override NME by Particulars", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 112621.8, "preExectionTotalDeductionAmount": 1432.99, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 112621.8, "postExectionTotalDeductionAmount": 22901.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Override NME by Particulars - Execution starts"}, {"message": "NME categories overridden to allow in the claim: SURCHARGES,DOCUMENTATION CHARGES / ADMINISTRATIVE EXPENSES,Admission/Registration Charges,Service Charges Where Nursing Charge Also Charged"}, {"message": "Total Payble NME Particulars to Override 4"}, {"message": "Override NME by Particulars - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 112621.8, "next_bill_amount": 89720.8, "bill_reduction": 22901.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 5, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 112621.8 to 89720.8"}}, {"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 112621.8, "preExectionTotalDeductionAmount": 1432.99, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 89720.8, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:4800.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:11800.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:7200.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiDeluxeRoom ,Room Rent PerDay:18700.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3200.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:34100.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:20900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9400.0"}, {"message": "Tariff ICU Room Rent From Master:9400.0"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Excess Of Traiff Package : Opted Room Category : PRIVATE"}, {"message": "Package Id : 1829550"}, {"message": "Package Master Details : Package Id : 1829550 General:61900,Sharing:65000,Private:74800,Deluxe:80800"}, {"message": "DIALYSIS COUNT :0"}, {"message": "Relevant Quantity :1"}, {"message": "Package Amount :74800"}, {"message": "Excess Of Tariff Package Calculated ; Package Amount: 97701.00 , Contracted Package Amount: 74800 "}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 89720.8, "next_bill_amount": 89720.8, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 6, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 89720.8 to 89720.8"}}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 88288.01, "preExecutionTotalBillAmount": 89720.8, "preExectionTotalDeductionAmount": 1432.99, "postExecutionApprovedAmount": 88288.01, "postExecutionTotalBillAmount": 89720.8, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:4800.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:11800.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:7200.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiDeluxeRoom ,Room Rent PerDay:18700.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3200.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:34100.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:20900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:2"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer nursing,room,room rent,room rent (per day),associated room charges,duty doctor,injection / iv administration charge,gst on room rent"}, {"message": "ICU : Room Rent based on insurer icu charges,icu nursing charges,icu room rent,icu injection / iv administration charge,intencivist charges,icu monitoring charges"}, {"message": "Normal Room Rent Lenght of Stay : 0"}, {"message": "ICU Room Rent Lenght of Stay : 0"}, {"message": "Tariff Bill Price as per insurer :8900.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :8900.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 8900.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 8900.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Excess Of Eligible Package : Eligible Room Category : PRIVATE"}, {"message": "Package Id : 1829550"}, {"message": "Package Master Details : Package Id : 1829550 General:61900,Sharing:65000,Private:74800,Deluxe:80800"}, {"message": "DIALYSIS COUNT :0"}, {"message": "Relevant Quantity :1"}, {"message": "Package Amount :74800"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 8900.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 0.0"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 0%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 89720.8, "next_bill_amount": null, "bill_reduction": 0.0, "calculation_method": "final_rule_no_reduction", "position_in_sequence": 7, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 89720.8 to final"}}], "_will_be_processed": true}, {"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": [{"category": "BaseConfiguration", "subCategory": "DefautChecks", "subCategoryDisplayName": "Default Configuration", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 0.0, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Default Configuration - Execution starts"}, {"message": "Claim types considered for discount override: PreHosp,PostHosp,HospBenefit,HealthCheckUp,RI,ReOpenClaim"}, {"message": "Override Discount By <PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON> not executed as no matching rule found."}, {"message": "Default Configuration - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 0.0, "next_bill_amount": 0.0, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 1, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 0.0 to 0.0"}}, {"category": "BaseConfiguration", "subCategory": "OverideRoomRentHead", "subCategoryDisplayName": "Override Room Rent Inclusion", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 0.0, "postExectionTotalDeductionAmount": -112621.8, "reasons": [{"message": "*****Overide Room category Started*****"}, {"message": "Matching Config Count: 1"}, {"message": "Override Room Rent Inclusion - Execution starts"}, {"message": "ProportinateType:Normal"}, {"message": "*****Overide Room category Ended*****"}, {"message": "Override Room Rent Inclusion - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 0.0, "next_bill_amount": 112621.8, "bill_reduction": -112621.8, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 2, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 0.0 to 112621.8"}}, {"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 112621.8, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 1432.00"}, {"message": "HMOU Discount Calculated: 1433.0"}, {"message": "MAX Discount Calculated: 1433.0"}, {"message": "Discount Calculation Is Goving with HMOU"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Consultant Charges: 250.17,Investigation & Lab Charges: 1091.76,Miscellaneous Charges: 91.06"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 112621.8, "next_bill_amount": 112621.8, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 3, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 112621.8 to 112621.8"}}, {"category": "NonMedicalExpense", "subCategory": "MarkAllNMEsAsPayable", "subCategoryDisplayName": "Pay All NMEs", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 111711.8, "preExectionTotalDeductionAmount": 1432.99, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 112621.8, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Pay All NMEs - Execution starts"}, {"message": "Overide Bills Based On ConfigType:Particulars PayableSubType:bio-medical waste fee, OverideNMEByPercenatage MaxPayable Percentage:100"}, {"message": "NME Deduction type: "}, {"message": "Calculated Payable Amt: 200.00"}, {"message": "Calculated Non-Payable Amt: 0"}, {"message": "Overide Bills Based On ConfigType:Particulars PayableSubType:nutritional and functional assessment charges, OverideNMEByPercenatage MaxPayable Percentage:100"}, {"message": "NME Deduction type: "}, {"message": "Calculated Payable Amt: 710.00"}, {"message": "Calculated Non-Payable Amt: 0"}, {"message": "Pay All NMEs - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 112621.8, "next_bill_amount": 112621.8, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 4, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 112621.8 to 112621.8"}}, {"category": "NonMedicalExpense", "subCategory": "OverideNMEByParticulars", "subCategoryDisplayName": "Override NME by Particulars", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 112621.8, "preExectionTotalDeductionAmount": 1432.99, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 112621.8, "postExectionTotalDeductionAmount": 22901.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Override NME by Particulars - Execution starts"}, {"message": "NME categories overridden to allow in the claim: SURCHARGES,DOCUMENTATION CHARGES / ADMINISTRATIVE EXPENSES,Admission/Registration Charges,Service Charges Where Nursing Charge Also Charged"}, {"message": "Total Payble NME Particulars to Override 4"}, {"message": "Override NME by Particulars - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 112621.8, "next_bill_amount": 89720.8, "bill_reduction": 22901.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 5, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 112621.8 to 89720.8"}}, {"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 112621.8, "preExectionTotalDeductionAmount": 1432.99, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 89720.8, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:4800.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:11800.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:7200.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiDeluxeRoom ,Room Rent PerDay:18700.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3200.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:34100.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:20900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9400.0"}, {"message": "Tariff ICU Room Rent From Master:9400.0"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Excess Of Traiff Package : Opted Room Category : PRIVATE"}, {"message": "Package Id : 1829550"}, {"message": "Package Master Details : Package Id : 1829550 General:61900,Sharing:65000,Private:74800,Deluxe:80800"}, {"message": "DIALYSIS COUNT :0"}, {"message": "Relevant Quantity :1"}, {"message": "Package Amount :74800"}, {"message": "Excess Of Tariff Package Calculated ; Package Amount: 97701.00 , Contracted Package Amount: 74800 "}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 89720.8, "next_bill_amount": 89720.8, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 6, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 89720.8 to 89720.8"}}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 88288.01, "preExecutionTotalBillAmount": 89720.8, "preExectionTotalDeductionAmount": 1432.99, "postExecutionApprovedAmount": 88288.01, "postExecutionTotalBillAmount": 89720.8, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:4800.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:11800.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:7200.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiDeluxeRoom ,Room Rent PerDay:18700.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3200.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:34100.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:20900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:2"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer nursing,room,room rent,room rent (per day),associated room charges,duty doctor,injection / iv administration charge,gst on room rent"}, {"message": "ICU : Room Rent based on insurer icu charges,icu nursing charges,icu room rent,icu injection / iv administration charge,intencivist charges,icu monitoring charges"}, {"message": "Normal Room Rent Lenght of Stay : 0"}, {"message": "ICU Room Rent Lenght of Stay : 0"}, {"message": "Tariff Bill Price as per insurer :8900.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :8900.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 8900.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 8900.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Excess Of Eligible Package : Eligible Room Category : PRIVATE"}, {"message": "Package Id : 1829550"}, {"message": "Package Master Details : Package Id : 1829550 General:61900,Sharing:65000,Private:74800,Deluxe:80800"}, {"message": "DIALYSIS COUNT :0"}, {"message": "Relevant Quantity :1"}, {"message": "Package Amount :74800"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 8900.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 0.0"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 0%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 89720.8, "next_bill_amount": null, "bill_reduction": 0.0, "calculation_method": "final_rule_no_reduction", "position_in_sequence": 7, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 89720.8 to final"}}], "_will_be_processed": true}, {"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": [{"category": "BaseConfiguration", "subCategory": "DefautChecks", "subCategoryDisplayName": "Default Configuration", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 0.0, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Default Configuration - Execution starts"}, {"message": "Claim types considered for discount override: PreHosp,PostHosp,HospBenefit,HealthCheckUp,RI,ReOpenClaim"}, {"message": "Override Discount By <PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON> not executed as no matching rule found."}, {"message": "Default Configuration - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 0.0, "next_bill_amount": 0.0, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 1, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 0.0 to 0.0"}}, {"category": "BaseConfiguration", "subCategory": "OverideRoomRentHead", "subCategoryDisplayName": "Override Room Rent Inclusion", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 0.0, "postExectionTotalDeductionAmount": -112621.8, "reasons": [{"message": "*****Overide Room category Started*****"}, {"message": "Matching Config Count: 1"}, {"message": "Override Room Rent Inclusion - Execution starts"}, {"message": "ProportinateType:Normal"}, {"message": "*****Overide Room category Ended*****"}, {"message": "Override Room Rent Inclusion - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 0.0, "next_bill_amount": 112621.8, "bill_reduction": -112621.8, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 2, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 0.0 to 112621.8"}}, {"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 112621.8, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 1432.00"}, {"message": "HMOU Discount Calculated: 1433.0"}, {"message": "MAX Discount Calculated: 1433.0"}, {"message": "Discount Calculation Is Goving with HMOU"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Consultant Charges: 250.17,Investigation & Lab Charges: 1091.76,Miscellaneous Charges: 91.06"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 112621.8, "next_bill_amount": 112621.8, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 3, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 112621.8 to 112621.8"}}, {"category": "NonMedicalExpense", "subCategory": "MarkAllNMEsAsPayable", "subCategoryDisplayName": "Pay All NMEs", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 111711.8, "preExectionTotalDeductionAmount": 1432.99, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 112621.8, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Pay All NMEs - Execution starts"}, {"message": "Overide Bills Based On ConfigType:Particulars PayableSubType:bio-medical waste fee, OverideNMEByPercenatage MaxPayable Percentage:100"}, {"message": "NME Deduction type: "}, {"message": "Calculated Payable Amt: 200.00"}, {"message": "Calculated Non-Payable Amt: 0"}, {"message": "Overide Bills Based On ConfigType:Particulars PayableSubType:nutritional and functional assessment charges, OverideNMEByPercenatage MaxPayable Percentage:100"}, {"message": "NME Deduction type: "}, {"message": "Calculated Payable Amt: 710.00"}, {"message": "Calculated Non-Payable Amt: 0"}, {"message": "Pay All NMEs - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 112621.8, "next_bill_amount": 112621.8, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 4, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 112621.8 to 112621.8"}}, {"category": "NonMedicalExpense", "subCategory": "OverideNMEByParticulars", "subCategoryDisplayName": "Override NME by Particulars", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 112621.8, "preExectionTotalDeductionAmount": 1432.99, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 112621.8, "postExectionTotalDeductionAmount": 22901.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Override NME by Particulars - Execution starts"}, {"message": "NME categories overridden to allow in the claim: SURCHARGES,DOCUMENTATION CHARGES / ADMINISTRATIVE EXPENSES,Admission/Registration Charges,Service Charges Where Nursing Charge Also Charged"}, {"message": "Total Payble NME Particulars to Override 4"}, {"message": "Override NME by Particulars - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 112621.8, "next_bill_amount": 89720.8, "bill_reduction": 22901.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 5, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 112621.8 to 89720.8"}}, {"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 112621.8, "preExectionTotalDeductionAmount": 1432.99, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 89720.8, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:4800.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:11800.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:7200.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiDeluxeRoom ,Room Rent PerDay:18700.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3200.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:34100.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:20900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9400.0"}, {"message": "Tariff ICU Room Rent From Master:9400.0"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Excess Of Traiff Package : Opted Room Category : PRIVATE"}, {"message": "Package Id : 1829550"}, {"message": "Package Master Details : Package Id : 1829550 General:61900,Sharing:65000,Private:74800,Deluxe:80800"}, {"message": "DIALYSIS COUNT :0"}, {"message": "Relevant Quantity :1"}, {"message": "Package Amount :74800"}, {"message": "Excess Of Tariff Package Calculated ; Package Amount: 97701.00 , Contracted Package Amount: 74800 "}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 89720.8, "next_bill_amount": 89720.8, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 6, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 89720.8 to 89720.8"}}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 88288.01, "preExecutionTotalBillAmount": 89720.8, "preExectionTotalDeductionAmount": 1432.99, "postExecutionApprovedAmount": 88288.01, "postExecutionTotalBillAmount": 89720.8, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:4800.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:11800.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:7200.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiDeluxeRoom ,Room Rent PerDay:18700.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3200.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:34100.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:20900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:2"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer nursing,room,room rent,room rent (per day),associated room charges,duty doctor,injection / iv administration charge,gst on room rent"}, {"message": "ICU : Room Rent based on insurer icu charges,icu nursing charges,icu room rent,icu injection / iv administration charge,intencivist charges,icu monitoring charges"}, {"message": "Normal Room Rent Lenght of Stay : 0"}, {"message": "ICU Room Rent Lenght of Stay : 0"}, {"message": "Tariff Bill Price as per insurer :8900.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :8900.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 8900.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 8900.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Excess Of Eligible Package : Eligible Room Category : PRIVATE"}, {"message": "Package Id : 1829550"}, {"message": "Package Master Details : Package Id : 1829550 General:61900,Sharing:65000,Private:74800,Deluxe:80800"}, {"message": "DIALYSIS COUNT :0"}, {"message": "Relevant Quantity :1"}, {"message": "Package Amount :74800"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 8900.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 0.0"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 0%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 89720.8, "next_bill_amount": null, "bill_reduction": 0.0, "calculation_method": "final_rule_no_reduction", "position_in_sequence": 7, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 89720.8 to final"}}], "_will_be_processed": true}, {"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": [{"category": "BaseConfiguration", "subCategory": "DefautChecks", "subCategoryDisplayName": "Default Configuration", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 0.0, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Default Configuration - Execution starts"}, {"message": "Claim types considered for discount override: PreHosp,PostHosp,HospBenefit,HealthCheckUp,RI,ReOpenClaim"}, {"message": "Override Discount By <PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON> not executed as no matching rule found."}, {"message": "Default Configuration - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 0.0, "next_bill_amount": 0.0, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 1, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 0.0 to 0.0"}}, {"category": "BaseConfiguration", "subCategory": "OverideRoomRentHead", "subCategoryDisplayName": "Override Room Rent Inclusion", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 0.0, "postExectionTotalDeductionAmount": -112621.8, "reasons": [{"message": "*****Overide Room category Started*****"}, {"message": "Matching Config Count: 1"}, {"message": "Override Room Rent Inclusion - Execution starts"}, {"message": "ProportinateType:Normal"}, {"message": "*****Overide Room category Ended*****"}, {"message": "Override Room Rent Inclusion - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 0.0, "next_bill_amount": 112621.8, "bill_reduction": -112621.8, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 2, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 0.0 to 112621.8"}}, {"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 112621.8, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 1432.00"}, {"message": "HMOU Discount Calculated: 1433.0"}, {"message": "MAX Discount Calculated: 1433.0"}, {"message": "Discount Calculation Is Goving with HMOU"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Consultant Charges: 250.17,Investigation & Lab Charges: 1091.76,Miscellaneous Charges: 91.06"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 112621.8, "next_bill_amount": 112621.8, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 3, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 112621.8 to 112621.8"}}, {"category": "NonMedicalExpense", "subCategory": "MarkAllNMEsAsPayable", "subCategoryDisplayName": "Pay All NMEs", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 111711.8, "preExectionTotalDeductionAmount": 1432.99, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 112621.8, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Pay All NMEs - Execution starts"}, {"message": "Overide Bills Based On ConfigType:Particulars PayableSubType:bio-medical waste fee, OverideNMEByPercenatage MaxPayable Percentage:100"}, {"message": "NME Deduction type: "}, {"message": "Calculated Payable Amt: 200.00"}, {"message": "Calculated Non-Payable Amt: 0"}, {"message": "Overide Bills Based On ConfigType:Particulars PayableSubType:nutritional and functional assessment charges, OverideNMEByPercenatage MaxPayable Percentage:100"}, {"message": "NME Deduction type: "}, {"message": "Calculated Payable Amt: 710.00"}, {"message": "Calculated Non-Payable Amt: 0"}, {"message": "Pay All NMEs - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 112621.8, "next_bill_amount": 112621.8, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 4, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 112621.8 to 112621.8"}}, {"category": "NonMedicalExpense", "subCategory": "OverideNMEByParticulars", "subCategoryDisplayName": "Override NME by Particulars", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 112621.8, "preExectionTotalDeductionAmount": 1432.99, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 112621.8, "postExectionTotalDeductionAmount": 22901.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Override NME by Particulars - Execution starts"}, {"message": "NME categories overridden to allow in the claim: SURCHARGES,DOCUMENTATION CHARGES / ADMINISTRATIVE EXPENSES,Admission/Registration Charges,Service Charges Where Nursing Charge Also Charged"}, {"message": "Total Payble NME Particulars to Override 4"}, {"message": "Override NME by Particulars - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 112621.8, "next_bill_amount": 89720.8, "bill_reduction": 22901.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 5, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 112621.8 to 89720.8"}}, {"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 112621.8, "preExectionTotalDeductionAmount": 1432.99, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 89720.8, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:4800.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:11800.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:7200.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiDeluxeRoom ,Room Rent PerDay:18700.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3200.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:34100.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:20900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9400.0"}, {"message": "Tariff ICU Room Rent From Master:9400.0"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Excess Of Traiff Package : Opted Room Category : PRIVATE"}, {"message": "Package Id : 1829550"}, {"message": "Package Master Details : Package Id : 1829550 General:61900,Sharing:65000,Private:74800,Deluxe:80800"}, {"message": "DIALYSIS COUNT :0"}, {"message": "Relevant Quantity :1"}, {"message": "Package Amount :74800"}, {"message": "Excess Of Tariff Package Calculated ; Package Amount: 97701.00 , Contracted Package Amount: 74800 "}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 89720.8, "next_bill_amount": 89720.8, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 6, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 89720.8 to 89720.8"}}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 88288.01, "preExecutionTotalBillAmount": 89720.8, "preExectionTotalDeductionAmount": 1432.99, "postExecutionApprovedAmount": 88288.01, "postExecutionTotalBillAmount": 89720.8, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:4800.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:11800.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:7200.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiDeluxeRoom ,Room Rent PerDay:18700.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3200.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:34100.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:20900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:2"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer nursing,room,room rent,room rent (per day),associated room charges,duty doctor,injection / iv administration charge,gst on room rent"}, {"message": "ICU : Room Rent based on insurer icu charges,icu nursing charges,icu room rent,icu injection / iv administration charge,intencivist charges,icu monitoring charges"}, {"message": "Normal Room Rent Lenght of Stay : 0"}, {"message": "ICU Room Rent Lenght of Stay : 0"}, {"message": "Tariff Bill Price as per insurer :8900.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :8900.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 8900.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 8900.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Excess Of Eligible Package : Eligible Room Category : PRIVATE"}, {"message": "Package Id : 1829550"}, {"message": "Package Master Details : Package Id : 1829550 General:61900,Sharing:65000,Private:74800,Deluxe:80800"}, {"message": "DIALYSIS COUNT :0"}, {"message": "Relevant Quantity :1"}, {"message": "Package Amount :74800"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 8900.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 0.0"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 0%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 89720.8, "next_bill_amount": null, "bill_reduction": 0.0, "calculation_method": "final_rule_no_reduction", "position_in_sequence": 7, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 89720.8 to final"}}], "_will_be_processed": true}, {"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": [{"category": "BaseConfiguration", "subCategory": "DefautChecks", "subCategoryDisplayName": "Default Configuration", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 0.0, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Default Configuration - Execution starts"}, {"message": "Claim types considered for discount override: PreHosp,PostHosp,HospBenefit,HealthCheckUp,RI,ReOpenClaim"}, {"message": "Override Discount By <PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON> not executed as no matching rule found."}, {"message": "Default Configuration - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 0.0, "next_bill_amount": 0.0, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 1, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 0.0 to 0.0"}}, {"category": "BaseConfiguration", "subCategory": "OverideRoomRentHead", "subCategoryDisplayName": "Override Room Rent Inclusion", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 0.0, "postExectionTotalDeductionAmount": -112621.8, "reasons": [{"message": "*****Overide Room category Started*****"}, {"message": "Matching Config Count: 1"}, {"message": "Override Room Rent Inclusion - Execution starts"}, {"message": "ProportinateType:Normal"}, {"message": "*****Overide Room category Ended*****"}, {"message": "Override Room Rent Inclusion - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 0.0, "next_bill_amount": 112621.8, "bill_reduction": -112621.8, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 2, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 0.0 to 112621.8"}}, {"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 112621.8, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 1432.00"}, {"message": "HMOU Discount Calculated: 1433.0"}, {"message": "MAX Discount Calculated: 1433.0"}, {"message": "Discount Calculation Is Goving with HMOU"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Consultant Charges: 250.17,Investigation & Lab Charges: 1091.76,Miscellaneous Charges: 91.06"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 112621.8, "next_bill_amount": 112621.8, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 3, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 112621.8 to 112621.8"}}, {"category": "NonMedicalExpense", "subCategory": "MarkAllNMEsAsPayable", "subCategoryDisplayName": "Pay All NMEs", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 111711.8, "preExectionTotalDeductionAmount": 1432.99, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 112621.8, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Pay All NMEs - Execution starts"}, {"message": "Overide Bills Based On ConfigType:Particulars PayableSubType:bio-medical waste fee, OverideNMEByPercenatage MaxPayable Percentage:100"}, {"message": "NME Deduction type: "}, {"message": "Calculated Payable Amt: 200.00"}, {"message": "Calculated Non-Payable Amt: 0"}, {"message": "Overide Bills Based On ConfigType:Particulars PayableSubType:nutritional and functional assessment charges, OverideNMEByPercenatage MaxPayable Percentage:100"}, {"message": "NME Deduction type: "}, {"message": "Calculated Payable Amt: 710.00"}, {"message": "Calculated Non-Payable Amt: 0"}, {"message": "Pay All NMEs - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 112621.8, "next_bill_amount": 112621.8, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 4, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 112621.8 to 112621.8"}}, {"category": "NonMedicalExpense", "subCategory": "OverideNMEByParticulars", "subCategoryDisplayName": "Override NME by Particulars", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 112621.8, "preExectionTotalDeductionAmount": 1432.99, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 112621.8, "postExectionTotalDeductionAmount": 22901.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Override NME by Particulars - Execution starts"}, {"message": "NME categories overridden to allow in the claim: SURCHARGES,DOCUMENTATION CHARGES / ADMINISTRATIVE EXPENSES,Admission/Registration Charges,Service Charges Where Nursing Charge Also Charged"}, {"message": "Total Payble NME Particulars to Override 4"}, {"message": "Override NME by Particulars - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 112621.8, "next_bill_amount": 89720.8, "bill_reduction": 22901.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 5, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 112621.8 to 89720.8"}}, {"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 112621.8, "preExectionTotalDeductionAmount": 1432.99, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 89720.8, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:4800.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:11800.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:7200.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiDeluxeRoom ,Room Rent PerDay:18700.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3200.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:34100.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:20900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9400.0"}, {"message": "Tariff ICU Room Rent From Master:9400.0"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Excess Of Traiff Package : Opted Room Category : PRIVATE"}, {"message": "Package Id : 1829550"}, {"message": "Package Master Details : Package Id : 1829550 General:61900,Sharing:65000,Private:74800,Deluxe:80800"}, {"message": "DIALYSIS COUNT :0"}, {"message": "Relevant Quantity :1"}, {"message": "Package Amount :74800"}, {"message": "Excess Of Tariff Package Calculated ; Package Amount: 97701.00 , Contracted Package Amount: 74800 "}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 89720.8, "next_bill_amount": 89720.8, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 6, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 89720.8 to 89720.8"}}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 88288.01, "preExecutionTotalBillAmount": 89720.8, "preExectionTotalDeductionAmount": 1432.99, "postExecutionApprovedAmount": 88288.01, "postExecutionTotalBillAmount": 89720.8, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:4800.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:11800.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:7200.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiDeluxeRoom ,Room Rent PerDay:18700.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3200.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:34100.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:20900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:2"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:IsolationWard ,Room Rent PerDay:9400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:8900.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer nursing,room,room rent,room rent (per day),associated room charges,duty doctor,injection / iv administration charge,gst on room rent"}, {"message": "ICU : Room Rent based on insurer icu charges,icu nursing charges,icu room rent,icu injection / iv administration charge,intencivist charges,icu monitoring charges"}, {"message": "Normal Room Rent Lenght of Stay : 0"}, {"message": "ICU Room Rent Lenght of Stay : 0"}, {"message": "Tariff Bill Price as per insurer :8900.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :8900.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 8900.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 8900.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Excess Of Eligible Package : Eligible Room Category : PRIVATE"}, {"message": "Package Id : 1829550"}, {"message": "Package Master Details : Package Id : 1829550 General:61900,Sharing:65000,Private:74800,Deluxe:80800"}, {"message": "DIALYSIS COUNT :0"}, {"message": "Relevant Quantity :1"}, {"message": "Package Amount :74800"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 8900.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 0.0"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 0%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 89720.8, "next_bill_amount": null, "bill_reduction": 0.0, "calculation_method": "final_rule_no_reduction", "position_in_sequence": 7, "total_rules_in_sequence": 7, "explanation": "Bill reduced from 89720.8 to final"}}], "_will_be_processed": true}], "_cleaning_metadata": {"fields_removed": ["logCreatedDate (from logs)", "data (from logs)", "timeOfOccurrence (from data entries and rule logs)", "loggerType (from data entries and rule logs)", "configRuleLogObject (from rule engine logs)", "matchingConfig (from rule engine logs)"], "fields_renamed": ["logMessages → reasons (in rule engine logs)"], "filtering_applied": ["Only rule engine logs with non-empty matchingConfig arrays are included"], "calculations_applied": ["postExectionTotalDeductionAmount calculated as bill amount reduction (current_bill - next_bill)"], "cleaning_timestamp": "2025-06-13T20:58:50.630310", "purpose": "Remove unnecessary fields, rename for clarity, filter for relevant rule logs, and calculate incremental deductions"}, "_processing_metadata": {"filtered_for_claim_logs_only": true, "processing_timestamp": "2025-06-13T20:58:50.637590", "total_logs": 8, "claim_logs_count": 8}}]