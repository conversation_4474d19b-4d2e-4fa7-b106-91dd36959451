import google.generativeai as genai
from typing import List, Dict, Any, Optional
import logging
from config import settings
from vector_store import VectorStore

logger = logging.getLogger(__name__)

class QAEngine:
    def __init__(self):
        self.vector_store = VectorStore()
        genai.configure(api_key=settings.GEMINI_API_KEY)
        self.model = genai.GenerativeModel(settings.GEMINI_MODEL)
        self.max_tokens = settings.MAX_TOKENS
    
    def _create_context_from_chunks(self, chunks: List[Dict[str, Any]]) -> str:
        """Create patient-focused context string from retrieved chunks"""
        if not chunks:
            return "No relevant information found about your claim."

        context_parts = []
        for i, chunk in enumerate(chunks, 1):
            content = chunk.get('content', '')
            chunk_type = chunk.get('chunk_type', 'unknown')
            claim_no = chunk.get('claim_no', 'unknown')

            # Use patient-friendly labels
            type_labels = {
                'rule_engine_log': 'Benefit Processing',
                'general_log': 'Claim Activity',
                'claim_summary': 'Claim Overview'
            }
            friendly_type = type_labels.get(chunk_type, chunk_type)

            context_parts.append(f"--- Information {i} (Claim #{claim_no} - {friendly_type}) ---")
            context_parts.append(content)
            context_parts.append("")

        return "\n".join(context_parts)

    def _create_context_from_structured_data(self, structured_data: Dict[str, Any], claim_no: Optional[int] = None) -> str:
        """Create comprehensive context from the final structured claim data"""
        if not structured_data:
            return "No claim information available."

        context_parts = []

        # Add claim header
        if claim_no:
            context_parts.append(f"=== COMPREHENSIVE CLAIM INFORMATION FOR CLAIM #{claim_no} ===")
        else:
            context_parts.append("=== COMPREHENSIVE CLAIM INFORMATION ===")
        context_parts.append("")

        # 1. DeductionsInClaim (Most Important)
        if 'DeductionsInClaim' in structured_data:
            context_parts.append("💰 DEDUCTIONS AND FINANCIAL IMPACT:")
            deductions = structured_data['DeductionsInClaim']
            if isinstance(deductions, list):
                total_deductions = 0
                for deduction in deductions:
                    if isinstance(deduction, dict):
                        desc = deduction.get('deductionDesc', 'Unknown')
                        amount = deduction.get('amount', 0)
                        total_deductions += amount

                        if desc == 'nonMedicalDeductions':
                            context_parts.append(f"  • Non-Medical Charges: ₹{amount:,}")
                            reasons = deduction.get('reasons', [])
                            if reasons:
                                context_parts.append("    Breakdown:")
                                for reason in reasons[:5]:  # Limit to first 5 for context
                                    if isinstance(reason, dict):
                                        bill_desc = reason.get('for', reason.get('typeDesc', 'Unknown'))
                                        bill_amt = reason.get('nonPayableAmt', 0)
                                        context_parts.append(f"      - {bill_desc}: ₹{bill_amt:,}")
                        else:
                            remarks = deduction.get('remarks', '')
                            context_parts.append(f"  • {desc}: ₹{amount:,}" + (f" ({remarks})" if remarks else ""))

                context_parts.append(f"  TOTAL DEDUCTIONS: ₹{total_deductions:,}")
            context_parts.append("")

        # 2. BasicClaimInformation
        if 'BasicClaimInformation' in structured_data:
            context_parts.append("📋 BASIC CLAIM INFORMATION:")
            basic_info = structured_data['BasicClaimInformation']
            if isinstance(basic_info, dict):
                # Extract key information
                key_fields = ['claimProcessed', 'hasDeductions', 'totalDeductionCategories', 'processingComplete']
                for field in key_fields:
                    if field in basic_info:
                        value = basic_info[field]
                        friendly_field = field.replace('_', ' ').replace('camelCase', ' ').title()
                        context_parts.append(f"  • {friendly_field}: {value}")
            context_parts.append("")

        # 3. AilmentSummary
        if 'AilmentSummary' in structured_data:
            context_parts.append("🏥 MEDICAL CONDITION DETAILS:")
            ailment = structured_data['AilmentSummary']
            if isinstance(ailment, dict):
                for key, value in ailment.items():
                    if value and str(value).strip():
                        friendly_key = key.replace('_', ' ').replace('camelCase', ' ').title()
                        context_parts.append(f"  • {friendly_key}: {value}")
            context_parts.append("")

        # 4. HospitalDetails
        if 'HospitalDetails' in structured_data:
            context_parts.append("🏥 HOSPITAL AND TREATMENT DETAILS:")
            hospital = structured_data['HospitalDetails']
            if isinstance(hospital, dict):
                important_fields = ['hospitalName', 'location', 'networkStatus', 'treatmentDepartment', 'admissionDate', 'dischargeDate']
                for field in important_fields:
                    if field in hospital and hospital[field]:
                        friendly_field = field.replace('_', ' ').replace('camelCase', ' ').title()
                        context_parts.append(f"  • {friendly_field}: {hospital[field]}")
            context_parts.append("")

        # 5. PatientAndPolicyDetails
        if 'PatientAndPolicyDetails' in structured_data:
            context_parts.append("👤 PATIENT AND POLICY INFORMATION:")
            patient = structured_data['PatientAndPolicyDetails']
            if isinstance(patient, dict):
                important_fields = ['policyNumber', 'policyType', 'coverageAmount', 'beneficiaryRelation']
                for field in important_fields:
                    if field in patient and patient[field]:
                        friendly_field = field.replace('_', ' ').replace('camelCase', ' ').title()
                        context_parts.append(f"  • {friendly_field}: {patient[field]}")
            context_parts.append("")

        # 6. Deduction reasons (Rule Processing Details)
        if 'Deduction reasons' in structured_data:
            context_parts.append("⚙️ RULE PROCESSING AND DEDUCTION EXPLANATIONS:")
            reasons = structured_data['Deduction reasons']
            if isinstance(reasons, list):
                for reason_entry in reasons:
                    if isinstance(reason_entry, dict) and 'logs' in reason_entry:
                        for log in reason_entry['logs']:
                            if isinstance(log, dict) and 'ruleEngineLogs' in log:
                                rule_logs = log['ruleEngineLogs']
                                if isinstance(rule_logs, dict):
                                    for category, category_data in rule_logs.items():
                                        if isinstance(category_data, dict):
                                            rules = category_data.get('rules', [])
                                            count = category_data.get('count', 0)
                                            context_parts.append(f"  • {category} Rules Applied ({count} rules):")
                                            for rule in rules[:3]:  # Limit to first 3 rules per category
                                                if isinstance(rule, dict):
                                                    sub_category = rule.get('subCategoryDisplayName', rule.get('subCategory', 'Unknown'))
                                                    reasons_list = rule.get('reasons', [])
                                                    context_parts.append(f"    - {sub_category}")
                                                    for reason in reasons_list[:2]:  # Limit to first 2 reasons per rule
                                                        if isinstance(reason, dict):
                                                            message = reason.get('message', '')
                                                            if message:
                                                                context_parts.append(f"      → {message}")
            context_parts.append("")

        # 7. MissingDocuments
        if 'MissingDocuments' in structured_data:
            missing_docs = structured_data['MissingDocuments']
            if isinstance(missing_docs, list) and missing_docs:
                context_parts.append("📄 MISSING DOCUMENTS:")
                for doc in missing_docs:
                    context_parts.append(f"  • {doc}")
                context_parts.append("")
            else:
                context_parts.append("✅ NO MISSING DOCUMENTS")
                context_parts.append("")

        return "\n".join(context_parts)
    
    def _create_system_prompt(self) -> str:
        """Create patient-focused system prompt for the LLM"""
        return """You are a helpful insurance claims assistant who explains claim processing information from the patient's perspective.

Your role is to:
1. Help patients understand their insurance claim status, approvals, and benefit decisions
2. Explain what benefits were approved, what amounts were covered, and any deductions applied
3. Clarify why certain policy rules affected their claim outcome
4. Provide clear explanations about coverage decisions and financial impacts
5. Focus on what the patient needs to know about their claim

Important Guidelines:
- Always respond from the patient's perspective - explain what happened to THEIR claim
- Use simple, patient-friendly language and avoid technical insurance jargon
- Focus on outcomes that matter to the patient: approved amounts, coverage decisions, deductions
- Explain policy rules in terms of how they affected the patient's benefits
- Never discuss internal system processes, technical operations, or backend processing details
- If asked about system internals, redirect to patient-relevant information instead
- Highlight important financial information clearly (what was approved, what was deducted, final amounts)
- Use bullet points and clear formatting to make information easy to understand

Always base your answers strictly on the provided claim data and focus on patient-relevant outcomes."""
    
    def _create_user_prompt(self, question: str, context: str) -> str:
        """Create patient-focused user prompt with question and context"""
        return f"""Based on the following information about the patient's insurance claim, please answer their question:

PATIENT'S CLAIM INFORMATION:
{context}

PATIENT'S QUESTION: {question}

Please provide a clear, patient-friendly answer that explains:
- What happened with their claim
- What benefits were approved or denied
- What amounts they can expect
- Why certain decisions were made based on their policy
- Any next steps they should be aware of

Focus only on information that is relevant and helpful to the patient. Avoid discussing internal system processes."""

    def _create_structured_system_prompt(self) -> str:
        """Create enhanced system prompt for structured claim data"""
        return """You are an expert insurance claims assistant with access to comprehensive, structured claim information.

You have access to complete claim data organized in the following sections:
1. **DeductionsInClaim**: All financial deductions including medical and non-medical charges
2. **BasicClaimInformation**: Core claim processing status and metadata
3. **AilmentSummary**: Medical condition and treatment details
4. **HospitalDetails**: Healthcare provider and treatment facility information
5. **PatientAndPolicyDetails**: Patient demographics and insurance policy information
6. **Deduction reasons**: Detailed rule processing explanations for why deductions were applied
7. **MissingDocuments**: Any outstanding documentation requirements

Your role is to:
- Provide comprehensive, accurate answers using this structured data
- Explain complex insurance concepts in simple, patient-friendly terms
- Connect financial impacts to specific medical treatments and policy rules
- Help patients understand the complete picture of their claim processing
- Reference specific amounts, dates, and policy details when relevant
- Explain the reasoning behind deductions and coverage decisions

Guidelines:
- Always use the structured data to provide complete, accurate answers
- Reference specific sections and amounts when explaining financial impacts
- Connect deductions to their underlying reasons and policy rules
- Be empathetic and focus on what matters most to the patient
- If asked about specific aspects, dive deep into the relevant data sections
- Provide actionable next steps when appropriate

Base all answers strictly on the provided structured claim data."""

    def _create_structured_user_prompt(self, question: str, context: str) -> str:
        """Create user prompt for structured claim data"""
        return f"""Based on the comprehensive structured claim information below, please answer the patient's question:

COMPREHENSIVE CLAIM DATA:
{context}

PATIENT'S QUESTION: {question}

Please provide a detailed, patient-friendly answer that:
- Uses specific information from the structured data sections
- Explains financial impacts with exact amounts and reasons
- References relevant policy details and medical information
- Connects deductions to their underlying explanations
- Provides a complete picture of what happened with their claim
- Focuses on what matters most to the patient

Use the structured data to give the most comprehensive and accurate answer possible."""

    def answer_question_with_structured_data(self, question: str, structured_data: Dict[str, Any],
                                           claim_no: Optional[int] = None) -> Dict[str, Any]:
        """Answer a question using comprehensive structured claim data"""
        try:
            if not structured_data:
                return {
                    "question": question,
                    "answer": "No structured claim data available to answer your question.",
                    "data_source": "structured_claim_data",
                    "confidence_score": 0.0
                }

            # Create comprehensive context from structured data
            context = self._create_context_from_structured_data(structured_data, claim_no)

            # Create prompts for structured data
            system_prompt = self._create_structured_system_prompt()
            user_prompt = self._create_structured_user_prompt(question, context)
            full_prompt = f"{system_prompt}\n\n{user_prompt}"

            # Call Gemini API
            response = self.model.generate_content(
                full_prompt,
                generation_config=genai.types.GenerationConfig(
                    max_output_tokens=self.max_tokens,
                    temperature=0.1,  # Low temperature for factual responses
                    top_p=0.9
                )
            )

            answer = response.text.strip()

            # Calculate confidence score based on data completeness
            data_sections = ['DeductionsInClaim', 'BasicClaimInformation', 'AilmentSummary',
                           'HospitalDetails', 'PatientAndPolicyDetails', 'Deduction reasons']
            available_sections = sum(1 for section in data_sections if section in structured_data)
            confidence_score = available_sections / len(data_sections)

            return {
                "question": question,
                "answer": answer,
                "data_source": "structured_claim_data",
                "available_sections": available_sections,
                "total_sections": len(data_sections),
                "confidence_score": confidence_score,
                "claim_no": claim_no
            }

        except Exception as e:
            logger.error(f"Error answering question with structured data: {str(e)}")
            return {
                "question": question,
                "answer": f"I encountered an error while processing your question: {str(e)}",
                "data_source": "structured_claim_data",
                "confidence_score": 0.0,
                "error": str(e)
            }

    def answer_question(self, question: str, claim_no: Optional[int] = None,
                       max_results: int = 5) -> Dict[str, Any]:
        """Answer a question based on the claims data"""
        try:
            # Retrieve relevant chunks
            relevant_chunks = self.vector_store.search_similar(
                query=question,
                claim_no=claim_no,
                max_results=max_results
            )
            
            if not relevant_chunks:
                return {
                    "question": question,
                    "answer": "I couldn't find any relevant information in the claims data to answer your question. Please make sure the claim data has been processed and stored.",
                    "relevant_chunks": [],
                    "confidence_score": 0.0
                }
            
            # Create context from chunks
            context = self._create_context_from_chunks(relevant_chunks)
            
            # Create prompt for Gemini
            system_prompt = self._create_system_prompt()
            user_prompt = self._create_user_prompt(question, context)
            full_prompt = f"{system_prompt}\n\n{user_prompt}"

            # Call Gemini API
            response = self.model.generate_content(
                full_prompt,
                generation_config=genai.types.GenerationConfig(
                    max_output_tokens=self.max_tokens,
                    temperature=0.1,  # Low temperature for factual responses
                    top_p=0.9
                )
            )

            answer = response.text.strip()
            
            # Calculate confidence score based on similarity scores
            avg_similarity = sum(chunk.get('similarity_score', 0) for chunk in relevant_chunks) / len(relevant_chunks)
            
            # Prepare chunk summaries for response
            chunk_summaries = []
            for chunk in relevant_chunks:
                chunk_summaries.append({
                    "claim_no": chunk.get('claim_no'),
                    "chunk_type": chunk.get('chunk_type'),
                    "similarity_score": chunk.get('similarity_score'),
                    "preview": chunk.get('content', '')[:200] + "..." if len(chunk.get('content', '')) > 200 else chunk.get('content', '')
                })
            
            return {
                "question": question,
                "answer": answer,
                "relevant_chunks": chunk_summaries,
                "confidence_score": avg_similarity
            }
            
        except Exception as e:
            logger.error(f"Error answering question: {str(e)}")
            return {
                "question": question,
                "answer": f"I encountered an error while processing your question: {str(e)}",
                "relevant_chunks": [],
                "confidence_score": 0.0
            }
    
    def get_claim_summary(self, claim_no: int) -> Dict[str, Any]:
        """Get a comprehensive summary of a specific claim"""
        try:
            # Get all chunks for the claim
            claim_chunks = self.vector_store.get_claim_chunks(claim_no)
            
            if not claim_chunks:
                return {
                    "claim_no": claim_no,
                    "summary": f"No data found for claim number {claim_no}",
                    "details": {}
                }
            
            # Create context from all chunks
            context = self._create_context_from_chunks(claim_chunks)
            
            # Create summary prompt
            summary_question = f"Please provide a comprehensive summary of claim {claim_no}, including key decisions, financial impacts, and important rule applications."

            system_prompt = self._create_system_prompt()
            user_prompt = self._create_user_prompt(summary_question, context)
            full_prompt = f"{system_prompt}\n\n{user_prompt}"

            # Call Gemini API
            response = self.model.generate_content(
                full_prompt,
                generation_config=genai.types.GenerationConfig(
                    max_output_tokens=self.max_tokens,
                    temperature=0.1
                )
            )

            summary = response.text.strip()
            
            # Analyze chunk types and counts
            chunk_analysis = {}
            for chunk in claim_chunks:
                chunk_type = chunk.get('chunk_type', 'unknown')
                chunk_analysis[chunk_type] = chunk_analysis.get(chunk_type, 0) + 1
            
            return {
                "claim_no": claim_no,
                "summary": summary,
                "details": {
                    "total_chunks": len(claim_chunks),
                    "chunk_types": chunk_analysis,
                    "data_available": True
                }
            }
            
        except Exception as e:
            logger.error(f"Error generating claim summary: {str(e)}")
            return {
                "claim_no": claim_no,
                "summary": f"Error generating summary: {str(e)}",
                "details": {"error": str(e)}
            }
    
    def suggest_questions(self, claim_no: Optional[int] = None) -> List[str]:
        """Suggest patient-relevant questions based on available data"""
        base_questions = [
            "How much was approved for my claim?",
            "What deductions were applied to my claim and why?",
            "What benefits were covered under my policy?",
            "What was the final amount I will receive?",
            "Were there any policy restrictions that affected my claim?",
            "What medical expenses were covered?",
            "What non-medical expenses were approved?",
            "Why was my claim amount different from what I expected?",
            "Are there any waiting period restrictions on my policy?",
            "What network restrictions applied to my claim?"
        ]
        
        if claim_no:
            # Get claim-specific data to suggest more targeted questions
            try:
                claim_chunks = self.vector_store.get_claim_chunks(claim_no)
                if claim_chunks:
                    # Analyze available data to suggest specific questions
                    categories = set()
                    has_financial_data = False
                    
                    for chunk in claim_chunks:
                        metadata = chunk.get('metadata', {})
                        if metadata.get('category'):
                            categories.add(metadata['category'])
                        if metadata.get('has_financial_impact'):
                            has_financial_data = True
                    
                    specific_questions = []
                    for category in categories:
                        specific_questions.append(f"What {category} rules were applied to claim {claim_no}?")
                    
                    if has_financial_data:
                        specific_questions.append(f"What was the detailed financial breakdown for claim {claim_no}?")
                    
                    return specific_questions + base_questions[:5]
            except:
                pass
        
        return base_questions

    def get_structured_claim_data(self, claim_no: int) -> Optional[Dict[str, Any]]:
        """Get structured claim data from debug files or processed data"""
        try:
            import glob
            import json
            import os

            # Look for debug files with structured claim data
            debug_files = glob.glob("debug_claims_response_*.json")
            if not debug_files:
                logger.warning(f"No debug files found for structured claim data")
                return None

            # Get the most recent debug file
            latest_debug_file = max(debug_files, key=os.path.getctime)

            with open(latest_debug_file, 'r', encoding='utf-8') as f:
                structured_data = json.load(f)

            # Validate that it has the expected structure
            expected_keys = ['DeductionsInClaim', 'BasicClaimInformation', 'AilmentSummary',
                           'HospitalDetails', 'PatientAndPolicyDetails', 'Deduction reasons']

            if any(key in structured_data for key in expected_keys):
                logger.info(f"Found structured claim data with {len([k for k in expected_keys if k in structured_data])} sections")
                return structured_data
            else:
                logger.warning(f"Debug file does not contain expected structured data format")
                return None

        except Exception as e:
            logger.error(f"Error loading structured claim data: {str(e)}")
            return None

    def answer_question_smart(self, question: str, claim_no: Optional[int] = None,
                             max_results: int = 5) -> Dict[str, Any]:
        """Smart question answering that tries structured data first, falls back to chunks"""
        try:
            # First, try to get structured claim data
            if claim_no:
                structured_data = self.get_structured_claim_data(claim_no)
                if structured_data:
                    logger.info(f"Using structured data for claim {claim_no}")
                    return self.answer_question_with_structured_data(question, structured_data, claim_no)

            # Fall back to chunk-based approach
            logger.info(f"Using chunk-based approach for question: {question}")
            return self.answer_question(question, claim_no, max_results)

        except Exception as e:
            logger.error(f"Error in smart question answering: {str(e)}")
            return {
                "question": question,
                "answer": f"I encountered an error while processing your question: {str(e)}",
                "data_source": "error",
                "confidence_score": 0.0
            }
