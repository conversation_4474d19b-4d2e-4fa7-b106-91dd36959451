import google.generativeai as genai
from typing import List, Dict, Any, Optional
import logging
from config import settings
from vector_store import VectorStore

logger = logging.getLogger(__name__)

class QAEngine:
    def __init__(self):
        self.vector_store = VectorStore()
        genai.configure(api_key=settings.GEMINI_API_KEY)
        self.model = genai.GenerativeModel(settings.GEMINI_MODEL)
        self.max_tokens = settings.MAX_TOKENS
    
    def _create_context_from_chunks(self, chunks: List[Dict[str, Any]]) -> str:
        """Create context string from retrieved chunks"""
        if not chunks:
            return "No relevant information found."
        
        context_parts = []
        for i, chunk in enumerate(chunks, 1):
            content = chunk.get('content', '')
            chunk_type = chunk.get('chunk_type', 'unknown')
            claim_no = chunk.get('claim_no', 'unknown')
            
            context_parts.append(f"--- Context {i} (Claim: {claim_no}, Type: {chunk_type}) ---")
            context_parts.append(content)
            context_parts.append("")
        
        return "\n".join(context_parts)
    
    def _create_system_prompt(self) -> str:
        """Create system prompt for the LLM"""
        return """You are an expert insurance claims analyst. You help users understand complex insurance claim processing data, rules, and decisions.

Your role is to:
1. Analyze insurance claim processing logs and rule engine data
2. Explain claim decisions, deductions, and approvals in simple terms
3. Help users understand why certain rules were applied or not applied
4. Provide insights into financial impacts and policy configurations
5. Answer questions about claim processing workflows and outcomes

Guidelines:
- Be precise and factual based on the provided context
- Explain technical insurance terms in simple language
- Highlight important financial impacts (amounts, deductions, approvals)
- If information is not available in the context, clearly state that
- Focus on the specific claim data provided
- Use bullet points and clear formatting for complex information

Always base your answers strictly on the provided context data."""
    
    def _create_user_prompt(self, question: str, context: str) -> str:
        """Create user prompt with question and context"""
        return f"""Based on the following insurance claim processing data, please answer the user's question:

CONTEXT DATA:
{context}

USER QUESTION: {question}

Please provide a comprehensive answer based on the context data above. If the context doesn't contain enough information to fully answer the question, please state what information is missing."""
    
    def answer_question(self, question: str, claim_no: Optional[int] = None, 
                       max_results: int = 5) -> Dict[str, Any]:
        """Answer a question based on the claims data"""
        try:
            # Retrieve relevant chunks
            relevant_chunks = self.vector_store.search_similar(
                query=question,
                claim_no=claim_no,
                max_results=max_results
            )
            
            if not relevant_chunks:
                return {
                    "question": question,
                    "answer": "I couldn't find any relevant information in the claims data to answer your question. Please make sure the claim data has been processed and stored.",
                    "relevant_chunks": [],
                    "confidence_score": 0.0
                }
            
            # Create context from chunks
            context = self._create_context_from_chunks(relevant_chunks)
            
            # Create prompt for Gemini
            system_prompt = self._create_system_prompt()
            user_prompt = self._create_user_prompt(question, context)
            full_prompt = f"{system_prompt}\n\n{user_prompt}"

            # Call Gemini API
            response = self.model.generate_content(
                full_prompt,
                generation_config=genai.types.GenerationConfig(
                    max_output_tokens=self.max_tokens,
                    temperature=0.1,  # Low temperature for factual responses
                    top_p=0.9
                )
            )

            answer = response.text.strip()
            
            # Calculate confidence score based on similarity scores
            avg_similarity = sum(chunk.get('similarity_score', 0) for chunk in relevant_chunks) / len(relevant_chunks)
            
            # Prepare chunk summaries for response
            chunk_summaries = []
            for chunk in relevant_chunks:
                chunk_summaries.append({
                    "claim_no": chunk.get('claim_no'),
                    "chunk_type": chunk.get('chunk_type'),
                    "similarity_score": chunk.get('similarity_score'),
                    "preview": chunk.get('content', '')[:200] + "..." if len(chunk.get('content', '')) > 200 else chunk.get('content', '')
                })
            
            return {
                "question": question,
                "answer": answer,
                "relevant_chunks": chunk_summaries,
                "confidence_score": avg_similarity
            }
            
        except Exception as e:
            logger.error(f"Error answering question: {str(e)}")
            return {
                "question": question,
                "answer": f"I encountered an error while processing your question: {str(e)}",
                "relevant_chunks": [],
                "confidence_score": 0.0
            }
    
    def get_claim_summary(self, claim_no: int) -> Dict[str, Any]:
        """Get a comprehensive summary of a specific claim"""
        try:
            # Get all chunks for the claim
            claim_chunks = self.vector_store.get_claim_chunks(claim_no)
            
            if not claim_chunks:
                return {
                    "claim_no": claim_no,
                    "summary": f"No data found for claim number {claim_no}",
                    "details": {}
                }
            
            # Create context from all chunks
            context = self._create_context_from_chunks(claim_chunks)
            
            # Create summary prompt
            summary_question = f"Please provide a comprehensive summary of claim {claim_no}, including key decisions, financial impacts, and important rule applications."

            system_prompt = self._create_system_prompt()
            user_prompt = self._create_user_prompt(summary_question, context)
            full_prompt = f"{system_prompt}\n\n{user_prompt}"

            # Call Gemini API
            response = self.model.generate_content(
                full_prompt,
                generation_config=genai.types.GenerationConfig(
                    max_output_tokens=self.max_tokens,
                    temperature=0.1
                )
            )

            summary = response.text.strip()
            
            # Analyze chunk types and counts
            chunk_analysis = {}
            for chunk in claim_chunks:
                chunk_type = chunk.get('chunk_type', 'unknown')
                chunk_analysis[chunk_type] = chunk_analysis.get(chunk_type, 0) + 1
            
            return {
                "claim_no": claim_no,
                "summary": summary,
                "details": {
                    "total_chunks": len(claim_chunks),
                    "chunk_types": chunk_analysis,
                    "data_available": True
                }
            }
            
        except Exception as e:
            logger.error(f"Error generating claim summary: {str(e)}")
            return {
                "claim_no": claim_no,
                "summary": f"Error generating summary: {str(e)}",
                "details": {"error": str(e)}
            }
    
    def suggest_questions(self, claim_no: Optional[int] = None) -> List[str]:
        """Suggest relevant questions based on available data"""
        base_questions = [
            "What was the total approved amount for this claim?",
            "What deductions were applied and why?",
            "Which rules were executed during claim processing?",
            "What was the financial impact of each rule category?",
            "Were there any repudiation rules applied?",
            "What non-medical expenses were processed?",
            "What configuration rules matched this claim?",
            "What was the pre and post execution difference in amounts?",
            "Were there any waiting period restrictions?",
            "What hospital and claim type restrictions were applied?"
        ]
        
        if claim_no:
            # Get claim-specific data to suggest more targeted questions
            try:
                claim_chunks = self.vector_store.get_claim_chunks(claim_no)
                if claim_chunks:
                    # Analyze available data to suggest specific questions
                    categories = set()
                    has_financial_data = False
                    
                    for chunk in claim_chunks:
                        metadata = chunk.get('metadata', {})
                        if metadata.get('category'):
                            categories.add(metadata['category'])
                        if metadata.get('has_financial_impact'):
                            has_financial_data = True
                    
                    specific_questions = []
                    for category in categories:
                        specific_questions.append(f"What {category} rules were applied to claim {claim_no}?")
                    
                    if has_financial_data:
                        specific_questions.append(f"What was the detailed financial breakdown for claim {claim_no}?")
                    
                    return specific_questions + base_questions[:5]
            except:
                pass
        
        return base_questions
