{"data": [{"claimNo": 131420765, "logs": [{"data": [], "logCreatedDate": "/Date(1749199259699)/", "typeOfLog": "ClaimSecondLevelApproval", "ruleEngineLogs": []}, {"data": [{"timeOfOccurrence": "/Date(1749199259363+0530)/", "message": "Setting Claim automation status started", "loggerType": "0"}, {"timeOfOccurrence": "/Date(1749199259363+0530)/", "message": "Status:Reject, Remarks:No records found for this configuration", "loggerType": "0"}], "logCreatedDate": "/Date(1749199259350)/", "typeOfLog": "Auomation", "ruleEngineLogs": []}, {"data": [], "logCreatedDate": "/Date(1749199259050)/", "typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": [{"timeOfOccurrence": "/Date(1749199258368+0530)/", "category": "BaseConfiguration", "subCategory": "DefautChecks", "subCategoryDisplayName": "Default Configuration", "loggerType": "Information", "logMessages": [], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 0.0, "postExectionTotalDeductionAmount": 0.0, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258369+0530)/", "category": "BaseConfiguration", "subCategory": "PolicyConfigIncaseOfDeath", "subCategoryDisplayName": "Exception On Death Case", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290307+0530)/", "message": "Exception On Death Case - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 0.0, "postExectionTotalDeductionAmount": 0.0, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258370+0530)/", "category": "BaseConfiguration", "subCategory": "OverideDiscount", "subCategoryDisplayName": "Override Discount By Claim Type", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290307+0530)/", "message": "Override Discount By <PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON> not executed as no matching rule found.", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749205290307+0530)/", "message": "Override Discount By Claim Type - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 0.0, "postExectionTotalDeductionAmount": 0.0, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258371+0530)/", "category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "loggerType": "Information", "matchingConfig": [{"claimType": "None"}, {"claimType": "PreAuth"}, {"claimType": "Cashless"}], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 145307.6, "postExectionTotalDeductionAmount": 0.0, "configRuleLogObject": [{"displayName": "ClaimType", "value": "None", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "ClaimType", "value": "PreAuth", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "ClaimType", "value": "Cashless", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}]}, {"timeOfOccurrence": "/Date(1749199258379+0530)/", "category": "NonMedicalExpense", "subCategory": "ManualOverRideBills", "subCategoryDisplayName": "Manual OverRide Bills", "loggerType": "Information", "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 145307.6, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258379+0530)/", "category": "NonMedicalExpense", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "loggerType": "Information", "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 145307.6, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258387+0530)/", "category": "NonMedicalExpense", "subCategory": "HandelNMEByPreAuthType", "subCategoryDisplayName": "Estimate For Initial", "loggerType": "Information", "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258387+0530)/", "category": "NonMedicalExpense", "subCategory": "CapAmbulanceCharges", "subCategoryDisplayName": "Ambulance Limit By Amount", "loggerType": "Information", "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258387+0530)/", "category": "NonMedicalExpense", "subCategory": "MarkAllNMEsAsPayable", "subCategoryDisplayName": "Pay All NMEs", "loggerType": "Information", "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258390+0530)/", "category": "NonMedicalExpense", "subCategory": "RIPrePostNMEDeductions", "subCategoryDisplayName": "Reimbursement Pre-Post Deductions", "loggerType": "Information", "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258390+0530)/", "category": "NonMedicalExpense", "subCategory": "VariantNotConfigured", "subCategoryDisplayName": "Condition To Be Reviewed", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290308+0530)/", "message": "Condition To Be Reviewed - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258390+0530)/", "category": "NonMedicalExpense", "subCategory": "OverrideNMEParticularsAmount", "subCategoryDisplayName": "NME limit by Subcharge", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290308+0530)/", "message": "Limit applicable subcharge head: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749205290308+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749205290308+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749205290308+0530)/", "message": "NME limit by Subcharge - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749205290308+0530)/", "message": "NME limit by Subcharge - Execution ends", "loggerType": "Information"}], "matchingConfig": [{"nmeConfigId": "0", "schemeId": "0", "policyId": "0", "payableType": "<PERSON>", "payableSubType": "Air Ambulance Charges", "isPayable": "True", "payableLimit": "100000", "quatityLimit": "-1", "quantityPriceLimit": "-1", "payablePercentageLimit": "0", "isLower": "False", "deductionLevel": "<PERSON><PERSON><PERSON>", "isActive": "True"}], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 119565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "NMEConfigId", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false}, {"displayName": "SchemeId", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false}, {"displayName": "PolicyId", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false}, {"displayName": "PayableType", "value": "<PERSON>", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "PayableSubType", "value": "Air Ambulance Charges", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "IsPayable", "value": "Applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "PayableLimit", "value": "100000", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "QuatityLimit", "value": "-1", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "QuantityPriceLimit", "value": "-1", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "PayablePercentageLimit", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "DeductionLevel", "value": "<PERSON><PERSON><PERSON>", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "IsActive", "value": "Applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}]}, {"timeOfOccurrence": "/Date(1749199258392+0530)/", "category": "NonMedicalExpense", "subCategory": "ManualOverRideBills", "subCategoryDisplayName": "Manual OverRide Bills", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290308+0530)/", "message": "Manual OverRide Bills - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258393+0530)/", "category": "Repudiation", "subCategory": "ExclusionByGenderAilment", "subCategoryDisplayName": "Gender and ailment", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290308+0530)/", "message": "Gender and ailment - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258394+0530)/", "category": "Repudiation", "subCategory": "OnNumberOfChildren", "subCategoryDisplayName": "Number of living children allowed for maternity coverage", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290308+0530)/", "message": "Number of living children allowed for maternity coverage - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258395+0530)/", "category": "Repudiation", "subCategory": "ExclusionByAilment", "subCategoryDisplayName": "Repudiation By Ailment Category", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290308+0530)/", "message": "Repudiation By Ailment Category - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258428+0530)/", "category": "Repudiation", "subCategory": "RepudiationByRefractiveError", "subCategoryDisplayName": "Refractive Error", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290308+0530)/", "message": "Refractive Error - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258429+0530)/", "category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290308+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749205290308+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749205290308+0530)/", "message": "Waiting Period for Pre-existing Ailments - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749205290308+0530)/", "message": "Waiting Period for Pre-existing Ailments - Execution ends", "loggerType": "Information"}], "matchingConfig": [{"relation to Primary beneficiary": "Father, <PERSON><PERSON><PERSON>, Mother, <PERSON><PERSON><PERSON>", "grade": "any", "check PED Applicable For Beneficiary": "True", "skip For Demise": "False", "skip For Emergency Admission": "False", "number of days for waiting period": "365", "benefitScope": "Policy"}], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 119565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "Relation to Primary beneficiary", "value": "Father,<PERSON><PERSON><PERSON>,Mother,<PERSON><PERSON><PERSON>", "isFilterApplicable": true, "defaultValue": "ALL", "claimValue": "<PERSON><PERSON><PERSON>", "isToBeDisplayed": true}, {"displayName": "Grade", "value": "any", "isFilterApplicable": true, "defaultValue": "ANY", "claimValue": "Gold Plus", "isToBeDisplayed": true}, {"displayName": "Check PED Applicable For Beneficiary", "value": "Applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Skip For Demise", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Skip For Emergency Admission", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Number of days for waiting period", "value": "365", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "BenefitScope", "value": "Policy", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false}]}, {"timeOfOccurrence": "/Date(1749199258431+0530)/", "category": "Repudiation", "subCategory": "DenyObesity", "subCategoryDisplayName": "Obesity", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290308+0530)/", "message": "Obesity - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258433+0530)/", "category": "Repudiation", "subCategory": "WaitingPeriodByAilmentsCategory", "subCategoryDisplayName": "Waiting Period by Ailment category", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290308+0530)/", "message": "Waiting Period by Ailment category - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258467+0530)/", "category": "Repudiation", "subCategory": "ExclusionByProcedures", "subCategoryDisplayName": "Repudiation By Procedure", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290308+0530)/", "message": "Repudiation By Procedure - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258467+0530)/", "category": "Repudiation", "subCategory": "WaitingPeriodByProcedures", "subCategoryDisplayName": "Waiting Period by Procedure", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290308+0530)/", "message": "Waiting Period by Procedure - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258468+0530)/", "category": "Repudiation", "subCategory": "RepudiationByHospital", "subCategoryDisplayName": "Repudiation By Hospital And Claim Type", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290309+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749205290309+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749205290309+0530)/", "message": "Repudiation By Hospital And Claim Type - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749205290309+0530)/", "message": "Repudiation By Hospital And Claim Type - Execution ends", "loggerType": "Information"}], "matchingConfig": [{"relation To Primary Beneficiary": "ALL", "grade": "gold, gold plus, gold plus_r, gold_r, silver, silver_r, platinum, platinum_r", "claim type": "Cashless, PreAuth", "skip For Network Hospital": "False", "skip For PPN Hospital": "False", "hospital": "287585", "skip for Demise": "False", "benefitScope": "Policy"}], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 119565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "Relation To Primary Beneficiary", "value": "ALL", "isFilterApplicable": true, "defaultValue": "ALL", "claimValue": "<PERSON><PERSON><PERSON>", "isToBeDisplayed": true}, {"displayName": "Grade", "value": "gold,gold plus,gold plus_r,gold_r,silver,silver_r,platinum,platinum_r", "isFilterApplicable": true, "defaultValue": "ANY", "claimValue": "Gold Plus", "isToBeDisplayed": true}, {"displayName": "Claim type", "value": "Cashless,PreAuth", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "<PERSON><PERSON> For Network Hospital", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Skip For PPN Hospital", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Hospital", "value": "287585", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Skip for Demise", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "BenefitScope", "value": "Policy", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false}]}, {"timeOfOccurrence": "/Date(1749199258470+0530)/", "category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "loggerType": "Information", "matchingConfig": [{"claimType": "PreAuth"}, {"claimType": "Cashless"}], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 119565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "ClaimType", "value": "PreAuth", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "ClaimType", "value": "Cashless", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}]}, {"timeOfOccurrence": "/Date(1749199258471+0530)/", "category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "loggerType": "Information", "matchingConfig": [{"type of  Room": "Normal", "provider Type": "False", "propotionateApplicable": "True", "skip Proportionate On DeathCase": "False", "skip Proportionate On AccidentCase": "False", "topUp Configured": "False", "relation to Primary beneficiary": "Father, <PERSON><PERSON><PERSON>, Mother, <PERSON><PERSON><PERSON>", "grade": "gold plus, gold", "min Age": "0", "max Age": "0", "room Value Limit Defined By": "Amount", "room Value Type": "5802.0", "room Capped Limit": "0", "proportionate Limit Defined By": "Amount", "proportionate Limit Defined By Value": "0", "min SI": "0", "max SI": "0", "benefitScope": "Policy"}], "preExecutionApprovedAmount": 110163.96, "preExecutionTotalBillAmount": 119565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 110163.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "Type of  Room", "value": "Normal", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Provider Type", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "PropotionateApplicable", "value": "Applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Skip Proportionate On DeathCase", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Skip Proportionate On AccidentCase", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "TopUp Configured", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Relation to Primary beneficiary", "value": "Father,<PERSON><PERSON><PERSON>,Mother,<PERSON><PERSON><PERSON>", "isFilterApplicable": true, "defaultValue": "ALL", "claimValue": "<PERSON><PERSON><PERSON>", "isToBeDisplayed": true}, {"displayName": "Grade", "value": "gold plus,gold", "isFilterApplicable": true, "defaultValue": "ANY", "claimValue": "Gold Plus", "isToBeDisplayed": true}, {"displayName": "Min Age", "value": "0", "isFilterApplicable": true, "defaultValue": "", "claimValue": "61", "isToBeDisplayed": false}, {"displayName": "Max Age", "value": "0", "isFilterApplicable": true, "defaultValue": "", "claimValue": "61", "isToBeDisplayed": false}, {"displayName": "Room Value Limit Defined By", "value": "Amount", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Room Value Type", "value": "5802.0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Room Capped Limit", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false}, {"displayName": "Proportionate Limit Defined By", "value": "Amount", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Proportionate Limit Defined By Value", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false}, {"displayName": "Min SI", "value": "0", "isFilterApplicable": true, "defaultValue": "", "claimValue": "1600000.0", "isToBeDisplayed": false}, {"displayName": "Max SI", "value": "0", "isFilterApplicable": true, "defaultValue": "", "claimValue": "1600000.0", "isToBeDisplayed": false}, {"displayName": "BenefitScope", "value": "Policy", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false}]}, {"timeOfOccurrence": "/Date(1749199258486+0530)/", "category": "PropotinateDeduction", "subCategory": "VariantNotConfigured", "subCategoryDisplayName": "Condition To Be Reviewed", "loggerType": "Information", "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 103439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258487+0530)/", "category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "loggerType": "Information", "matchingConfig": [{"relation To Primary Beneficiary": "ALL", "grade": "gold, gold plus, platinum, silver", "claim type": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>", "intimation Days": "3", "copay Defined By": "PecentageOfAdmissibleAmount", "copay Defined Value(Percentage/Amount)": "10", "upper/Lower Copay Limit For Comparison": "0", "skip For Emergency Admission": "True", "is Min/Max Copay Applicable Per Person": "False", "is Copay Applicable Per Admission Basis": "False", "copay Limit Applicable Per Family": "False", "dedution Remarks": "INTIMATION_COPAY", "benefitScope": "Policy"}], "preExecutionApprovedAmount": 103439.96, "preExecutionTotalBillAmount": 112842.58, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 103439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "Relation To Primary Beneficiary", "value": "ALL", "isFilterApplicable": true, "defaultValue": "ALL", "claimValue": "<PERSON><PERSON><PERSON>", "isToBeDisplayed": true}, {"displayName": "Grade", "value": "gold,gold plus,platinum,silver", "isFilterApplicable": true, "defaultValue": "ANY", "claimValue": "Gold Plus", "isToBeDisplayed": true}, {"displayName": "Claim type", "value": "<PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>", "isFilterApplicable": true, "defaultValue": "None", "claimValue": "PreAuth", "isToBeDisplayed": true}, {"displayName": "Intimation Days", "value": "3", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Copay Defined By", "value": "PecentageOfAdmissibleAmount", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Copay Defined Value(Percentage/Amount)", "value": "10", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Upper/Lower Copay Limit For Comparison", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false}, {"displayName": "Skip For Emergency Admission", "value": "Applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Is Min/Max Copay Applicable Per Person", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Is Copay Applicable Per Admission Basis", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Copay Limit Applicable Per Family", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Dedution Remarks", "value": "INTIMATION_COPAY", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "BenefitScope", "value": "Policy", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false}]}, {"timeOfOccurrence": "/Date(1749199258490+0530)/", "category": "Copay", "subCategory": "CopayOnNonNetwork", "subCategoryDisplayName": "Non-Network Hospital", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290309+0530)/", "message": "Non-Network Hospital - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 103439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258494+0530)/", "category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "loggerType": "Information", "matchingConfig": [{"relationship": "ALL", "grade": "gold plus, gold plus_r", "minimum Age": "0", "maximum Age": "0", "min SI": "0", "max SI": "0", "min Claim amount": "0", "max Claim amount": "0", "min Admissible amount": "0", "max Admissible amount": "0", "exculding Procedures": "3119, 3163, 3165, 3467, 3861, 4121, 4430, 4436, 4513, 4560, 4591, 4599, 4671", "hospital Network/Non-Network": "False", "copay Type": "MinOfPercentageOfAdmissibleAndCappedAmount", "copay Percentage": "10", "capped Value": "10000", "skip For Emergency Admission": "False", "applicable on claim event basis": "True", "is Family Floater": "False", "applicable on claim Member basis": "False", "no. of Days": "0", "check PED Applicable For Beneficiary": "False", "isCopayDueToAnyoneIllness": "False", "exculdingAilmentsCategoryOrAilment": "IVF_FOR_INFERTILITY,INFERTILITY,PSYCHIATRIC,GENETICDISORDER,M<PERSON><PERSON><PERSON>LE_SCLEROSIS,MATERNITY_COMPLICATIONS,MATERNITY_MULTIPLE_DELIVERY,MATER<PERSON><PERSON>_NORMAL,MATE<PERSON><PERSON>TY_LSCS,TWIN_DELIVERY,TRIPLET_DELIVERY,MORE_THAN_TRIPLET_DELIVERY,PRE_AND_POST_NATAL,Mental_disorder", "dedution Remarks": "MANDATORY_COPAY", "sum Insured Utilization": "0", "benefitScope": "Policy"}], "preExecutionApprovedAmount": 103439.96, "preExecutionTotalBillAmount": 112842.58, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 103439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "Relationship", "value": "ALL", "isFilterApplicable": true, "defaultValue": "ALL", "claimValue": "<PERSON><PERSON><PERSON>", "isToBeDisplayed": true}, {"displayName": "Grade", "value": "gold plus,gold plus_r", "isFilterApplicable": true, "defaultValue": "ANY", "claimValue": "Gold Plus", "isToBeDisplayed": true}, {"displayName": "Minimum Age", "value": "0", "isFilterApplicable": true, "defaultValue": "", "claimValue": "61", "isToBeDisplayed": false}, {"displayName": "Maximum Age", "value": "0", "isFilterApplicable": true, "defaultValue": "", "claimValue": "61", "isToBeDisplayed": false}, {"displayName": "Min SI", "value": "0", "isFilterApplicable": true, "defaultValue": "", "claimValue": "1600000.0", "isToBeDisplayed": false}, {"displayName": "Max SI", "value": "0", "isFilterApplicable": true, "defaultValue": "", "claimValue": "1600000.0", "isToBeDisplayed": false}, {"displayName": "Min Claim amount", "value": "0", "isFilterApplicable": true, "defaultValue": "", "claimValue": "145307.0", "isToBeDisplayed": false}, {"displayName": "Max Claim amount", "value": "0", "isFilterApplicable": true, "defaultValue": "", "claimValue": "145307.0", "isToBeDisplayed": false}, {"displayName": "Min Admissible amount", "value": "0", "isFilterApplicable": true, "defaultValue": "", "claimValue": "112842", "isToBeDisplayed": false}, {"displayName": "Max Admissible amount", "value": "0", "isFilterApplicable": true, "defaultValue": "", "claimValue": "112842", "isToBeDisplayed": false}, {"displayName": "Exculding Procedures", "value": "3119,3163,3165,3467,3861,4121,4430,4436,4513,4560,4591,4599,4671", "isFilterApplicable": true, "defaultValue": "", "claimValue": "4564", "isToBeDisplayed": true}, {"displayName": "Hospital Network/Non-Network", "value": "Not applicable", "isFilterApplicable": true, "defaultValue": "", "claimValue": "True", "isToBeDisplayed": true}, {"displayName": "Copay Type", "value": "MinOfPercentageOfAdmissibleAndCappedAmount", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Copay Per<PERSON>age", "value": "10", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Capped Value", "value": "10000", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Skip For Emergency Admission", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Applicable on claim event basis", "value": "Applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Is Family Floater", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Applicable on claim Member basis", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "No. of Days", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false}, {"displayName": "Check PED Applicable For Beneficiary", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "isCopayDueToAnyoneIllness", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "ExculdingAilmentsCategoryOrAilment", "value": "IVF_FOR_INFERTILITY,INFERTILITY,PSYCHIATRIC,GENETICDISORDER,M<PERSON><PERSON><PERSON>LE_SCLEROSIS,MATERNITY_COMPLICATIONS,MATERNITY_MULTIPLE_DELIVERY,MATER<PERSON><PERSON>_NORMAL,MATE<PERSON><PERSON>TY_LSCS,TWIN_DELIVERY,TRIPLET_DELIVERY,MORE_THAN_TRIPLET_DELIVERY,PRE_AND_POST_NATAL,Mental_disorder", "isFilterApplicable": true, "defaultValue": "", "claimValue": "INFECTIOUS_BACTERIAL_VIRAL_MEDICAL_MGMT", "isToBeDisplayed": true}, {"displayName": "Dedution Remarks", "value": "MANDATORY_COPAY", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Sum Insured Utilization", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false}, {"displayName": "BenefitScope", "value": "Policy", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false}]}, {"timeOfOccurrence": "/Date(1749199258572+0530)/", "category": "Copay", "subCategory": "RelationGradeAdmissibleAmount", "subCategoryDisplayName": "Copay By Admissible Amount", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290309+0530)/", "message": "Copay By Admissible Amount - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258578+0530)/", "category": "Copay", "subCategory": "VariantNotConfigured", "subCategoryDisplayName": "Condition To Be Reviewed", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290309+0530)/", "message": "Condition To Be Reviewed - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258578+0530)/", "category": "Copay", "subCategory": "RelationGradeAilment", "subCategoryDisplayName": "Ailment Category", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290310+0530)/", "message": "Ailment Category - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258580+0530)/", "category": "AilmentCapping", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290310+0530)/", "message": "Standard (IRDAI) - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749205290310+0530)/", "message": "Standard (IRDAI) - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 93439.96, "preExecutionTotalBillAmount": 112842.58, "preExectionTotalDeductionAmount": 19402.04, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258580+0530)/", "category": "AilmentCapping", "subCategory": "AilmentCappingByGrade", "subCategoryDisplayName": "Ailment Category", "loggerType": "Information", "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258763+0530)/", "category": "AilmentCapping", "subCategory": "ProcedureCappingByApproachAndPostion", "subCategoryDisplayName": "Position and Approach", "loggerType": "Information", "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258781+0530)/", "category": "AilmentCapping", "subCategory": "AilmentProcedureCappingBySIType", "subCategoryDisplayName": "Ailment Category and Procedure Combination", "loggerType": "Information", "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258929+0530)/", "category": "AilmentCapping", "subCategory": "ProcedureCappingByGrade", "subCategoryDisplayName": "Procedure", "loggerType": "Information", "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258952+0530)/", "category": "AilmentCapping", "subCategory": "AilmentCappingByClaimType", "subCategoryDisplayName": "Ailment Capping by <PERSON><PERSON><PERSON>", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290310+0530)/", "message": "Ailment Capping by <PERSON><PERSON>m Type - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199259006+0530)/", "category": "AilmentCapping", "subCategory": "MaternityWellnessBabyCapping", "subCategoryDisplayName": "Maternity, wellness baby care & Pre-Post natal", "loggerType": "Information", "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199259047+0530)/", "category": "AilmentCapping", "subCategory": "VariantNotConfigured", "subCategoryDisplayName": "Condition To Be Reviewed", "loggerType": "Information", "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199259047+0530)/", "category": "SIRestriction", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "loggerType": "Information", "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199259047+0530)/", "category": "SIRestriction", "subCategory": "OtherHealthCareSIRestriction", "subCategoryDisplayName": "SI Restriction By Health Care Types", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290310+0530)/", "message": "SI Restriction By Health Care Types - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199259050+0530)/", "category": "PolicyBenefits", "subCategory": "NotApplicable", "subCategoryDisplayName": "Not Applicable", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290310+0530)/", "message": "Not Applicable - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199259050+0530)/", "category": "UCRDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290310+0530)/", "message": "*****Calculation On SOC Started*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749205290310+0530)/", "message": "NO SOC Details Found", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749205290310+0530)/", "message": "*****Calculation On SOC Ends*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749205290310+0530)/", "message": "Standard - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}], "claimRuleResults": {"denialType": "NA", "claimDenials": []}}, {"logCreatedDate": "/Date(1749199258082)/", "typeOfLog": "Decision", "ruleEngineLogs": [], "decisionRuleResults": {"_decisionResults": [{"status": "AUTOMATE", "reasons": ["Request To Execute Rule Engine"]}], "claimDenials": []}}, {"data": [], "logCreatedDate": "/Date(1749199183568)/", "typeOfLog": "ClaimSecondLevelApproval", "ruleEngineLogs": []}, {"data": [{"timeOfOccurrence": "/Date(1749199183303+0530)/", "message": "Setting Claim automation status started", "loggerType": "0"}, {"timeOfOccurrence": "/Date(1749199183303+0530)/", "message": "Status:Reject, Remarks:No records found for this configuration", "loggerType": "0"}], "logCreatedDate": "/Date(1749199183292)/", "typeOfLog": "Auomation", "ruleEngineLogs": []}, {"data": [], "logCreatedDate": "/Date(1749199182955)/", "typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": [{"timeOfOccurrence": "/Date(1749199182265+0530)/", "category": "BaseConfiguration", "subCategory": "DefautChecks", "subCategoryDisplayName": "Default Configuration", "loggerType": "Information", "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 0.0, "postExectionTotalDeductionAmount": 0.0, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182266+0530)/", "category": "BaseConfiguration", "subCategory": "PolicyConfigIncaseOfDeath", "subCategoryDisplayName": "Exception On Death Case", "loggerType": "Information", "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 0.0, "postExectionTotalDeductionAmount": 0.0, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182267+0530)/", "category": "BaseConfiguration", "subCategory": "OverideDiscount", "subCategoryDisplayName": "Override Discount By Claim Type", "loggerType": "Information", "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 0.0, "postExectionTotalDeductionAmount": 0.0, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182267+0530)/", "category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "loggerType": "Information", "matchingConfig": [{"claimType": "None"}, {"claimType": "PreAuth"}, {"claimType": "Cashless"}], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 145307.6, "postExectionTotalDeductionAmount": 0.0, "configRuleLogObject": [{"displayName": "ClaimType", "value": "None", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "ClaimType", "value": "PreAuth", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "ClaimType", "value": "Cashless", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}]}, {"timeOfOccurrence": "/Date(1749199182276+0530)/", "category": "NonMedicalExpense", "subCategory": "ManualOverRideBills", "subCategoryDisplayName": "Manual OverRide Bills", "loggerType": "Information", "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 145307.6, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182276+0530)/", "category": "NonMedicalExpense", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "loggerType": "Information", "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 145307.6, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182284+0530)/", "category": "NonMedicalExpense", "subCategory": "HandelNMEByPreAuthType", "subCategoryDisplayName": "Estimate For Initial", "loggerType": "Information", "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182284+0530)/", "category": "NonMedicalExpense", "subCategory": "CapAmbulanceCharges", "subCategoryDisplayName": "Ambulance Limit By Amount", "loggerType": "Information", "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182284+0530)/", "category": "NonMedicalExpense", "subCategory": "MarkAllNMEsAsPayable", "subCategoryDisplayName": "Pay All NMEs", "loggerType": "Information", "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182286+0530)/", "category": "NonMedicalExpense", "subCategory": "RIPrePostNMEDeductions", "subCategoryDisplayName": "Reimbursement Pre-Post Deductions", "loggerType": "Information", "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182286+0530)/", "category": "NonMedicalExpense", "subCategory": "VariantNotConfigured", "subCategoryDisplayName": "Condition To Be Reviewed", "loggerType": "Information", "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182286+0530)/", "category": "NonMedicalExpense", "subCategory": "OverrideNMEParticularsAmount", "subCategoryDisplayName": "NME limit by Subcharge", "loggerType": "Information", "matchingConfig": [{"nmeConfigId": "0", "schemeId": "0", "policyId": "0", "payableType": "<PERSON>", "payableSubType": "Air Ambulance Charges", "isPayable": "True", "payableLimit": "100000", "quatityLimit": "-1", "quantityPriceLimit": "-1", "payablePercentageLimit": "0", "isLower": "False", "deductionLevel": "<PERSON><PERSON><PERSON>", "isActive": "True"}], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 119565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "NMEConfigId", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false}, {"displayName": "SchemeId", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false}, {"displayName": "PolicyId", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false}, {"displayName": "PayableType", "value": "<PERSON>", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "PayableSubType", "value": "Air Ambulance Charges", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "IsPayable", "value": "Applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "PayableLimit", "value": "100000", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "QuatityLimit", "value": "-1", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "QuantityPriceLimit", "value": "-1", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "PayablePercentageLimit", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "DeductionLevel", "value": "<PERSON><PERSON><PERSON>", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "IsActive", "value": "Applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}]}, {"timeOfOccurrence": "/Date(1749199182289+0530)/", "category": "NonMedicalExpense", "subCategory": "ManualOverRideBills", "subCategoryDisplayName": "Manual OverRide Bills", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290311+0530)/", "message": "Manual OverRide Bills - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182289+0530)/", "category": "Repudiation", "subCategory": "ExclusionByGenderAilment", "subCategoryDisplayName": "Gender and ailment", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290311+0530)/", "message": "Gender and ailment - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182291+0530)/", "category": "Repudiation", "subCategory": "OnNumberOfChildren", "subCategoryDisplayName": "Number of living children allowed for maternity coverage", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290311+0530)/", "message": "Number of living children allowed for maternity coverage - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182292+0530)/", "category": "Repudiation", "subCategory": "ExclusionByAilment", "subCategoryDisplayName": "Repudiation By Ailment Category", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290311+0530)/", "message": "Repudiation By Ailment Category - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182324+0530)/", "category": "Repudiation", "subCategory": "RepudiationByRefractiveError", "subCategoryDisplayName": "Refractive Error", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290311+0530)/", "message": "Refractive Error - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182325+0530)/", "category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290311+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749205290311+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749205290311+0530)/", "message": "Waiting Period for Pre-existing Ailments - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749205290311+0530)/", "message": "Waiting Period for Pre-existing Ailments - Execution ends", "loggerType": "Information"}], "matchingConfig": [{"relation to Primary beneficiary": "Father, <PERSON><PERSON><PERSON>, Mother, <PERSON><PERSON><PERSON>", "grade": "any", "check PED Applicable For Beneficiary": "True", "skip For Demise": "False", "skip For Emergency Admission": "False", "number of days for waiting period": "365", "benefitScope": "Policy"}], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 119565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "Relation to Primary beneficiary", "value": "Father,<PERSON><PERSON><PERSON>,Mother,<PERSON><PERSON><PERSON>", "isFilterApplicable": true, "defaultValue": "ALL", "claimValue": "<PERSON><PERSON><PERSON>", "isToBeDisplayed": true}, {"displayName": "Grade", "value": "any", "isFilterApplicable": true, "defaultValue": "ANY", "claimValue": "Gold Plus", "isToBeDisplayed": true}, {"displayName": "Check PED Applicable For Beneficiary", "value": "Applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Skip For Demise", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Skip For Emergency Admission", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Number of days for waiting period", "value": "365", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "BenefitScope", "value": "Policy", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false}]}, {"timeOfOccurrence": "/Date(1749199182327+0530)/", "category": "Repudiation", "subCategory": "DenyObesity", "subCategoryDisplayName": "Obesity", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290311+0530)/", "message": "Obesity - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182328+0530)/", "category": "Repudiation", "subCategory": "WaitingPeriodByAilmentsCategory", "subCategoryDisplayName": "Waiting Period by Ailment category", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290311+0530)/", "message": "Waiting Period by Ailment category - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182363+0530)/", "category": "Repudiation", "subCategory": "ExclusionByProcedures", "subCategoryDisplayName": "Repudiation By Procedure", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290312+0530)/", "message": "Repudiation By Procedure - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182363+0530)/", "category": "Repudiation", "subCategory": "WaitingPeriodByProcedures", "subCategoryDisplayName": "Waiting Period by Procedure", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290312+0530)/", "message": "Waiting Period by Procedure - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182363+0530)/", "category": "Repudiation", "subCategory": "RepudiationByHospital", "subCategoryDisplayName": "Repudiation By Hospital And Claim Type", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290312+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749205290312+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749205290312+0530)/", "message": "Repudiation By Hospital And Claim Type - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749205290312+0530)/", "message": "Repudiation By Hospital And Claim Type - Execution ends", "loggerType": "Information"}], "matchingConfig": [{"relation To Primary Beneficiary": "ALL", "grade": "gold, gold plus, gold plus_r, gold_r, silver, silver_r, platinum, platinum_r", "claim type": "Cashless, PreAuth", "skip For Network Hospital": "False", "skip For PPN Hospital": "False", "hospital": "287585", "skip for Demise": "False", "benefitScope": "Policy"}], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 119565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "Relation To Primary Beneficiary", "value": "ALL", "isFilterApplicable": true, "defaultValue": "ALL", "claimValue": "<PERSON><PERSON><PERSON>", "isToBeDisplayed": true}, {"displayName": "Grade", "value": "gold,gold plus,gold plus_r,gold_r,silver,silver_r,platinum,platinum_r", "isFilterApplicable": true, "defaultValue": "ANY", "claimValue": "Gold Plus", "isToBeDisplayed": true}, {"displayName": "Claim type", "value": "Cashless,PreAuth", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "<PERSON><PERSON> For Network Hospital", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Skip For PPN Hospital", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Hospital", "value": "287585", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Skip for Demise", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "BenefitScope", "value": "Policy", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false}]}, {"timeOfOccurrence": "/Date(1749199182365+0530)/", "category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "loggerType": "Information", "matchingConfig": [{"claimType": "PreAuth"}, {"claimType": "Cashless"}], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 119565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "ClaimType", "value": "PreAuth", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "ClaimType", "value": "Cashless", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}]}, {"timeOfOccurrence": "/Date(1749199182367+0530)/", "category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "loggerType": "Information", "matchingConfig": [{"type of  Room": "Normal", "provider Type": "False", "propotionateApplicable": "True", "skip Proportionate On DeathCase": "False", "skip Proportionate On AccidentCase": "False", "topUp Configured": "False", "relation to Primary beneficiary": "Father, <PERSON><PERSON><PERSON>, Mother, <PERSON><PERSON><PERSON>", "grade": "gold plus, gold", "min Age": "0", "max Age": "0", "room Value Limit Defined By": "Amount", "room Value Type": "5802.0", "room Capped Limit": "0", "proportionate Limit Defined By": "Amount", "proportionate Limit Defined By Value": "0", "min SI": "0", "max SI": "0", "benefitScope": "Policy"}], "preExecutionApprovedAmount": 106163.96, "preExecutionTotalBillAmount": 115565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 106163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "Type of  Room", "value": "Normal", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Provider Type", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "PropotionateApplicable", "value": "Applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Skip Proportionate On DeathCase", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Skip Proportionate On AccidentCase", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "TopUp Configured", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Relation to Primary beneficiary", "value": "Father,<PERSON><PERSON><PERSON>,Mother,<PERSON><PERSON><PERSON>", "isFilterApplicable": true, "defaultValue": "ALL", "claimValue": "<PERSON><PERSON><PERSON>", "isToBeDisplayed": true}, {"displayName": "Grade", "value": "gold plus,gold", "isFilterApplicable": true, "defaultValue": "ANY", "claimValue": "Gold Plus", "isToBeDisplayed": true}, {"displayName": "Min Age", "value": "0", "isFilterApplicable": true, "defaultValue": "", "claimValue": "61", "isToBeDisplayed": false}, {"displayName": "Max Age", "value": "0", "isFilterApplicable": true, "defaultValue": "", "claimValue": "61", "isToBeDisplayed": false}, {"displayName": "Room Value Limit Defined By", "value": "Amount", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Room Value Type", "value": "5802.0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Room Capped Limit", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false}, {"displayName": "Proportionate Limit Defined By", "value": "Amount", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Proportionate Limit Defined By Value", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false}, {"displayName": "Min SI", "value": "0", "isFilterApplicable": true, "defaultValue": "", "claimValue": "1600000.0", "isToBeDisplayed": false}, {"displayName": "Max SI", "value": "0", "isFilterApplicable": true, "defaultValue": "", "claimValue": "1600000.0", "isToBeDisplayed": false}, {"displayName": "BenefitScope", "value": "Policy", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false}]}, {"timeOfOccurrence": "/Date(1749199182382+0530)/", "category": "PropotinateDeduction", "subCategory": "VariantNotConfigured", "subCategoryDisplayName": "Condition To Be Reviewed", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290312+0530)/", "message": "Condition To Be Reviewed - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 106163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182382+0530)/", "category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "loggerType": "Information", "matchingConfig": [{"relation To Primary Beneficiary": "ALL", "grade": "gold, gold plus, platinum, silver", "claim type": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>", "intimation Days": "3", "copay Defined By": "PecentageOfAdmissibleAmount", "copay Defined Value(Percentage/Amount)": "10", "upper/Lower Copay Limit For Comparison": "0", "skip For Emergency Admission": "True", "is Min/Max Copay Applicable Per Person": "False", "is Copay Applicable Per Admission Basis": "False", "copay Limit Applicable Per Family": "False", "dedution Remarks": "INTIMATION_COPAY", "benefitScope": "Policy"}], "preExecutionApprovedAmount": 106163.96, "preExecutionTotalBillAmount": 115565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 106163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "Relation To Primary Beneficiary", "value": "ALL", "isFilterApplicable": true, "defaultValue": "ALL", "claimValue": "<PERSON><PERSON><PERSON>", "isToBeDisplayed": true}, {"displayName": "Grade", "value": "gold,gold plus,platinum,silver", "isFilterApplicable": true, "defaultValue": "ANY", "claimValue": "Gold Plus", "isToBeDisplayed": true}, {"displayName": "Claim type", "value": "<PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>", "isFilterApplicable": true, "defaultValue": "None", "claimValue": "PreAuth", "isToBeDisplayed": true}, {"displayName": "Intimation Days", "value": "3", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Copay Defined By", "value": "PecentageOfAdmissibleAmount", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Copay Defined Value(Percentage/Amount)", "value": "10", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Upper/Lower Copay Limit For Comparison", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false}, {"displayName": "Skip For Emergency Admission", "value": "Applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Is Min/Max Copay Applicable Per Person", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Is Copay Applicable Per Admission Basis", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Copay Limit Applicable Per Family", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Dedution Remarks", "value": "INTIMATION_COPAY", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "BenefitScope", "value": "Policy", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false}]}, {"timeOfOccurrence": "/Date(1749199182386+0530)/", "category": "Copay", "subCategory": "CopayOnNonNetwork", "subCategoryDisplayName": "Non-Network Hospital", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290312+0530)/", "message": "Non-Network Hospital - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 106163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182390+0530)/", "category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "loggerType": "Information", "matchingConfig": [{"relationship": "ALL", "grade": "gold plus, gold plus_r", "minimum Age": "0", "maximum Age": "0", "min SI": "0", "max SI": "0", "min Claim amount": "0", "max Claim amount": "0", "min Admissible amount": "0", "max Admissible amount": "0", "exculding Procedures": "3119, 3163, 3165, 3467, 3861, 4121, 4430, 4436, 4513, 4560, 4591, 4599, 4671", "hospital Network/Non-Network": "False", "copay Type": "MinOfPercentageOfAdmissibleAndCappedAmount", "copay Percentage": "10", "capped Value": "10000", "skip For Emergency Admission": "False", "applicable on claim event basis": "True", "is Family Floater": "False", "applicable on claim Member basis": "False", "no. of Days": "0", "check PED Applicable For Beneficiary": "False", "isCopayDueToAnyoneIllness": "False", "exculdingAilmentsCategoryOrAilment": "IVF_FOR_INFERTILITY,INFERTILITY,PSYCHIATRIC,GENETICDISORDER,M<PERSON><PERSON><PERSON>LE_SCLEROSIS,MATERNITY_COMPLICATIONS,MATERNITY_MULTIPLE_DELIVERY,MATER<PERSON><PERSON>_NORMAL,MATE<PERSON><PERSON>TY_LSCS,TWIN_DELIVERY,TRIPLET_DELIVERY,MORE_THAN_TRIPLET_DELIVERY,PRE_AND_POST_NATAL,Mental_disorder", "dedution Remarks": "MANDATORY_COPAY", "sum Insured Utilization": "0", "benefitScope": "Policy"}], "preExecutionApprovedAmount": 106163.96, "preExecutionTotalBillAmount": 115565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 106163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "Relationship", "value": "ALL", "isFilterApplicable": true, "defaultValue": "ALL", "claimValue": "<PERSON><PERSON><PERSON>", "isToBeDisplayed": true}, {"displayName": "Grade", "value": "gold plus,gold plus_r", "isFilterApplicable": true, "defaultValue": "ANY", "claimValue": "Gold Plus", "isToBeDisplayed": true}, {"displayName": "Minimum Age", "value": "0", "isFilterApplicable": true, "defaultValue": "", "claimValue": "61", "isToBeDisplayed": false}, {"displayName": "Maximum Age", "value": "0", "isFilterApplicable": true, "defaultValue": "", "claimValue": "61", "isToBeDisplayed": false}, {"displayName": "Min SI", "value": "0", "isFilterApplicable": true, "defaultValue": "", "claimValue": "1600000.0", "isToBeDisplayed": false}, {"displayName": "Max SI", "value": "0", "isFilterApplicable": true, "defaultValue": "", "claimValue": "1600000.0", "isToBeDisplayed": false}, {"displayName": "Min Claim amount", "value": "0", "isFilterApplicable": true, "defaultValue": "", "claimValue": "145307.0", "isToBeDisplayed": false}, {"displayName": "Max Claim amount", "value": "0", "isFilterApplicable": true, "defaultValue": "", "claimValue": "145307.0", "isToBeDisplayed": false}, {"displayName": "Min Admissible amount", "value": "0", "isFilterApplicable": true, "defaultValue": "", "claimValue": "115566", "isToBeDisplayed": false}, {"displayName": "Max Admissible amount", "value": "0", "isFilterApplicable": true, "defaultValue": "", "claimValue": "115566", "isToBeDisplayed": false}, {"displayName": "Exculding Procedures", "value": "3119,3163,3165,3467,3861,4121,4430,4436,4513,4560,4591,4599,4671", "isFilterApplicable": true, "defaultValue": "", "claimValue": "4564", "isToBeDisplayed": true}, {"displayName": "Hospital Network/Non-Network", "value": "Not applicable", "isFilterApplicable": true, "defaultValue": "", "claimValue": "True", "isToBeDisplayed": true}, {"displayName": "Copay Type", "value": "MinOfPercentageOfAdmissibleAndCappedAmount", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Copay Per<PERSON>age", "value": "10", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Capped Value", "value": "10000", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Skip For Emergency Admission", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Applicable on claim event basis", "value": "Applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Is Family Floater", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Applicable on claim Member basis", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "No. of Days", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false}, {"displayName": "Check PED Applicable For Beneficiary", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "isCopayDueToAnyoneIllness", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "ExculdingAilmentsCategoryOrAilment", "value": "IVF_FOR_INFERTILITY,INFERTILITY,PSYCHIATRIC,GENETICDISORDER,M<PERSON><PERSON><PERSON>LE_SCLEROSIS,MATERNITY_COMPLICATIONS,MATERNITY_MULTIPLE_DELIVERY,MATER<PERSON><PERSON>_NORMAL,MATE<PERSON><PERSON>TY_LSCS,TWIN_DELIVERY,TRIPLET_DELIVERY,MORE_THAN_TRIPLET_DELIVERY,PRE_AND_POST_NATAL,Mental_disorder", "isFilterApplicable": true, "defaultValue": "", "claimValue": "INFECTIOUS_BACTERIAL_VIRAL_MEDICAL_MGMT", "isToBeDisplayed": true}, {"displayName": "Dedution Remarks", "value": "MANDATORY_COPAY", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "Sum Insured Utilization", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false}, {"displayName": "BenefitScope", "value": "Policy", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false}]}, {"timeOfOccurrence": "/Date(1749199182465+0530)/", "category": "Copay", "subCategory": "RelationGradeAdmissibleAmount", "subCategoryDisplayName": "Copay By Admissible Amount", "loggerType": "Information", "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182470+0530)/", "category": "Copay", "subCategory": "VariantNotConfigured", "subCategoryDisplayName": "Condition To Be Reviewed", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290313+0530)/", "message": "Condition To Be Reviewed - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182471+0530)/", "category": "Copay", "subCategory": "RelationGradeAilment", "subCategoryDisplayName": "Ailment Category", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290313+0530)/", "message": "Ailment Category - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182472+0530)/", "category": "AilmentCapping", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290313+0530)/", "message": "Standard (IRDAI) - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749205290313+0530)/", "message": "Standard (IRDAI) - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 96163.96, "preExecutionTotalBillAmount": 115565.9, "preExectionTotalDeductionAmount": 19402.04, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182473+0530)/", "category": "AilmentCapping", "subCategory": "AilmentCappingByGrade", "subCategoryDisplayName": "Ailment Category", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290313+0530)/", "message": "Ailment Category - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182672+0530)/", "category": "AilmentCapping", "subCategory": "ProcedureCappingByApproachAndPostion", "subCategoryDisplayName": "Position and Approach", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290313+0530)/", "message": "Position and Approach - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182690+0530)/", "category": "AilmentCapping", "subCategory": "AilmentProcedureCappingBySIType", "subCategoryDisplayName": "Ailment Category and Procedure Combination", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290313+0530)/", "message": "Ailment Category and Procedure Combination - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182840+0530)/", "category": "AilmentCapping", "subCategory": "ProcedureCappingByGrade", "subCategoryDisplayName": "Procedure", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290313+0530)/", "message": "Procedure - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182860+0530)/", "category": "AilmentCapping", "subCategory": "AilmentCappingByClaimType", "subCategoryDisplayName": "Ailment Capping by <PERSON><PERSON><PERSON>", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290313+0530)/", "message": "Ailment Capping by <PERSON><PERSON>m Type - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182913+0530)/", "category": "AilmentCapping", "subCategory": "MaternityWellnessBabyCapping", "subCategoryDisplayName": "Maternity, wellness baby care & Pre-Post natal", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290313+0530)/", "message": "Maternity, wellness baby care & Pre-Post natal - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182952+0530)/", "category": "AilmentCapping", "subCategory": "VariantNotConfigured", "subCategoryDisplayName": "Condition To Be Reviewed", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290313+0530)/", "message": "Condition To Be Reviewed - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182952+0530)/", "category": "SIRestriction", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290313+0530)/", "message": "Standard - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182952+0530)/", "category": "SIRestriction", "subCategory": "OtherHealthCareSIRestriction", "subCategoryDisplayName": "SI Restriction By Health Care Types", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290313+0530)/", "message": "SI Restriction By Health Care Types - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182955+0530)/", "category": "PolicyBenefits", "subCategory": "NotApplicable", "subCategoryDisplayName": "Not Applicable", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749205290313+0530)/", "message": "Not Applicable - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182955+0530)/", "category": "UCRDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "loggerType": "Information", "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}], "claimRuleResults": {"denialType": "NA", "claimDenials": []}}, {"logCreatedDate": "/Date(1749199181984)/", "typeOfLog": "Decision", "ruleEngineLogs": [], "decisionRuleResults": {"_decisionResults": [{"status": "AUTOMATE", "reasons": ["Request To Execute Rule Engine"]}], "claimDenials": []}}, {"logCreatedDate": "/Date(1749032620767)/", "typeOfLog": "Decision", "ruleEngineLogs": [], "decisionRuleResults": {"_decisionResults": [{"status": "MISSING_DOC", "reasons": [""]}], "claimDenials": []}}, {"logCreatedDate": "/Date(1748952897990)/", "typeOfLog": "Decision", "ruleEngineLogs": [], "decisionRuleResults": {"_decisionResults": [{"status": "MISSING_DOC", "reasons": [""]}], "claimDenials": []}}]}], "isSuccess": true, "errorMsg": "None"}