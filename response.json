[{"claimNo": 130768275, "logs": [{"logCreatedDate": "/Date(1749713719386)/", "typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": [{"timeOfOccurrence": "/Date(1749713622925+0530)/", "category": "BaseConfiguration", "subCategory": "DefautChecks", "subCategoryDisplayName": "Default Configuration", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Claim types considered for discount override: PreHosp,PostHosp,HospBenefit,HealthCheckUp,RI,ReOpenClaim", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Override Discount By <PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON> not executed as no matching rule found.", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Default Configuration - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 0, "postExectionTotalDeductionAmount": 0, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749713622936+0530)/", "category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "*****Hospital Discount Calculation Started*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Matching Config Count: 3", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Matching Config Count: 3", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Standard - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "All Bills are pre-post bills so overiding the bill date to DOA", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "MMOU Discount Calculated: 804.42", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "HMOU Discount Calculated: 914", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "MAX Discount Calculated: 914", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Discount Calculation Is Goving with HMOU", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Calculated Discount Amount: 0.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Calculated Discount Remarks: Miscellaneous Charges: 664.07,Pharmacy & Medicine Charges: 249.93", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "*****HospitalDiscount Calculation Ends*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "*****Calculate UCR Started*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "User Selected Bill UCR Bill Amount : 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "*****CalculateHospitalDiscount Calculation Ends*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Standard - Execution ends", "loggerType": "Information"}], "matchingConfig": [{"claimType": "None"}, {"claimType": "PreAuth"}, {"claimType": "Cashless"}], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 93230.35, "postExectionTotalDeductionAmount": 0, "configRuleLogObject": [{"displayName": "ClaimType", "value": "None", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": true}, {"displayName": "ClaimType", "value": "PreAuth", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": true}, {"displayName": "ClaimType", "value": "Cashless", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": true}]}, {"timeOfOccurrence": "/Date(1749713643852+0530)/", "category": "NonMedicalExpense", "subCategory": "ManualOverRideBills", "subCategoryDisplayName": "Manual OverRide Bills", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Manual OverRide Bills - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 93230.35, "postExectionTotalDeductionAmount": 914, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749713643852+0530)/", "category": "NonMedicalExpense", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Standard (IRDAI) - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Admission/Registration Charges, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:DOCUMENTATION CHARGES / ADMINISTRATIVE EXPENSES, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Diaper Of Any Type, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:CREAMS POWDERS LOTIONS (Toiletries are not payable, only prescribed medical pharmaceuticals payable), Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Tpa Charges, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Admission/Registration Charges, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Vaccination Charges, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Vaccination Charges, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Vaccination Charges, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Standard (IRDAI) - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 93230.35, "preExectionTotalDeductionAmount": 914, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 88448.88, "postExectionTotalDeductionAmount": 914, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749713643859+0530)/", "category": "NonMedicalExpense", "subCategory": "HandelNMEByPreAuthType", "subCategoryDisplayName": "Estimate For Initial", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "*****Calulating CalculateNMEDeduction Started*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Estimate For Initial - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 88448.88, "postExectionTotalDeductionAmount": 914, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749713643863+0530)/", "category": "NonMedicalExpense", "subCategory": "CapAmbulanceCharges", "subCategoryDisplayName": "Ambulance Limit By Amount", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "No deduction found in the historic claims", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Max Payable Ambulance Charge: 5000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Current Payable Ambulance Charge if Any applicable: 5000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Ambulance Limit By Amount - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 88448.88, "postExectionTotalDeductionAmount": 914, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749713643873+0530)/", "category": "NonMedicalExpense", "subCategory": "RIPrePostNMEDeductions", "subCategoryDisplayName": "Reimbursement Pre-Post Deductions", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Reimbursement Pre-Post Deductions - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 88448.88, "postExectionTotalDeductionAmount": 914, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749713643873+0530)/", "category": "NonMedicalExpense", "subCategory": "ManualOverRideBills", "subCategoryDisplayName": "Manual OverRide Bills", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Manual OverRide Bills - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 88448.88, "postExectionTotalDeductionAmount": 914, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749713643875+0530)/", "category": "Repudiation", "subCategory": "ExclusionByGenderAilment", "subCategoryDisplayName": "Gender and ailment", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Gender and ailment - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 88448.88, "postExectionTotalDeductionAmount": 914, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749713643879+0530)/", "category": "Repudiation", "subCategory": "ExclusionByProcedures", "subCategoryDisplayName": "Repudiation By Procedure", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Repudiation By Procedure - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 88448.88, "postExectionTotalDeductionAmount": 914, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749713643885+0530)/", "category": "Repudiation", "subCategory": "ExclusionByAilment", "subCategoryDisplayName": "Repudiation By Ailment Category", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Repudiation By Ailment Category - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 88448.88, "postExectionTotalDeductionAmount": 914, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749713643911+0530)/", "category": "Repudiation", "subCategory": "RepudiationByRefractiveError", "subCategoryDisplayName": "Refractive Error", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Refractive Error - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 88448.88, "postExectionTotalDeductionAmount": 914, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749713643914+0530)/", "category": "Repudiation", "subCategory": "DenyObesity", "subCategoryDisplayName": "Obesity", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Obesity - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 88448.88, "postExectionTotalDeductionAmount": 914, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749713643915+0530)/", "category": "Repudiation", "subCategory": "OnNumberOfChildren", "subCategoryDisplayName": "Number of living children allowed for maternity coverage", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Number of living children allowed for maternity coverage - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 88448.88, "postExectionTotalDeductionAmount": 914, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749713643916+0530)/", "category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Matching Config Count: 2", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Matching Config Count: 2", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Standard (IRDAI) - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Applying Excess of Tariff ", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Calcualtion on Excess of Room Tariff Start", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Hospital room rent master details", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Opted Room Category :SinglePrivateRoom", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Tariff Room Rent From Master:9500.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Tariff ICU Room Rent From Master:9500.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Calcualtion on Excess of Room Tariff Ends", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Calculation On Excess Of Package Starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Calculation On Excess Of Traiff Package", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Excess Of Traiff Package : Opted Room Category : PRIVATE", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Package Id : 3796467", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Package Master Details : Package Id : 3796467 General:69700,Sharing:73800,Private:82000,Deluxe:82000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "DIALYSIS COUNT :0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Relevant Quantity :1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Package Amount :82000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Excess Of Tariff Package Calculated ; Package Amount: 83282.00 , Contracted Package Amount: 82000 ", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Calculation On Excess Of Package Ends", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Standard (IRDAI) - Execution ends", "loggerType": "Information"}], "matchingConfig": [{"claimType": "PreAuth"}, {"claimType": "Cashless"}], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 88448.88, "preExectionTotalDeductionAmount": 914, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 87166.88, "postExectionTotalDeductionAmount": 914, "configRuleLogObject": [{"displayName": "ClaimType", "value": "PreAuth", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": true}, {"displayName": "ClaimType", "value": "Cashless", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": true}]}, {"timeOfOccurrence": "/Date(1749713647536+0530)/", "category": "PropotinateDeduction", "subCategory": "FixedLimitWithAilmentExclusion", "subCategoryDisplayName": "By Room Value With Ailment Exclusion", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Matching Config Count: 2", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Matching Config Count: 2", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room Rent limit applied based on 3.5% of sum insured 500000.0 is 17500.00", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room Rent limit applied based on 5% of sum insured 500000.0 is 25000.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "*****Calulating Eligibile Room Category By RoomRent Started*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Rule Suggested, Eligible Room Rent 17500.00", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Rule Suggested, Eligible Room Rent Tolerance 105", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Rule Suggested, Eligible Room Rent ToleranceType Percentage", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "ICU Category: Intensive,ICU,HDU,Burnward", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "DELUXE Category: Suite,Delux,DeluxeRoom", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Hospital room rent master details", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Hospital room rent master details After filtering ICU", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "No excat room rent details for the eligible room rent :17500.00", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Now trying to find room rent details by applying tolerance by :Percentage", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Tolerance Amount:18375.0000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Finding room rent details from master , taking max price which is less than tolerance amount 18375.0000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Max room rent price 18000.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room Rent master details which is used to calculate eligible room rent", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Rule Calculated EligibleRoomCategory : DELUXE", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "*****Calulating Eligibile Room Category By RoomRent END*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "*****Set Proportionate Calculation Started*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Calculation On Excess Of Package Starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Calculation On Excess Of Eligible Package", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Excess Of Eligible Package : Eligible Room Category : DELUXE", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Package Id : 3796467", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Package Master Details : Package Id : 3796467 General:69700,Sharing:73800,Private:82000,Deluxe:82000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "DIALYSIS COUNT :0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Relevant Quantity :1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Package Amount :82000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Calculation On Excess Of Package Ends", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Proportionate CalCulation MaxEligibilityNormal: 17500.00", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Proportionate Calculation MaxEligibilityICU: 25000.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Proportionate CalCulation Availed ICU Length Of Stay: 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Proportionate CalCulation Availed Normal Length Of Stay: 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Proportionate CalCulation Max Normal Room Rent Allowed: 0.00", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Proportionate CalCulation Proportionate Factor Normal: 100%", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Proportionate CalCulation Proportionate Factor ICU: 100%", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "*****Proportionate Calculation End*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521390+0530)/", "message": "*****Set Proportionate Calculation Ended*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Proportionate deduction limit: 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Proportionate deduction limit: 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Proportionate Calculation Total Admissible Amount Rs: 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "By Room Value With Ailment Exclusion - Execution ends", "loggerType": "Information"}], "matchingConfig": [{"type of  Room": "Normal", "provider Type": "False", "propotionateApplicable": "True", "skip Proportionate On DeathCase": "False", "skip Proportionate On AccidentCase": "False", "topUp Configured": "False", "relation to Primary beneficiary": "ALL", "grade": "any", "min Age": "0", "max Age": "0", "room Value Limit Defined By": "PercentageOfTotalSI", "room Value Type": "3.5", "room Capped Limit": "0", "proportionate Limit Defined By": "Amount", "proportionate Limit Defined By Value": "0", "min SI": "0", "max SI": "0", "benefitScope": "Policy"}, {"type of  Room": "ICU", "provider Type": "False", "propotionateApplicable": "False", "skip Proportionate On DeathCase": "False", "skip Proportionate On AccidentCase": "False", "topUp Configured": "False", "relation to Primary beneficiary": "ALL", "grade": "any", "min Age": "0", "max Age": "0", "room Value Limit Defined By": "PercentageOfTotalSI", "room Value Type": "5", "room Capped Limit": "0", "proportionate Limit Defined By": "Amount", "proportionate Limit Defined By Value": "0", "min SI": "0", "max SI": "0", "benefitScope": "Policy"}], "preExecutionApprovedAmount": 86253, "preExecutionTotalBillAmount": 87166.88, "preExectionTotalDeductionAmount": 914, "postExecutionApprovedAmount": 86253, "postExecutionTotalBillAmount": 87166.88, "postExectionTotalDeductionAmount": 914, "configRuleLogObject": [{"displayName": "Type of  Room", "value": "Normal", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": true}, {"displayName": "Provider Type", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": true}, {"displayName": "PropotionateApplicable", "value": "Applicable", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": true}, {"displayName": "Skip Proportionate On DeathCase", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": true}, {"displayName": "Skip Proportionate On AccidentCase", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": true}, {"displayName": "TopUp Configured", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": true}, {"displayName": "Relation to Primary beneficiary", "value": "ALL", "isFilterApplicable": true, "defaultValue": "ALL", "claimValue": "Self", "isToBeDisplayed": true}, {"displayName": "Grade", "value": "any", "isFilterApplicable": true, "defaultValue": "ANY", "isToBeDisplayed": true}, {"displayName": "Min Age", "value": "0", "isFilterApplicable": true, "defaultValue": "", "claimValue": "29", "isToBeDisplayed": false}, {"displayName": "Max Age", "value": "0", "isFilterApplicable": true, "defaultValue": "", "claimValue": "29", "isToBeDisplayed": false}, {"displayName": "Room Value Limit Defined By", "value": "PercentageOfTotalSI", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": true}, {"displayName": "Room Value Type", "value": "3.5", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": true}, {"displayName": "Room Capped Limit", "value": "0", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": false}, {"displayName": "Proportionate Limit Defined By", "value": "Amount", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": true}, {"displayName": "Proportionate Limit Defined By Value", "value": "0", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": false}, {"displayName": "Min SI", "value": "0", "isFilterApplicable": true, "defaultValue": "", "claimValue": "500000.0", "isToBeDisplayed": false}, {"displayName": "Max SI", "value": "0", "isFilterApplicable": true, "defaultValue": "", "claimValue": "500000.0", "isToBeDisplayed": false}, {"displayName": "BenefitScope", "value": "Policy", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": false}, {"displayName": "Type of  Room", "value": "ICU", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": true}, {"displayName": "Provider Type", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": true}, {"displayName": "PropotionateApplicable", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": true}, {"displayName": "Skip Proportionate On DeathCase", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": true}, {"displayName": "Skip Proportionate On AccidentCase", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": true}, {"displayName": "TopUp Configured", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": true}, {"displayName": "Relation to Primary beneficiary", "value": "ALL", "isFilterApplicable": true, "defaultValue": "ALL", "claimValue": "Self", "isToBeDisplayed": true}, {"displayName": "Grade", "value": "any", "isFilterApplicable": true, "defaultValue": "ANY", "isToBeDisplayed": true}, {"displayName": "Min Age", "value": "0", "isFilterApplicable": true, "defaultValue": "", "claimValue": "29", "isToBeDisplayed": false}, {"displayName": "Max Age", "value": "0", "isFilterApplicable": true, "defaultValue": "", "claimValue": "29", "isToBeDisplayed": false}, {"displayName": "Room Value Limit Defined By", "value": "PercentageOfTotalSI", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": true}, {"displayName": "Room Value Type", "value": "5", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": true}, {"displayName": "Room Capped Limit", "value": "0", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": false}, {"displayName": "Proportionate Limit Defined By", "value": "Amount", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": true}, {"displayName": "Proportionate Limit Defined By Value", "value": "0", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": false}, {"displayName": "Min SI", "value": "0", "isFilterApplicable": true, "defaultValue": "", "claimValue": "500000.0", "isToBeDisplayed": false}, {"displayName": "Max SI", "value": "0", "isFilterApplicable": true, "defaultValue": "", "claimValue": "500000.0", "isToBeDisplayed": false}, {"displayName": "BenefitScope", "value": "Policy", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": false}]}, {"timeOfOccurrence": "/Date(1749713708703+0530)/", "category": "Copay", "subCategory": "NotApplicable", "subCategoryDisplayName": "Not Applicable", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Not Applicable - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 86253, "postExecutionTotalBillAmount": 87166.88, "postExectionTotalDeductionAmount": 914, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749713708705+0530)/", "category": "AilmentCapping", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Standard (IRDAI) - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Standard (IRDAI) - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 86253, "preExecutionTotalBillAmount": 87166.88, "preExectionTotalDeductionAmount": 914, "postExecutionApprovedAmount": 86253, "postExecutionTotalBillAmount": 87166.88, "postExectionTotalDeductionAmount": 914, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749713708717+0530)/", "category": "AilmentCapping", "subCategory": "ProcedureCappingByApproachAndPostion", "subCategoryDisplayName": "Position and Approach", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Position and Approach - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 86253, "postExecutionTotalBillAmount": 87166.88, "postExectionTotalDeductionAmount": 914, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749713708729+0530)/", "category": "AilmentCapping", "subCategory": "ProcedureCappingByGrade", "subCategoryDisplayName": "Procedure", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Procedure - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 86253, "postExecutionTotalBillAmount": 87166.88, "postExectionTotalDeductionAmount": 914, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749713708743+0530)/", "category": "AilmentCapping", "subCategory": "MaternityWellnessBabyCapping", "subCategoryDisplayName": "Maternity, wellness baby care & Pre-Post natal", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Maternity, wellness baby care & Pre-Post natal - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Maternity sub-limit based on amount: 50000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Excess of Maternity sub-limit value is 36253.00", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Maternity, wellness baby care & Pre-Post natal - Execution ends", "loggerType": "Information"}], "matchingConfig": [{"relation To Primary Beneficiary": "Employee, Self, Spouse, Wife", "grade": "any", "from Sum Insured Band": "1", "to Sum Insured Band": "1000000", "ailment Category for Maternity Limit": "MATERNITY_LSCS", "ailmentCategoryForExpn": "MATERNITY_LSCS", "maternity Capping Defined By": "Amount", "maternity Capping Defined Value(Percentage/Amount)": "50000", "maternity Upper/Lower Capping Limit For Comparison": "0", "well Baby 'Bill for'": "Consultation Well baby charges", "wellness baby Capping Defined By": "NONE", "wellness baby Capping Defined value": "0", "wellness baby outside maternity": "False", "pre-Post Natal Within Maternity": "True", "pre-Post Natal Calculated outside maternity": "False", "pre-post natal deduction By": "NONE", "pre-post natal deduction Value(Percentage/Amount)": "0", "is Capping Applicable Per Admission Basis": "False", "is Capping Applicable Per Family": "False", "benefitScope": "Policy"}], "preExecutionApprovedAmount": 86253, "preExecutionTotalBillAmount": 87166.88, "preExectionTotalDeductionAmount": 914, "postExecutionApprovedAmount": 86253, "postExecutionTotalBillAmount": 87166.88, "postExectionTotalDeductionAmount": 914, "configRuleLogObject": [{"displayName": "Relation To Primary Beneficiary", "value": "Employee,Self,Spouse,Wife", "isFilterApplicable": true, "defaultValue": "ALL", "claimValue": "Self", "isToBeDisplayed": true}, {"displayName": "Grade", "value": "any", "isFilterApplicable": true, "defaultValue": "ANY", "isToBeDisplayed": true}, {"displayName": "From Sum Insured Band", "value": "1", "isFilterApplicable": true, "defaultValue": "", "claimValue": "500000.0", "isToBeDisplayed": true}, {"displayName": "To Sum Insured Band", "value": "1000000", "isFilterApplicable": true, "defaultValue": "", "claimValue": "500000.0", "isToBeDisplayed": true}, {"displayName": "Ailment Category for Maternity Limit", "value": "MATERNITY_LSCS", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": true}, {"displayName": "AilmentCategoryForExpn", "value": "MATERNITY_LSCS", "isFilterApplicable": true, "defaultValue": "ALL", "claimValue": "MATERNITY_LSCS", "isToBeDisplayed": true}, {"displayName": "Maternity Capping Defined By", "value": "Amount", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": true}, {"displayName": "Maternity Capping Defined Value(Percentage/Amount)", "value": "50000", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": true}, {"displayName": "Maternity Upper/Lower Capping Limit For Comparison", "value": "0", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": false}, {"displayName": "Well Baby 'Bill for'", "value": "Consultation Well baby charges", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": true}, {"displayName": "Wellness baby Capping Defined By", "value": "NONE", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": true}, {"displayName": "Wellness baby Capping Defined value", "value": "0", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": true}, {"displayName": "Wellness baby outside maternity", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": true}, {"displayName": "Pre-Post Natal Within Maternity", "value": "Applicable", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": true}, {"displayName": "Pre-Post Natal Calculated outside maternity", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": true}, {"displayName": "Pre-post natal deduction By", "value": "NONE", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": true}, {"displayName": "Pre-post natal deduction Value(Percentage/Amount)", "value": "0", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": false}, {"displayName": "Is Capping Applicable Per Admission Basis", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": true}, {"displayName": "Is Capping Applicable Per Family", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": true}, {"displayName": "BenefitScope", "value": "Policy", "isFilterApplicable": false, "defaultValue": "", "claimValue": "", "isToBeDisplayed": false}]}, {"timeOfOccurrence": "/Date(1749713719371+0530)/", "category": "SIRestriction", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Standard - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 50000, "postExecutionTotalBillAmount": 87166.88, "postExectionTotalDeductionAmount": 37167, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749713719380+0530)/", "category": "SIRestriction", "subCategory": "OtherHealthCareSIRestriction", "subCategoryDisplayName": "SI Restriction By Health Care Types", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "SI Restriction By Health Care Types - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 50000, "postExecutionTotalBillAmount": 87166.88, "postExectionTotalDeductionAmount": 37167, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749713719384+0530)/", "category": "PolicyBenefits", "subCategory": "NotApplicable", "subCategoryDisplayName": "Not Applicable", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Not Applicable - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 50000, "postExecutionTotalBillAmount": 87166.88, "postExectionTotalDeductionAmount": 37167, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749713719384+0530)/", "category": "UCRDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "*****Calculation On SOC Started*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "NO SOC Details Found", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "*****Calculation On SOC Ends*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Standard - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 50000, "postExecutionTotalBillAmount": 87166.88, "postExectionTotalDeductionAmount": 37167, "configRuleLogObject": []}], "claimRuleResults": {"denialType": "NA", "claimDenials": []}}, {"logCreatedDate": "/Date(1749189988658)/", "typeOfLog": "ClaimSecondLevelApproval", "ruleEngineLogs": []}, {"logCreatedDate": "/Date(1749189988400)/", "typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": [{"timeOfOccurrence": "/Date(1749189988341+0530)/", "category": "BaseConfiguration", "subCategory": "DefautChecks", "subCategoryDisplayName": "Default Configuration", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Claim types considered for discount override: PreHosp,PostHosp,HospBenefit,HealthCheckUp,RI,ReOpenClaim", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Override Discount By <PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON> not executed as no matching rule found.", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Default Configuration - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 0, "postExectionTotalDeductionAmount": 0, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749189988342+0530)/", "category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "*****Hospital Discount Calculation Started*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Matching Config Count: 3", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Matching Config Count: 3", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Standard - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "All Bills are pre-post bills so overiding the bill date to DOA", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "MMOU Discount Calculated: 804.42", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "HMOU Discount Calculated: 914", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "MAX Discount Calculated: 914", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Discount Calculation Is Goving with HMOU", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Calculated Discount Amount: 0.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Calculated Discount Remarks: Miscellaneous Charges: 664.07,Pharmacy & Medicine Charges: 249.93", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "*****HospitalDiscount Calculation Ends*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "*****Calculate UCR Started*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "User Selected Bill UCR Bill Amount : 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "*****CalculateHospitalDiscount Calculation Ends*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Standard - Execution ends", "loggerType": "Information"}], "matchingConfig": [{"claimType": "None"}, {"claimType": "PreAuth"}, {"claimType": "Cashless"}], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 93230.35, "postExectionTotalDeductionAmount": 0, "configRuleLogObject": [{"displayName": "ClaimType", "value": "None", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "ClaimType", "value": "PreAuth", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "ClaimType", "value": "Cashless", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}]}, {"timeOfOccurrence": "/Date(1749189988342+0530)/", "category": "NonMedicalExpense", "subCategory": "ManualOverRideBills", "subCategoryDisplayName": "Manual OverRide Bills", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Manual OverRide Bills - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 93230.35, "postExectionTotalDeductionAmount": 914, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749189988342+0530)/", "category": "NonMedicalExpense", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Standard (IRDAI) - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Admission/Registration Charges, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:DOCUMENTATION CHARGES / ADMINISTRATIVE EXPENSES, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Diaper Of Any Type, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:CREAMS POWDERS LOTIONS (Toiletries are not payable, only prescribed medical pharmaceuticals payable), Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Tpa Charges, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Admission/Registration Charges, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Vaccination Charges, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Vaccination Charges, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Vaccination Charges, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Standard (IRDAI) - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 93230.35, "preExectionTotalDeductionAmount": 914, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 88448.88, "postExectionTotalDeductionAmount": 914, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749189988343+0530)/", "category": "NonMedicalExpense", "subCategory": "HandelNMEByPreAuthType", "subCategoryDisplayName": "Estimate For Initial", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "*****Calulating CalculateNMEDeduction Started*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Estimate For Initial - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 88448.88, "postExectionTotalDeductionAmount": 914, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749189988343+0530)/", "category": "NonMedicalExpense", "subCategory": "CapAmbulanceCharges", "subCategoryDisplayName": "Ambulance Limit By Amount", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "No deduction found in the historic claims", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Max Payable Ambulance Charge: 5000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Current Payable Ambulance Charge if Any applicable: 5000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Ambulance Limit By Amount - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 88448.88, "postExectionTotalDeductionAmount": 914, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749189988343+0530)/", "category": "NonMedicalExpense", "subCategory": "RIPrePostNMEDeductions", "subCategoryDisplayName": "Reimbursement Pre-Post Deductions", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Reimbursement Pre-Post Deductions - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 88448.88, "postExectionTotalDeductionAmount": 914, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749189988343+0530)/", "category": "NonMedicalExpense", "subCategory": "ManualOverRideBills", "subCategoryDisplayName": "Manual OverRide Bills", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Manual OverRide Bills - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 88448.88, "postExectionTotalDeductionAmount": 914, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749189988343+0530)/", "category": "Repudiation", "subCategory": "ExclusionByGenderAilment", "subCategoryDisplayName": "Gender and ailment", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Gender and ailment - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 88448.88, "postExectionTotalDeductionAmount": 914, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749189988344+0530)/", "category": "Repudiation", "subCategory": "ExclusionByProcedures", "subCategoryDisplayName": "Repudiation By Procedure", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Repudiation By Procedure - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 88448.88, "postExectionTotalDeductionAmount": 914, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749189988344+0530)/", "category": "Repudiation", "subCategory": "ExclusionByAilment", "subCategoryDisplayName": "Repudiation By Ailment Category", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Repudiation By Ailment Category - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 88448.88, "postExectionTotalDeductionAmount": 914, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749189988351+0530)/", "category": "Repudiation", "subCategory": "RepudiationByRefractiveError", "subCategoryDisplayName": "Refractive Error", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Refractive Error - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 88448.88, "postExectionTotalDeductionAmount": 914, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749189988352+0530)/", "category": "Repudiation", "subCategory": "DenyObesity", "subCategoryDisplayName": "Obesity", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Obesity - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 88448.88, "postExectionTotalDeductionAmount": 914, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749189988352+0530)/", "category": "Repudiation", "subCategory": "OnNumberOfChildren", "subCategoryDisplayName": "Number of living children allowed for maternity coverage", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Number of living children allowed for maternity coverage - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 88448.88, "postExectionTotalDeductionAmount": 914, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749189988352+0530)/", "category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Matching Config Count: 2", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Matching Config Count: 2", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Standard (IRDAI) - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Applying Excess of Tariff ", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Calcualtion on Excess of Room Tariff Start", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Hospital room rent master details", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Opted Room Category :SinglePrivateRoom", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Tariff Room Rent From Master:9500.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Tariff ICU Room Rent From Master:9500.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Calcualtion on Excess of Room Tariff Ends", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Calculation On Excess Of Package Starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Calculation On Excess Of Traiff Package", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Excess Of Traiff Package : Opted Room Category : PRIVATE", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Package Id : 3796467", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Package Master Details : Package Id : 3796467 General:69700,Sharing:73800,Private:82000,Deluxe:82000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "DIALYSIS COUNT :0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Relevant Quantity :1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Package Amount :82000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Excess Of Tariff Package Calculated ; Package Amount: 83282.00 , Contracted Package Amount: 82000 ", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Calculation On Excess Of Package Ends", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Standard (IRDAI) - Execution ends", "loggerType": "Information"}], "matchingConfig": [{"claimType": "PreAuth"}, {"claimType": "Cashless"}], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 88448.88, "preExectionTotalDeductionAmount": 914, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 87166.88, "postExectionTotalDeductionAmount": 914, "configRuleLogObject": [{"displayName": "ClaimType", "value": "PreAuth", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "ClaimType", "value": "Cashless", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}]}, {"timeOfOccurrence": "/Date(1749189988353+0530)/", "category": "PropotinateDeduction", "subCategory": "FixedLimitWithAilmentExclusion", "subCategoryDisplayName": "By Room Value With Ailment Exclusion", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Matching Config Count: 2", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Matching Config Count: 2", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room Rent limit applied based on 3.5% of sum insured 500000.0 is 17500.00", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room Rent limit applied based on 5% of sum insured 500000.0 is 25000.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "*****Calulating Eligibile Room Category By RoomRent Started*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Rule Suggested, Eligible Room Rent 17500.00", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Rule Suggested, Eligible Room Rent Tolerance 105", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Rule Suggested, Eligible Room Rent ToleranceType Percentage", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "ICU Category: Intensive,ICU,HDU,Burnward", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "DELUXE Category: Suite,Delux,DeluxeRoom", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Hospital room rent master details", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Hospital room rent master details After filtering ICU", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "No excat room rent details for the eligible room rent :17500.00", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Now trying to find room rent details by applying tolerance by :Percentage", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Tolerance Amount:18375.0000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Finding room rent details from master , taking max price which is less than tolerance amount 18375.0000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Max room rent price 18000.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room Rent master details which is used to calculate eligible room rent", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Rule Calculated EligibleRoomCategory : DELUXE", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "*****Calulating Eligibile Room Category By RoomRent END*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "*****Set Proportionate Calculation Started*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Calculation On Excess Of Package Starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Calculation On Excess Of Eligible Package", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Excess Of Eligible Package : Eligible Room Category : DELUXE", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Package Id : 3796467", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Package Master Details : Package Id : 3796467 General:69700,Sharing:73800,Private:82000,Deluxe:82000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "DIALYSIS COUNT :0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Relevant Quantity :1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Package Amount :82000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Calculation On Excess Of Package Ends", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Proportionate CalCulation MaxEligibilityNormal: 17500.00", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Proportionate Calculation MaxEligibilityICU: 25000.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Proportionate CalCulation Availed ICU Length Of Stay: 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Proportionate CalCulation Availed Normal Length Of Stay: 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Proportionate CalCulation Max Normal Room Rent Allowed: 0.00", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Proportionate CalCulation Proportionate Factor Normal: 100%", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Proportionate CalCulation Proportionate Factor ICU: 100%", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "*****Proportionate Calculation End*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "*****Set Proportionate Calculation Ended*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Proportionate deduction limit: 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Proportionate deduction limit: 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Proportionate Calculation Total Admissible Amount Rs: 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "By Room Value With Ailment Exclusion - Execution ends", "loggerType": "Information"}], "matchingConfig": [{"type of  Room": "Normal", "provider Type": "False", "propotionateApplicable": "True", "skip Proportionate On DeathCase": "False", "skip Proportionate On AccidentCase": "False", "topUp Configured": "False", "relation to Primary beneficiary": "ALL", "grade": "any", "min Age": "0", "max Age": "0", "room Value Limit Defined By": "PercentageOfTotalSI", "room Value Type": "3.5", "room Capped Limit": "0", "proportionate Limit Defined By": "Amount", "proportionate Limit Defined By Value": "0", "min SI": "0", "max SI": "0", "benefitScope": "Policy"}, {"type of  Room": "ICU", "provider Type": "False", "propotionateApplicable": "False", "skip Proportionate On DeathCase": "False", "skip Proportionate On AccidentCase": "False", "topUp Configured": "False", "relation to Primary beneficiary": "ALL", "grade": "any", "min Age": "0", "max Age": "0", "room Value Limit Defined By": "PercentageOfTotalSI", "room Value Type": "5", "room Capped Limit": "0", "proportionate Limit Defined By": "Amount", "proportionate Limit Defined By Value": "0", "min SI": "0", "max SI": "0", "benefitScope": "Policy"}], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 86253, "postExecutionTotalBillAmount": 87166.88, "postExectionTotalDeductionAmount": 914, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749189988354+0530)/", "category": "Copay", "subCategory": "NotApplicable", "subCategoryDisplayName": "Not Applicable", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Not Applicable - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 86253, "postExecutionTotalBillAmount": 87166.88, "postExectionTotalDeductionAmount": 914, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749189988354+0530)/", "category": "AilmentCapping", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Standard (IRDAI) - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Standard (IRDAI) - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 86253, "preExecutionTotalBillAmount": 87166.88, "preExectionTotalDeductionAmount": 914, "postExecutionApprovedAmount": 86253, "postExecutionTotalBillAmount": 87166.88, "postExectionTotalDeductionAmount": 914, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749189988354+0530)/", "category": "AilmentCapping", "subCategory": "ProcedureCappingByApproachAndPostion", "subCategoryDisplayName": "Position and Approach", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Position and Approach - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 86253, "postExecutionTotalBillAmount": 87166.88, "postExectionTotalDeductionAmount": 914, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749189988358+0530)/", "category": "AilmentCapping", "subCategory": "ProcedureCappingByGrade", "subCategoryDisplayName": "Procedure", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Procedure - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 86253, "postExecutionTotalBillAmount": 87166.88, "postExectionTotalDeductionAmount": 914, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749189988362+0530)/", "category": "AilmentCapping", "subCategory": "MaternityWellnessBabyCapping", "subCategoryDisplayName": "Maternity, wellness baby care & Pre-Post natal", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Maternity sub-limit based on amount: 50000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Excess of Maternity sub-limit value is 36253.00", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Maternity, wellness baby care & Pre-Post natal - Execution ends", "loggerType": "Information"}], "matchingConfig": [{"relation To Primary Beneficiary": "Employee, Self, Spouse, Wife", "grade": "any", "from Sum Insured Band": "1", "to Sum Insured Band": "1000000", "ailment Category for Maternity Limit": "MATERNITY_LSCS", "ailmentCategoryForExpn": "MATERNITY_LSCS", "maternity Capping Defined By": "Amount", "maternity Capping Defined Value(Percentage/Amount)": "50000", "maternity Upper/Lower Capping Limit For Comparison": "0", "well Baby 'Bill for'": "Consultation Well baby charges", "wellness baby Capping Defined By": "NONE", "wellness baby Capping Defined value": "0", "wellness baby outside maternity": "False", "pre-Post Natal Within Maternity": "True", "pre-Post Natal Calculated outside maternity": "False", "pre-post natal deduction By": "NONE", "pre-post natal deduction Value(Percentage/Amount)": "0", "is Capping Applicable Per Admission Basis": "False", "is Capping Applicable Per Family": "False", "benefitScope": "Policy"}], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 86253, "postExecutionTotalBillAmount": 87166.88, "postExectionTotalDeductionAmount": 914, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749189988398+0530)/", "category": "SIRestriction", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Standard - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 50000, "postExecutionTotalBillAmount": 87166.88, "postExectionTotalDeductionAmount": 37167, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749189988398+0530)/", "category": "SIRestriction", "subCategory": "OtherHealthCareSIRestriction", "subCategoryDisplayName": "SI Restriction By Health Care Types", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "SI Restriction By Health Care Types - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 50000, "postExecutionTotalBillAmount": 87166.88, "postExectionTotalDeductionAmount": 37167, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749189988400+0530)/", "category": "PolicyBenefits", "subCategory": "NotApplicable", "subCategoryDisplayName": "Not Applicable", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Not Applicable - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 50000, "postExecutionTotalBillAmount": 87166.88, "postExectionTotalDeductionAmount": 37167, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749189988400+0530)/", "category": "UCRDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "*****Calculation On SOC Started*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "NO SOC Details Found", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "*****Calculation On SOC Ends*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521391+0530)/", "message": "Standard - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 50000, "postExecutionTotalBillAmount": 87166.88, "postExectionTotalDeductionAmount": 37167, "configRuleLogObject": []}], "claimRuleResults": {"denialType": "NA", "claimDenials": []}}, {"logCreatedDate": "/Date(1749189988148)/", "typeOfLog": "Decision", "ruleEngineLogs": [], "decisionRuleResults": {"_decisionResults": [{"status": "AUTOMATE", "reasons": ["Request To Execute Rule Engine"]}], "claimDenials": []}}, {"logCreatedDate": "/Date(1749189931167)/", "typeOfLog": "Decision", "ruleEngineLogs": [], "decisionRuleResults": {"_decisionResults": [{"status": "MISSING_DOC", "reasons": [""]}], "claimDenials": []}}]}, {"claimNo": 130874986, "logs": [{"logCreatedDate": "/Date(1747297229250)/", "typeOfLog": "ClaimSecondLevelApproval", "ruleEngineLogs": []}, {"logCreatedDate": "/Date(1747297229085)/", "typeOfLog": "Auomation", "ruleEngineLogs": []}, {"logCreatedDate": "/Date(1747297228853)/", "typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": [{"timeOfOccurrence": "/Date(1747297228747+0530)/", "category": "BaseConfiguration", "subCategory": "DefautChecks", "subCategoryDisplayName": "Default Configuration", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Claim types considered for discount override: PreHosp,PostHosp,HospBenefit,HealthCheckUp,RI,ReOpenClaim", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Override Discount By <PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON> not executed as no matching rule found.", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Default Configuration - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 0, "postExectionTotalDeductionAmount": 0, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1747297228748+0530)/", "category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "*****Hospital Discount Calculation Started*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Matching Config Count: 3", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Matching Config Count: 3", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Standard - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "All Bills are pre-post bills so overiding the bill date to DOA", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "MMOU Discount Calculated: 1617.42", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "HMOU Discount Calculated: 1597.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "MAX Discount Calculated: 1617.42", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Discount Calculation Is Goving with HMOU", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Calculated Discount Amount: 0.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Calculated Discount Remarks: Miscellaneous Charges: 929.85,Consultant Charges: 197.88,Pharmacy & Medicine Charges: 240.31,Investigation & Lab Charges: 228.97", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "*****HospitalDiscount Calculation Ends*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "*****Calculate UCR Started*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "User Selected Bill UCR Bill Amount : 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "*****CalculateHospitalDiscount Calculation Ends*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Standard - Execution ends", "loggerType": "Information"}], "matchingConfig": [{"claimType": "None"}, {"claimType": "PreAuth"}, {"claimType": "Cashless"}], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 101360.85, "postExectionTotalDeductionAmount": 0, "configRuleLogObject": [{"displayName": "ClaimType", "value": "None", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "ClaimType", "value": "PreAuth", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "ClaimType", "value": "Cashless", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}]}, {"timeOfOccurrence": "/Date(1747297228749+0530)/", "category": "NonMedicalExpense", "subCategory": "ManualOverRideBills", "subCategoryDisplayName": "Manual OverRide Bills", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Manual OverRide Bills - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 101360.85, "postExectionTotalDeductionAmount": 1597.01, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1747297228749+0530)/", "category": "NonMedicalExpense", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Standard (IRDAI) - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Tpa Charges, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Nutrition Planning Charges - Dietician Charges- Diet Charges, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Admission/Registration Charges, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:DOCUMENTATION CHARGES / ADMINISTRATIVE EXPENSES, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Admission/Registration Charges, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Admission/Registration Charges, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:DOCUMENTATION CHARGES / ADMINISTRATIVE EXPENSES, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Diaper Of Any Type, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Vaccination Charges, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:CREAMS POWDERS LOTIONS (Toiletries are not payable, only prescribed medical pharmaceuticals payable), Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Vaccination Charges, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Vaccination Charges, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Standard (IRDAI) - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 101360.85, "preExectionTotalDeductionAmount": 1597.01, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 88149.38, "postExectionTotalDeductionAmount": 1597.01, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1747297228750+0530)/", "category": "NonMedicalExpense", "subCategory": "HandelNMEByPreAuthType", "subCategoryDisplayName": "Estimate For Initial", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "*****Calulating CalculateNMEDeduction Started*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Estimate For Initial - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 88149.38, "postExectionTotalDeductionAmount": 1597.01, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1747297228750+0530)/", "category": "NonMedicalExpense", "subCategory": "CapAmbulanceCharges", "subCategoryDisplayName": "Ambulance Limit By Amount", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "No deduction found in the historic claims", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Max Payable Ambulance Charge: 5000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Current Payable Ambulance Charge if Any applicable: 5000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Ambulance Limit By Amount - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 88149.38, "postExectionTotalDeductionAmount": 1597.01, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1747297228751+0530)/", "category": "NonMedicalExpense", "subCategory": "RIPrePostNMEDeductions", "subCategoryDisplayName": "Reimbursement Pre-Post Deductions", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Reimbursement Pre-Post Deductions - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 88149.38, "postExectionTotalDeductionAmount": 1597.01, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1747297228751+0530)/", "category": "NonMedicalExpense", "subCategory": "ManualOverRideBills", "subCategoryDisplayName": "Manual OverRide Bills", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Manual OverRide Bills - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 88149.38, "postExectionTotalDeductionAmount": 1597.01, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1747297228751+0530)/", "category": "Repudiation", "subCategory": "ExclusionByGenderAilment", "subCategoryDisplayName": "Gender and ailment", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Gender and ailment - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 88149.38, "postExectionTotalDeductionAmount": 1597.01, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1747297228753+0530)/", "category": "Repudiation", "subCategory": "ExclusionByProcedures", "subCategoryDisplayName": "Repudiation By Procedure", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Repudiation By Procedure - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 88149.38, "postExectionTotalDeductionAmount": 1597.01, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1747297228753+0530)/", "category": "Repudiation", "subCategory": "ExclusionByAilment", "subCategoryDisplayName": "Repudiation By Ailment Category", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Repudiation By Ailment Category - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 88149.38, "postExectionTotalDeductionAmount": 1597.01, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1747297228763+0530)/", "category": "Repudiation", "subCategory": "RepudiationByRefractiveError", "subCategoryDisplayName": "Refractive Error", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Refractive Error - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 88149.38, "postExectionTotalDeductionAmount": 1597.01, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1747297228763+0530)/", "category": "Repudiation", "subCategory": "DenyObesity", "subCategoryDisplayName": "Obesity", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Obesity - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 88149.38, "postExectionTotalDeductionAmount": 1597.01, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1747297228764+0530)/", "category": "Repudiation", "subCategory": "OnNumberOfChildren", "subCategoryDisplayName": "Number of living children allowed for maternity coverage", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Number of living children allowed for maternity coverage - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 88149.38, "postExectionTotalDeductionAmount": 1597.01, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1747297228764+0530)/", "category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Matching Config Count: 2", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Matching Config Count: 2", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Standard (IRDAI) - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Applying Excess of Tariff ", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Calcualtion on Excess of Room Tariff Start", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Hospital room rent master details", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Opted Room Category :SinglePrivateRoom", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Tariff Room Rent From Master:9500.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Tariff ICU Room Rent From Master:9500.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Calcualtion on Excess of Room Tariff Ends", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Calculation On Excess Of Package Starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Calculation On Excess Of Traiff Package", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Excess Of Traiff Package : Opted Room Category : PRIVATE", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Package Id : 3796467", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Package Master Details : Package Id : 3796467 General:69700,Sharing:73800,Private:82000,Deluxe:82000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "DIALYSIS COUNT :0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Relevant Quantity :1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Package Amount :82000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Calculation On Excess Of Package Ends", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Standard (IRDAI) - Execution ends", "loggerType": "Information"}], "matchingConfig": [{"claimType": "PreAuth"}, {"claimType": "Cashless"}], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 88149.38, "preExectionTotalDeductionAmount": 1597.01, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 88149.38, "postExectionTotalDeductionAmount": 1597.01, "configRuleLogObject": [{"displayName": "ClaimType", "value": "PreAuth", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "ClaimType", "value": "Cashless", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}]}, {"timeOfOccurrence": "/Date(1747297228765+0530)/", "category": "PropotinateDeduction", "subCategory": "FixedLimitWithAilmentExclusion", "subCategoryDisplayName": "By Room Value With Ailment Exclusion", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Matching Config Count: 2", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Matching Config Count: 2", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room Rent limit applied based on 3.5% of sum insured 500000.0 is 17500.00", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room Rent limit applied based on 5% of sum insured 500000.0 is 25000.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "*****Calulating Eligibile Room Category By RoomRent Started*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Rule Suggested, Eligible Room Rent 17500.00", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Rule Suggested, Eligible Room Rent Tolerance 105", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Rule Suggested, Eligible Room Rent ToleranceType Percentage", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "ICU Category: Intensive,ICU,HDU,Burnward", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "DELUXE Category: Suite,Delux,DeluxeRoom", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Hospital room rent master details", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Hospital room rent master details After filtering ICU", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "No excat room rent details for the eligible room rent :17500.00", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Now trying to find room rent details by applying tolerance by :Percentage", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Tolerance Amount:18375.0000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Finding room rent details from master , taking max price which is less than tolerance amount 18375.0000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Max room rent price 18000.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room Rent master details which is used to calculate eligible room rent", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Rule Calculated EligibleRoomCategory : DELUXE", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "*****Calulating Eligibile Room Category By RoomRent END*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "*****Set Proportionate Calculation Started*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Calculation On Excess Of Package Starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Calculation On Excess Of Eligible Package", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Excess Of Eligible Package : Eligible Room Category : DELUXE", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Package Id : 3796467", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Package Master Details : Package Id : 3796467 General:69700,Sharing:73800,Private:82000,Deluxe:82000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "DIALYSIS COUNT :0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Relevant Quantity :1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Package Amount :82000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Calculation On Excess Of Package Ends", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Proportionate CalCulation MaxEligibilityNormal: 17500.00", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Proportionate Calculation MaxEligibilityICU: 25000.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Proportionate CalCulation Availed ICU Length Of Stay: 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Proportionate CalCulation Availed Normal Length Of Stay: 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Proportionate CalCulation Max Normal Room Rent Allowed: 0.00", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Proportionate CalCulation Proportionate Factor Normal: 100%", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 0%", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Proportionate CalCulation Proportionate Factor ICU: 100%", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "*****Proportionate Calculation End*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "*****Set Proportionate Calculation Ended*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Proportionate deduction limit: 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Proportionate deduction limit: 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Proportionate Calculation Total Admissible Amount Rs: 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "By Room Value With Ailment Exclusion - Execution ends", "loggerType": "Information"}], "matchingConfig": [{"type of  Room": "Normal", "provider Type": "False", "propotionateApplicable": "True", "skip Proportionate On DeathCase": "False", "skip Proportionate On AccidentCase": "False", "topUp Configured": "False", "relation to Primary beneficiary": "ALL", "grade": "any", "min Age": "0", "max Age": "0", "room Value Limit Defined By": "PercentageOfTotalSI", "room Value Type": "3.5", "room Capped Limit": "0", "proportionate Limit Defined By": "Amount", "proportionate Limit Defined By Value": "0", "min SI": "0", "max SI": "0", "benefitScope": "Policy"}, {"type of  Room": "ICU", "provider Type": "False", "propotionateApplicable": "False", "skip Proportionate On DeathCase": "False", "skip Proportionate On AccidentCase": "False", "topUp Configured": "False", "relation to Primary beneficiary": "ALL", "grade": "any", "min Age": "0", "max Age": "0", "room Value Limit Defined By": "PercentageOfTotalSI", "room Value Type": "5", "room Capped Limit": "0", "proportionate Limit Defined By": "Amount", "proportionate Limit Defined By Value": "0", "min SI": "0", "max SI": "0", "benefitScope": "Policy"}], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 86551.99, "postExecutionTotalBillAmount": 88149.38, "postExectionTotalDeductionAmount": 1597.01, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1747297228767+0530)/", "category": "Copay", "subCategory": "NotApplicable", "subCategoryDisplayName": "Not Applicable", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Not Applicable - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 86551.99, "postExecutionTotalBillAmount": 88149.38, "postExectionTotalDeductionAmount": 1597.01, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1747297228767+0530)/", "category": "AilmentCapping", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Standard (IRDAI) - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Standard (IRDAI) - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 86551.99, "preExecutionTotalBillAmount": 88149.38, "preExectionTotalDeductionAmount": 1597.01, "postExecutionApprovedAmount": 86551.99, "postExecutionTotalBillAmount": 88149.38, "postExectionTotalDeductionAmount": 1597.01, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1747297228767+0530)/", "category": "AilmentCapping", "subCategory": "ProcedureCappingByApproachAndPostion", "subCategoryDisplayName": "Position and Approach", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Position and Approach - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 86551.99, "postExecutionTotalBillAmount": 88149.38, "postExectionTotalDeductionAmount": 1597.01, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1747297228772+0530)/", "category": "AilmentCapping", "subCategory": "ProcedureCappingByGrade", "subCategoryDisplayName": "Procedure", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Procedure - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 86551.99, "postExecutionTotalBillAmount": 88149.38, "postExectionTotalDeductionAmount": 1597.01, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1747297228777+0530)/", "category": "AilmentCapping", "subCategory": "MaternityWellnessBabyCapping", "subCategoryDisplayName": "Maternity, wellness baby care & Pre-Post natal", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Maternity sub-limit based on amount: 50000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Excess of Maternity sub-limit value is 36551.99", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Maternity, wellness baby care & Pre-Post natal - Execution ends", "loggerType": "Information"}], "matchingConfig": [{"relation To Primary Beneficiary": "Employee, Self, Spouse, Wife", "grade": "any", "from Sum Insured Band": "1", "to Sum Insured Band": "1000000", "ailment Category for Maternity Limit": "MATERNITY_LSCS", "ailmentCategoryForExpn": "MATERNITY_LSCS", "maternity Capping Defined By": "Amount", "maternity Capping Defined Value(Percentage/Amount)": "50000", "maternity Upper/Lower Capping Limit For Comparison": "0", "well Baby 'Bill for'": "Consultation Well baby charges", "wellness baby Capping Defined By": "NONE", "wellness baby Capping Defined value": "0", "wellness baby outside maternity": "False", "pre-Post Natal Within Maternity": "True", "pre-Post Natal Calculated outside maternity": "False", "pre-post natal deduction By": "NONE", "pre-post natal deduction Value(Percentage/Amount)": "0", "is Capping Applicable Per Admission Basis": "False", "is Capping Applicable Per Family": "False", "benefitScope": "Policy"}], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 86551.99, "postExecutionTotalBillAmount": 88149.38, "postExectionTotalDeductionAmount": 1597.01, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1747297228851+0530)/", "category": "SIRestriction", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Standard - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 50000, "postExecutionTotalBillAmount": 88149.38, "postExectionTotalDeductionAmount": 38149, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1747297228851+0530)/", "category": "SIRestriction", "subCategory": "OtherHealthCareSIRestriction", "subCategoryDisplayName": "SI Restriction By Health Care Types", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "SI Restriction By Health Care Types - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 50000, "postExecutionTotalBillAmount": 88149.38, "postExectionTotalDeductionAmount": 38149, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1747297228853+0530)/", "category": "PolicyBenefits", "subCategory": "NotApplicable", "subCategoryDisplayName": "Not Applicable", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Not Applicable - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 50000, "postExecutionTotalBillAmount": 88149.38, "postExectionTotalDeductionAmount": 38149, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1747297228853+0530)/", "category": "UCRDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "*****Calculation On SOC Started*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "NO SOC Details Found", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "*****Calculation On SOC Ends*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521392+0530)/", "message": "Standard - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 50000, "postExecutionTotalBillAmount": 88149.38, "postExectionTotalDeductionAmount": 38149, "configRuleLogObject": []}], "claimRuleResults": {"denialType": "NA", "claimDenials": []}}, {"logCreatedDate": "/Date(1747297228544)/", "typeOfLog": "Decision", "ruleEngineLogs": [], "decisionRuleResults": {"_decisionResults": [{"status": "AUTOMATE", "reasons": ["Request To Execute Rule Engine"]}], "claimDenials": []}}]}, {"claimNo": 130768274, "logs": [{"logCreatedDate": "/Date(1746870866686)/", "typeOfLog": "ClaimSecondLevelApproval", "ruleEngineLogs": []}, {"logCreatedDate": "/Date(1746870866470)/", "typeOfLog": "Auomation", "ruleEngineLogs": []}, {"logCreatedDate": "/Date(1746870866243)/", "typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": [{"timeOfOccurrence": "/Date(1746870866178+0530)/", "category": "BaseConfiguration", "subCategory": "DefautChecks", "subCategoryDisplayName": "Default Configuration", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Discount not applicable for pre-auth initial cases", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Claim types considered for discount override: PreHosp,PostHosp,HospBenefit,HealthCheckUp,RI,ReOpenClaim", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Override Discount By <PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON> not executed as no matching rule found.", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Default Configuration - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 0, "postExectionTotalDeductionAmount": 0, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1746870866178+0530)/", "category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "*****Hospital Discount Calculation Started*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Matching Config Count: 3", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Matching Config Count: 3", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Standard - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "All Bills are pre-post bills so overiding the bill date to DOA", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "MMOU Discount Calculated: 0.00", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "HMOU Discount Calculated: 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "MAX Discount Calculated: 0.00", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Calculated Discount Amount: 0.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Calculated Discount Remarks: ", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "*****HospitalDiscount Calculation Ends*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "*****Calculate UCR Started*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "User Selected Bill UCR Bill Amount : 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "*****CalculateHospitalDiscount Calculation Ends*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Standard - Execution ends", "loggerType": "Information"}], "matchingConfig": [{"claimType": "None"}, {"claimType": "PreAuth"}, {"claimType": "Cashless"}], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 92625, "postExectionTotalDeductionAmount": 0, "configRuleLogObject": [{"displayName": "ClaimType", "value": "None", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "ClaimType", "value": "PreAuth", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "ClaimType", "value": "Cashless", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}]}, {"timeOfOccurrence": "/Date(1746870866179+0530)/", "category": "NonMedicalExpense", "subCategory": "ManualOverRideBills", "subCategoryDisplayName": "Manual OverRide Bills", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Manual OverRide Bills - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 92625, "postExectionTotalDeductionAmount": 0, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1746870866179+0530)/", "category": "NonMedicalExpense", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Standard (IRDAI) - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Due to Initial PA NME and Relevant Quantity is Not Considered", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Standard (IRDAI) - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 92625, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 92625, "postExectionTotalDeductionAmount": 0, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1746870866179+0530)/", "category": "NonMedicalExpense", "subCategory": "HandelNMEByPreAuthType", "subCategoryDisplayName": "Estimate For Initial", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "*****Calulating CalculateNMEDeduction Started*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "CalculateNMEDeduction isInitialPA: TRUE", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "NMEDeduction percentage: 100", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "*****Calulating CalculateNMEDeduction End*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Estimate For Initial - Execution ends", "loggerType": "Information"}], "matchingConfig": [{"isPackage": "True", "payablePercentage": "100"}], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 92625, "postExectionTotalDeductionAmount": 0, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1746870866180+0530)/", "category": "NonMedicalExpense", "subCategory": "CapAmbulanceCharges", "subCategoryDisplayName": "Ambulance Limit By Amount", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "No deduction found in the historic claims", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Max Payable Ambulance Charge: 5000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Current Payable Ambulance Charge if Any applicable: 5000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Ambulance Limit By Amount - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 92625, "postExectionTotalDeductionAmount": 0, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1746870866180+0530)/", "category": "NonMedicalExpense", "subCategory": "RIPrePostNMEDeductions", "subCategoryDisplayName": "Reimbursement Pre-Post Deductions", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Reimbursement Pre-Post Deductions - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 92625, "postExectionTotalDeductionAmount": 0, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1746870866180+0530)/", "category": "NonMedicalExpense", "subCategory": "ManualOverRideBills", "subCategoryDisplayName": "Manual OverRide Bills", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Manual OverRide Bills - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 92625, "postExectionTotalDeductionAmount": 0, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1746870866180+0530)/", "category": "Repudiation", "subCategory": "ExclusionByGenderAilment", "subCategoryDisplayName": "Gender and ailment", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Gender and ailment - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 92625, "postExectionTotalDeductionAmount": 0, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1746870866181+0530)/", "category": "Repudiation", "subCategory": "ExclusionByProcedures", "subCategoryDisplayName": "Repudiation By Procedure", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Repudiation By Procedure - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 92625, "postExectionTotalDeductionAmount": 0, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1746870866181+0530)/", "category": "Repudiation", "subCategory": "ExclusionByAilment", "subCategoryDisplayName": "Repudiation By Ailment Category", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Repudiation By Ailment Category - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 92625, "postExectionTotalDeductionAmount": 0, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1746870866189+0530)/", "category": "Repudiation", "subCategory": "RepudiationByRefractiveError", "subCategoryDisplayName": "Refractive Error", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Refractive Error - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 92625, "postExectionTotalDeductionAmount": 0, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1746870866189+0530)/", "category": "Repudiation", "subCategory": "DenyObesity", "subCategoryDisplayName": "Obesity", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Obesity - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 92625, "postExectionTotalDeductionAmount": 0, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1746870866190+0530)/", "category": "Repudiation", "subCategory": "OnNumberOfChildren", "subCategoryDisplayName": "Number of living children allowed for maternity coverage", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Number of living children allowed for maternity coverage - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 92625, "postExectionTotalDeductionAmount": 0, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1746870866190+0530)/", "category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Matching Config Count: 2", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Matching Config Count: 2", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Standard (IRDAI) - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Applying Excess of Tariff ", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Calcualtion on Excess of Room Tariff Start", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Hospital room rent master details", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Opted Room Category :SinglePrivateRoom", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Tariff Room Rent From Master:9500.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Tariff ICU Room Rent From Master:9500.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Calcualtion on Excess of Room Tariff Ends", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Calculation On Excess Of Package Starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Calculation On Excess Of Traiff Package", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Excess Of Traiff Package : Opted Room Category : PRIVATE", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Package Id : 3796467", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Package Master Details : Package Id : 3796467 General:69700,Sharing:73800,Private:82000,Deluxe:82000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "DIALYSIS COUNT :0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Relevant Quantity :1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Package Amount :82000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Excess Of Tariff Package Calculated ; Package Amount: 92625.00 , Contracted Package Amount: 82000 ", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Calculation On Excess Of Package Ends", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Standard (IRDAI) - Execution ends", "loggerType": "Information"}], "matchingConfig": [{"claimType": "PreAuth"}, {"claimType": "Cashless"}], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 92625, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 0, "postExecutionTotalBillAmount": 82000, "postExectionTotalDeductionAmount": 0, "configRuleLogObject": [{"displayName": "ClaimType", "value": "PreAuth", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}, {"displayName": "ClaimType", "value": "Cashless", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true}]}, {"timeOfOccurrence": "/Date(1746870866190+0530)/", "category": "PropotinateDeduction", "subCategory": "FixedLimitWithAilmentExclusion", "subCategoryDisplayName": "By Room Value With Ailment Exclusion", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Matching Config Count: 2", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Matching Config Count: 2", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room Rent limit applied based on 3.5% of sum insured 500000.0 is 17500.00", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room Rent limit applied based on 5% of sum insured 500000.0 is 25000.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "*****Calulating Eligibile Room Category By RoomRent Started*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Rule Suggested, Eligible Room Rent 17500.00", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Rule Suggested, Eligible Room Rent Tolerance 105", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Rule Suggested, Eligible Room Rent ToleranceType Percentage", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "ICU Category: Intensive,ICU,HDU,Burnward", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "DELUXE Category: Suite,Delux,DeluxeRoom", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Hospital room rent master details", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Hospital room rent master details After filtering ICU", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "No excat room rent details for the eligible room rent :17500.00", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Now trying to find room rent details by applying tolerance by :Percentage", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Tolerance Amount:18375.0000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Finding room rent details from master , taking max price which is less than tolerance amount 18375.0000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Max room rent price 18000.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room Rent master details which is used to calculate eligible room rent", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Rule Calculated EligibleRoomCategory : DELUXE", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "*****Calulating Eligibile Room Category By RoomRent END*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "*****Set Proportionate Calculation Started*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Calculation On Excess Of Package Starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Calculation On Excess Of Eligible Package", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Excess Of Eligible Package : Eligible Room Category : DELUXE", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Package Id : 3796467", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Package Master Details : Package Id : 3796467 General:69700,Sharing:73800,Private:82000,Deluxe:82000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "DIALYSIS COUNT :0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Relevant Quantity :1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Package Amount :82000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Calculation On Excess Of Package Ends", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Proportionate CalCulation MaxEligibilityNormal: 17500.00", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Proportionate Calculation MaxEligibilityICU: 25000.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Due to Initial PA LengthOfStay calculated on DOD - DOA ", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Proportionate CalCulation Availed ICU Length Of Stay: 2", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Proportionate CalCulation Availed Normal Length Of Stay: 2", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Proportionate CalCulation Max Normal Room Rent Allowed: 35000.00", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Proportionate CalCulation Proportionate Factor Normal: 100%", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Proportionate CalCulation Max ICU Room Rent Allowed: 50000.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Proportionate CalCulation Proportionate Factor ICU: 100%", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "*****Proportionate Calculation End*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "*****Set Proportionate Calculation Ended*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Proportionate deduction limit: 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Proportionate deduction limit: 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Proportionate Calculation Total Admissible Amount Rs: 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "By Room Value With Ailment Exclusion - Execution ends", "loggerType": "Information"}], "matchingConfig": [{"type of  Room": "Normal", "provider Type": "False", "propotionateApplicable": "True", "skip Proportionate On DeathCase": "False", "skip Proportionate On AccidentCase": "False", "topUp Configured": "False", "relation to Primary beneficiary": "ALL", "grade": "any", "min Age": "0", "max Age": "0", "room Value Limit Defined By": "PercentageOfTotalSI", "room Value Type": "3.5", "room Capped Limit": "0", "proportionate Limit Defined By": "Amount", "proportionate Limit Defined By Value": "0", "min SI": "0", "max SI": "0", "benefitScope": "Policy"}, {"type of  Room": "ICU", "provider Type": "False", "propotionateApplicable": "False", "skip Proportionate On DeathCase": "False", "skip Proportionate On AccidentCase": "False", "topUp Configured": "False", "relation to Primary beneficiary": "ALL", "grade": "any", "min Age": "0", "max Age": "0", "room Value Limit Defined By": "PercentageOfTotalSI", "room Value Type": "5", "room Capped Limit": "0", "proportionate Limit Defined By": "Amount", "proportionate Limit Defined By Value": "0", "min SI": "0", "max SI": "0", "benefitScope": "Policy"}], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 82000, "postExecutionTotalBillAmount": 82000, "postExectionTotalDeductionAmount": 0, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1746870866191+0530)/", "category": "Copay", "subCategory": "NotApplicable", "subCategoryDisplayName": "Not Applicable", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Not Applicable - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 82000, "postExecutionTotalBillAmount": 82000, "postExectionTotalDeductionAmount": 0, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1746870866191+0530)/", "category": "AilmentCapping", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Standard (IRDAI) - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Standard (IRDAI) - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 82000, "preExecutionTotalBillAmount": 82000, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 82000, "postExecutionTotalBillAmount": 82000, "postExectionTotalDeductionAmount": 0, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1746870866192+0530)/", "category": "AilmentCapping", "subCategory": "ProcedureCappingByApproachAndPostion", "subCategoryDisplayName": "Position and Approach", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Position and Approach - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 82000, "postExecutionTotalBillAmount": 82000, "postExectionTotalDeductionAmount": 0, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1746870866197+0530)/", "category": "AilmentCapping", "subCategory": "ProcedureCappingByGrade", "subCategoryDisplayName": "Procedure", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Procedure - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 82000, "postExecutionTotalBillAmount": 82000, "postExectionTotalDeductionAmount": 0, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1746870866201+0530)/", "category": "AilmentCapping", "subCategory": "MaternityWellnessBabyCapping", "subCategoryDisplayName": "Maternity, wellness baby care & Pre-Post natal", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Maternity sub-limit based on amount: 50000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Excess of Maternity sub-limit value is 32000.00", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Maternity, wellness baby care & Pre-Post natal - Execution ends", "loggerType": "Information"}], "matchingConfig": [{"relation To Primary Beneficiary": "Employee, Self, Spouse, Wife", "grade": "any", "from Sum Insured Band": "1", "to Sum Insured Band": "1000000", "ailment Category for Maternity Limit": "MATERNITY_LSCS", "ailmentCategoryForExpn": "MATERNITY_LSCS", "maternity Capping Defined By": "Amount", "maternity Capping Defined Value(Percentage/Amount)": "50000", "maternity Upper/Lower Capping Limit For Comparison": "0", "well Baby 'Bill for'": "Consultation Well baby charges", "wellness baby Capping Defined By": "NONE", "wellness baby Capping Defined value": "0", "wellness baby outside maternity": "False", "pre-Post Natal Within Maternity": "True", "pre-Post Natal Calculated outside maternity": "False", "pre-post natal deduction By": "NONE", "pre-post natal deduction Value(Percentage/Amount)": "0", "is Capping Applicable Per Admission Basis": "False", "is Capping Applicable Per Family": "False", "benefitScope": "Policy"}], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 82000, "postExecutionTotalBillAmount": 82000, "postExectionTotalDeductionAmount": 0, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1746870866242+0530)/", "category": "SIRestriction", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Standard - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 50000, "postExecutionTotalBillAmount": 82000, "postExectionTotalDeductionAmount": 32000, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1746870866242+0530)/", "category": "SIRestriction", "subCategory": "OtherHealthCareSIRestriction", "subCategoryDisplayName": "SI Restriction By Health Care Types", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "SI Restriction By Health Care Types - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 50000, "postExecutionTotalBillAmount": 82000, "postExectionTotalDeductionAmount": 32000, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1746870866243+0530)/", "category": "PolicyBenefits", "subCategory": "NotApplicable", "subCategoryDisplayName": "Not Applicable", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Not Applicable - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 50000, "postExecutionTotalBillAmount": 82000, "postExectionTotalDeductionAmount": 32000, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1746870866243+0530)/", "category": "UCRDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "*****Calculation On SOC Started*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "NO SOC Details Found", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "*****Calculation On SOC Ends*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749809521393+0530)/", "message": "Standard - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0, "preExecutionTotalBillAmount": 0, "preExectionTotalDeductionAmount": 0, "postExecutionApprovedAmount": 50000, "postExecutionTotalBillAmount": 82000, "postExectionTotalDeductionAmount": 32000, "configRuleLogObject": []}], "claimRuleResults": {"denialType": "NA", "claimDenials": []}}, {"logCreatedDate": "/Date(1746870866054)/", "typeOfLog": "Decision", "ruleEngineLogs": [], "decisionRuleResults": {"_decisionResults": [{"status": "AUTOMATE", "reasons": ["Request To Execute Rule Engine"]}], "claimDenials": []}}]}]