{"claimDetails": [{"canMoveToLC": false, "relatedPreAuths": [{"canMoveToLC": false, "id": *********, "recdate": "/Date(*************+0530)/", "typeId": 0, "doa": "/Date(*************+0530)/", "dod": "/Date(*************+0530)/", "type": "Initial PA", "strAmount": "1,45,308", "strTotalBillAmt": "1,45,308", "strApprovedAmt": "93,440", "amount": 145308, "statusId": 7, "statusName": "Settled", "totalBillAmt": 0, "approvedAmt": 93440, "letterRemarks": "Rs.­10000\\-deducted as 10% or maximum up to Rs.10000 copay applicable as per policy T & C", "infoReqDate": "/Date(-**************)/", "docRecDate": "/Date(-**************)/", "appDate": "/Date(-**************)/", "settlementDate": "/Date(-**************)/", "denialDate": "/Date(-**************)/", "lastAuditDate": "/Date(-**************)/", "regionId": 14, "emailId": "0", "faxMsgId": "706", "provisionalDiag": "REFER PREAUTH", "budget": 145308, "isFinal": true, "isLessThanIntial": false, "check": false, "ailmentCode": "12864", "paDeductionDetails": [{"slNo": 67166135, "claimId": *********, "deductionId": 5, "deductionDesc": "Hospital Discount", "amount": 9402, "remarks": "Pharmacy & Medicine Charges: 2760.22,Hospital Charges: 1827.11,Consultant Charges: 1299.90,Surgery Charges: 94.74,Investigation & Lab Charges: 2460.50,Miscellaneous Charges: 959.57 on Rs. 103440"}, {"slNo": 67166136, "claimId": *********, "deductionId": 1, "deductionDesc": "Copay", "amount": 10000, "remarks": "Minimum capped value for copay applicable between 10%  of admissible amount 103439.96 and flat amount 10000 is 10000 (Mandatory Copay) on Rs. 103440"}], "paDenialClauses": [], "bills": [{"id": 312269973, "for": "package", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 5160, "payableAmt": 5160, "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 15, "clmBillLOS": 1}, {"id": 312819046, "for": "iv fluids / disposables", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 11438, "payableAmt": 3407, "dedReasonDesc": "tegaderm 1633 (7x8.5cm) for cannula hsn:30051020 ):-214.00,venflon i i.v canulla-20g(bd) ( hsn:90183930 ):-342.50,accu-chek safe-t-pr0 uno ( hsn:90183990 ):-345.00,dc face mask 3 layer loop bulk 50s (doctor choice) hsn:62103090 ):-120.00,sensi care nitrile p/f exam gloves-m 100s (mediline) (:-1800.00,syringes 10ml bd (discardit) ( hsn:90183100 ):-185.00,n95 venus v-4400 niosh flat ffr mask (venus) hsn:63079090 ):-285.00,syringe 2 ml w/n. (nipro) ( hsn:90183990 ):-22.00,(b) accucheck performa (12062967 ):-180.00,gastroenterology disposable mouth piece (15411814 ):-150.00,(b) accucheck performa (12067846 ):-180.00,(b) accucheck performa (12073003 ):-180.00,b 0 syringes 20ml (discard) ( hsn:90183990 ) (pmis2243673227:-162.00,syringe 3ml nipro ( hsn:90183990 ) (pmis2243620620 ):-15.00,tegaderm 1633 (7x8.5cm) for cannula (hsn:30051020 ):-214.00,ultra fine minipen needles (b.d)# ( hsn:90183220 ):-41.20,dc face mask 3 layer loop bulk 50s (doctor choice) (hsn:62103090 ):-30.00,dc under pad 60x90cm hsn:48182000 ):-99.00,gauze swabs 7.5 7.5cm 12ply(sterile): # (:-132.00,gauze swabs 7.5 7.5cm 12ply(sterile) # (:-132.00,tegaderm 1657 (8.5x11.5cm) for central line hsn:30051020:-1604.00,dc face mask 3 layer loop bulk 50s (doctor choice) (hsn:62103090:-50.00,dc under pad 60x90cm ( hsn:48182000 ):-99.00,ecg electrodes (3m health care) ( hsn:90189099 ):-94.50,flexi mask (adult) ## hsn:90189099 ):-331.00,oxy set adult nasal oxygen catheter sh-2016s (romsons):-275.00,sensi care nitrile p/f exam gloves-m 100s (mediline):-400.00,surgeons cap (dchoice) ( hsn:62103090 ):-37.50,syringe 3ml nipro hsn:90183990 (pmis2243644051 ):-15.00,syringes 10ml bd (discardit) ( hsn:90183100:-74.00,syringes 5ml b.d. hsn:90183100 (pmis2243644051:-42.00,accucheck performa :-180.00", "nonPayableAmt": 8031, "packageID": 0, "isDeleted": false, "ctype": 14, "clmBillLOS": 213}, {"id": 312819052, "for": "other surgical item charges", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 1400, "payableAmt": 1194, "dedReasonDesc": "Proportionate Deduction due to Higher Room Rent Occupied:-205.80", "nonPayableAmt": 206, "packageID": 0, "isDeleted": false, "ctype": 9, "clmBillLOS": 1}, {"id": 312819054, "for": "radiology (mri/ct/x-ray)", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 22020, "payableAmt": 22020, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 2, "clmBillLOS": 3}, {"id": 312819056, "for": "anaesthaesthist charges", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 1075, "payableAmt": 917, "dedReasonDesc": "Proportionate Deduction due to Higher Room Rent Occupied:-158.02", "nonPayableAmt": 158, "packageID": 0, "isDeleted": false, "ctype": 10, "clmBillLOS": 1}, {"id": 312819047, "for": "medicines/drugs", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 2952, "payableAmt": 2502, "dedReasonDesc": "dc cutarub-chg 500ml with pump ( hsn:30041030:-450.00", "nonPayableAmt": 450, "packageID": 0, "isDeleted": false, "ctype": 14, "clmBillLOS": 58}, {"id": 312819048, "for": "gst on room rent", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 1209, "payableAmt": 1031, "dedReasonDesc": "Excess of Room rent charges for higher room opted:-177.72", "nonPayableAmt": 178, "packageID": 0, "isDeleted": false, "ctype": 1, "clmBillLOS": 2}, {"id": 312819049, "for": "room rent", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 26000, "payableAmt": 22178, "dedReasonDesc": "Excess of Room rent charges for higher room opted:-3822.00", "nonPayableAmt": 3822, "packageID": 0, "isDeleted": false, "ctype": 1, "clmBillLOS": 4}, {"id": 312819050, "for": "consultation / visit", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 18134, "payableAmt": 12840, "dedReasonDesc": "Proportionate Deduction due to Higher Room Rent Occupied:-2212.78,dr. nithya m (psychiatry ):-2151.00,nutritional and functional assessment:-930.00", "nonPayableAmt": 5294, "packageID": 0, "isDeleted": false, "ctype": 10, "clmBillLOS": 8}, {"id": 312819051, "for": "ventilator charges", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 1000, "payableAmt": 853, "dedReasonDesc": "Proportionate Deduction due to Higher Room Rent Occupied:-147.00", "nonPayableAmt": 147, "packageID": 0, "isDeleted": false, "ctype": 1, "clmBillLOS": 1}, {"id": 312819053, "for": "neurology (eeg,emg)", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 1080, "payableAmt": 1080, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 2, "clmBillLOS": 1}, {"id": 312819055, "for": "other miscellaneous charges", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 13380, "payableAmt": 0, "dedReasonDesc": "hospital services and treatment monitoring and management charges(private):-13200.00,ENDOSCOPY PRINTOUT CHARGES:-180.00", "nonPayableAmt": 13380, "packageID": 0, "isDeleted": false, "ctype": 4, "clmBillLOS": 5}, {"id": 312819057, "for": "bio chemistry (lft,rf,lipid)", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 13260, "payableAmt": 13260, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 2, "clmBillLOS": 3}, {"id": 312819058, "for": "external durable appliances", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 26400, "payableAmt": 26400, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 14, "clmBillLOS": 4}, {"id": 312819059, "for": "documentation charges", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 800, "payableAmt": 0, "dedReasonDesc": "medical records:-800.00", "nonPayableAmt": 800, "packageID": 0, "isDeleted": false, "ctype": 4, "clmBillLOS": 1}], "isClaimMOS": 0, "isClaimLCM": 0, "pd": "REFER PREAUTH", "rcl": "REFER PREAUTH", "rentPerDay": 0, "stayDays": 0, "treatmentName": "Conservative Management", "claimPriority": "", "clmSourceName": "ClaimBook", "paExternalStatus": "Pre-Auth Processed", "paInsurerStatus": "Pre-Auth Processed", "clmOtherTreatmentDetails": "{\"HealthCareSystem\":\"Allopathy\",\"PrimaryApproach\":\"\",\"PrimaryPosition\":\"\",\"PrimaryAilmentDuration\":\"1\",\"SecondaryApproach\":null,\"SecondaryPosition\":null,\"SecondaryAilmentDuration\":\"1\",\"TertiaryApproach\":null,\"TertiaryPosition\":null,\"TertiaryAilmentDuration\":null,\"EligibleNormalRoomRent\":5802,\"EligibleICURoomRent\":0,\"IsRuleConfigured\":true,\"IsEmergency\":false,\"IsDigitized\":true,\"IsRuleEngineDenied\":false}", "clmTotalCostofCare": 145308, "clmNetClaimedAmt": 145308, "clmHospDiscountOnBill": 0}], "billSummary": [{"id": 312819731, "typeDesc": "Pharmacy & Medicine Charges", "for": "iv fluids / disposables", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 11438, "payableAmt": 3407, "dedReasonDesc": "tegaderm 1633 (7x8.5cm) for cannula hsn:30051020 ):-214.00,venflon i i.v canulla-20g(bd) ( hsn:90183930 ):-342.50,accu-chek safe-t-pr0 uno ( hsn:90183990 ):-345.00,dc face mask 3 layer loop bulk 50s (doctor choice) hsn:62103090 ):-120.00,sensi care nitrile p/f exam gloves-m 100s (mediline) (:-1800.00,syringes 10ml bd (discardit) ( hsn:90183100 ):-185.00,n95 venus v-4400 niosh flat ffr mask (venus) hsn:63079090 ):-285.00,syringe 2 ml w/n. (nipro) ( hsn:90183990 ):-22.00,(b) accucheck performa (12062967 ):-180.00,gastroenterology disposable mouth piece (15411814 ):-150.00,(b) accucheck performa (12067846 ):-180.00,(b) accucheck performa (12073003 ):-180.00,b 0 syringes 20ml (discard) ( hsn:90183990 ) (pmis2243673227:-162.00,syringe 3ml nipro ( hsn:90183990 ) (pmis2243620620 ):-15.00,tegaderm 1633 (7x8.5cm) for cannula (hsn:30051020 ):-214.00,ultra fine minipen needles (b.d)# ( hsn:90183220 ):-41.20,dc face mask 3 layer loop bulk 50s (doctor choice) (hsn:62103090 ):-30.00,dc under pad 60x90cm hsn:48182000 ):-99.00,gauze swabs 7.5 7.5cm 12ply(sterile): # (:-132.00,gauze swabs 7.5 7.5cm 12ply(sterile) # (:-132.00,tegaderm 1657 (8.5x11.5cm) for central line hsn:30051020:-1604.00,dc face mask 3 layer loop bulk 50s (doctor choice) (hsn:62103090:-50.00,dc under pad 60x90cm ( hsn:48182000 ):-99.00,ecg electrodes (3m health care) ( hsn:90189099 ):-94.50,flexi mask (adult) ## hsn:90189099 ):-331.00,oxy set adult nasal oxygen catheter sh-2016s (romsons):-275.00,sensi care nitrile p/f exam gloves-m 100s (mediline):-400.00,surgeons cap (dchoice) ( hsn:62103090 ):-37.50,syringe 3ml nipro hsn:90183990 (pmis2243644051 ):-15.00,syringes 10ml bd (discardit) ( hsn:90183100:-74.00,syringes 5ml b.d. hsn:90183100 (pmis2243644051:-42.00,accucheck performa :-180.00", "nonPayableAmt": 8031, "packageID": 0, "isDeleted": false, "ctype": 14, "clmBillLOS": 213}, {"id": 312819740, "typeDesc": "Miscellaneous Charges", "for": "other miscellaneous charges", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 13380, "payableAmt": 0, "dedReasonDesc": "hospital services and treatment monitoring and management charges(private):-13200.00,ENDOSCOPY PRINTOUT CHARGES:-180.00", "nonPayableAmt": 13380, "packageID": 0, "isDeleted": false, "ctype": 4, "clmBillLOS": 5}, {"id": 312819744, "typeDesc": "Miscellaneous Charges", "for": "documentation charges", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 800, "payableAmt": 0, "dedReasonDesc": "medical records:-800.00", "nonPayableAmt": 800, "packageID": 0, "isDeleted": false, "ctype": 4, "clmBillLOS": 1}, {"id": 312819730, "typeDesc": "Package", "for": "package", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 5160, "payableAmt": 5160, "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 15, "clmBillLOS": 1}, {"id": 312819732, "typeDesc": "Pharmacy & Medicine Charges", "for": "medicines/drugs", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 2952, "payableAmt": 2502, "dedReasonDesc": "dc cutarub-chg 500ml with pump ( hsn:30041030:-450.00", "nonPayableAmt": 450, "packageID": 0, "isDeleted": false, "ctype": 14, "clmBillLOS": 58}, {"id": 312819733, "typeDesc": "Hospital Charges", "for": "gst on room rent", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 1209, "payableAmt": 1031, "dedReasonDesc": "Excess of Room rent charges for higher room opted:-177.72", "nonPayableAmt": 178, "packageID": 0, "isDeleted": false, "ctype": 1, "clmBillLOS": 2}, {"id": 312819734, "typeDesc": "Hospital Charges", "for": "room rent", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 26000, "payableAmt": 22178, "dedReasonDesc": "Excess of Room rent charges for higher room opted:-3822.00", "nonPayableAmt": 3822, "packageID": 0, "isDeleted": false, "ctype": 1, "clmBillLOS": 4}, {"id": 312819735, "typeDesc": "Consultant Charges", "for": "consultation / visit", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 18134, "payableAmt": 12840, "dedReasonDesc": "Proportionate Deduction due to Higher Room Rent Occupied:-2212.78,dr. nithya m (psychiatry ):-2151.00,nutritional and functional assessment:-930.00", "nonPayableAmt": 5294, "packageID": 0, "isDeleted": false, "ctype": 10, "clmBillLOS": 8}, {"id": 312819736, "typeDesc": "Hospital Charges", "for": "ventilator charges", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 1000, "payableAmt": 853, "dedReasonDesc": "Proportionate Deduction due to Higher Room Rent Occupied:-147.00", "nonPayableAmt": 147, "packageID": 0, "isDeleted": false, "ctype": 1, "clmBillLOS": 1}, {"id": 312819737, "typeDesc": "Surgery Charges", "for": "other surgical item charges", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 1400, "payableAmt": 1194, "dedReasonDesc": "Proportionate Deduction due to Higher Room Rent Occupied:-205.80", "nonPayableAmt": 206, "packageID": 0, "isDeleted": false, "ctype": 9, "clmBillLOS": 1}, {"id": 312819738, "typeDesc": "Investigation & Lab Charges", "for": "neurology (eeg,emg)", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 1080, "payableAmt": 1080, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 2, "clmBillLOS": 1}, {"id": 312819739, "typeDesc": "Investigation & Lab Charges", "for": "radiology (mri/ct/x-ray)", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 22020, "payableAmt": 22020, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 2, "clmBillLOS": 3}, {"id": 312819741, "typeDesc": "Consultant Charges", "for": "anaesthaesthist charges", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 1075, "payableAmt": 917, "dedReasonDesc": "Proportionate Deduction due to Higher Room Rent Occupied:-158.02", "nonPayableAmt": 158, "packageID": 0, "isDeleted": false, "ctype": 10, "clmBillLOS": 1}, {"id": 312819742, "typeDesc": "Investigation & Lab Charges", "for": "bio chemistry (lft,rf,lipid)", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 13260, "payableAmt": 13260, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 2, "clmBillLOS": 3}, {"id": 312819743, "typeDesc": "Pharmacy & Medicine Charges", "for": "external durable appliances", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 26400, "payableAmt": 26400, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 14, "clmBillLOS": 4}], "deductionSummary": [{"slNo": 67166164, "claimId": *********, "deductionId": 5, "deductionDesc": "Hospital Discount", "amount": 9402, "remarks": "Pharmacy & Medicine Charges: 2760.22,Hospital Charges: 1827.11,Consultant Charges: 1299.90,Surgery Charges: 94.74,Investigation & Lab Charges: 2460.50,Miscellaneous Charges: 959.57 on Rs. 103440"}, {"slNo": 67166165, "claimId": *********, "deductionId": 1, "deductionDesc": "Copay", "amount": 10000, "remarks": "Minimum capped value for copay applicable between 10%  of admissible amount 103439.96 and flat amount 10000 is 10000 (Mandatory Copay) on Rs. 103440"}], "paDeductionSummary": [{"slNo": 67166135, "claimId": *********, "deductionId": 5, "deductionDesc": "Hospital Discount", "amount": 9402, "remarks": "Pharmacy & Medicine Charges: 2760.22,Hospital Charges: 1827.11,Consultant Charges: 1299.90,Surgery Charges: 94.74,Investigation & Lab Charges: 2460.50,Miscellaneous Charges: 959.57 on Rs. 103440"}, {"slNo": 67166136, "claimId": *********, "deductionId": 1, "deductionDesc": "Copay", "amount": 10000, "remarks": "Minimum capped value for copay applicable between 10%  of admissible amount 103439.96 and flat amount 10000 is 10000 (Mandatory Copay) on Rs. 103440"}], "priBenefDtls": {"empCode": "883136", "name": "Suresh G", "floaterSum": 1400000, "totalPremium": 0, "totalAmtSpent": 0, "totalAmtAuthorised": 70124, "balanceSI": 1329876, "bufferAmtTotal": 0, "bufferAmtAnnual": 0, "bufferAmtIncident": 0, "bufferAmtAuthorized": 0, "paBuffAmt": 0, "bufferAmtSpent": 0, "isVRS": false, "domLimit": 0, "isSIProtected": false, "priBenefID": 67147713}, "claimInfo": {"isClaimLocked": false, "isTechError": false, "isSuspected": false, "isInwarded": false, "eventId": *********, "insurerClmRefNo": "TP00392000025900728404", "infoReqSt1Date": "/Date(-**************)/", "infoReqSt2Date": "/Date(-**************)/", "docRecDate": "/Date(-**************+0553)/", "appDate": "/Date(-**************)/", "settlementDate": "/Date(-**************)/", "actualReceivedDate": "/Date(*************+0530)/", "denialDate": "/Date(-**************)/", "closedDate": "/Date(-**************)/", "clsDate": "/Date(-**************)/", "netPayAmount": "0", "taxAmount": "0", "chequeAmount": "0", "serviceTaxAmt": "0", "admissibleAmt": "0", "approverRemarks": "-", "id": *********, "typeDesc": "Cashless", "type": 3, "statusName": "Received", "statusId": 2, "receivedDate": "/Date(*************+0530)/", "preAuths": "*********", "compRefNo": "113382_38538", "irDate": "/Date(-**************)/", "settledDate": "/Date(-**************)/", "latestAuditDate": "/Date(-**************)/", "doa": "/Date(*************+0530)/", "dod": "/Date(*************+0530)/", "doctorName": "Dr. RAVIKUMAR N R", "lengthofStay": 5, "amount": 145308, "approvedAmt": 93440, "paApprovedAmt": "93,440", "varianceAmt": "0", "contactNumber": "99******88", "unMaskContactNumber": "**********", "mainNumber": *********, "claimRegionId": 14, "reciptdate": "01-Jan-0001", "epd": 0, "roomCategory": "Single private room", "accountVerified": "Account verified", "lastScanDocRecdate": "/Date(*************+0530)/", "latestPreAuthStatusId": 0, "roomCategoryId": 26, "isClaimLCM": 0, "clmSourceName": "ClaimBook", "claimMainClaim": 0, "claimExternalStatus": "<PERSON><PERSON><PERSON>", "claimInsurerStatus": "Pending for data entry", "clmOtherTreatmentDetails": "{\"HealthCareSystem\":\"Allopathy\",\"PrimaryApproach\":\"\",\"PrimaryPosition\":\"\",\"PrimaryAilmentDuration\":\"1\",\"SecondaryApproach\":null,\"SecondaryPosition\":null,\"SecondaryAilmentDuration\":\"1\",\"TertiaryApproach\":null,\"TertiaryPosition\":null,\"TertiaryAilmentDuration\":null,\"EligibleNormalRoomRent\":5802,\"EligibleICURoomRent\":0,\"IsRuleConfigured\":true,\"IsEmergency\":false,\"IsDigitized\":true}", "clmEligibleRoomCategory": 26, "eligibleRoomType": "Single private room", "clmTotalCostofCare": 145308, "clmHospDiscountOnBill": 0, "domiIrSplit": false}, "accountInfo": {"holderName": "Apollo Hospitals Enterprises Ltd", "ifscCode": "UTIB0000006", "bankAccountNumber": "00*********2164", "encryptedAccountNumber": "FZlIFLh3ZrkGjDRSQOM9VuFvpcyIm/hCE3hl6+Oi41jXitM6dB6GzWXUBlaz+4G/", "bankName": "Axis Bank", "branchAddress": "Mylapore Branch, Chennai - 600004", "type": 2, "mobileNumber": "99******44", "emailID": "chandra<PERSON><PERSON><EMAIL>", "isHospitalAccount": false, "updationDate": "/Date(*************+0530)/", "updatedByUserID": 0}, "ailmentSmry": {"code": "K29.60", "group": "", "treatmentName": "Conservative Management", "descs": "Other gastritis without bleeding", "summary": ".Other gastritis without bleeding", "ids": "12864", "ailmentBodySystem": "Digestive System"}, "hospHeader": {"id": 113382, "name": "Apollo Speciality Hospital", "cityId": 0, "city": "Chennai", "stateId": 0, "state": "Tamil Nadu", "pincode": "600095", "isPPN": "GIPSA", "typeOfCare": "Tertiary", "hospTariffURL": "http://mawire.mediassistindia.com/HN/PACKAGE%20TARIFF/GIPSA%20PPN/CHENNAI/Apollospeciality%********%2022jan16upayanambakkamchennaiTNppnpack.zip", "hospGeneralTariffURL": "http://*************/HN/PACKAGE%20TARIFF/TARIFF%20PRICES%20OF%20HOSPITALS/Apollosplty%********%204apr19upVanagaramTNtariff.zip", "hospisVerified": true}, "paymentDtls": {"id": 0, "dtChequeDate": "/Date(-**************)/"}, "benefDtls": {"policyHeader": {"polId": ********, "polCorpId": 1006639, "groupCorpId": 1049400, "polNo": "92000063250400000025", "polDevelopmentOfficer": "", "polDevelopmentAgent": "", "polInsCompanyID": 1, "polTypeID": 1, "polSubTypeID": 100, "polInsCompanyName": "The New India Assurance Co. Ltd", "polStartDate": "/Date(1743445800000+0530)/", "polEndDate": "/Date(1774895400000+0530)/", "polHldrName": "Tata Consultancy Services Ltd", "isFloater": false, "isRetail": false, "isMixed": false, "isActive": true, "polFloaterType": "Non Floater", "polTPAId": 93, "polSumInsured": "0", "polPaymentDirect": false, "polBlackListed": false, "maServicingBranchId": "2", "polTotalPremium": 0, "isPartialPending": false, "polRenewalRating": 0, "polCategoryId": 0, "polDeleteStatus": false, "tpaName": "Medi Assist Insurance TPA Pvt. Ltd.", "policySequence": 0, "isSuccess": false}, "mediassistId": 4017855552, "active": true, "name": "Dhakshinamoorthy K", "contactNo": "", "sex": "M", "dob": "/Date(-186384600000+0530)/", "age": 61, "relId": 11, "relName": "Father-in-law", "occId": 0, "cumBonusPer": 0, "domiLimit": 6000, "premium": 0, "balanceSI": 0, "sumInsured": 200000, "bsi": 0, "totalSumInsured": 0, "amtSpent": 145184, "amtAuthorised": 54816, "wef": "01-Apr-2025", "areaCode": "Chennai", "email": "sk**@tcs.com", "addedOn": "/Date(-**************)/", "addedBy": 0, "modifiedUser": 0, "modifiedOn": "/Date(-**************)/", "benefUserId": 151213808, "unmaskedEmail": "<EMAIL>"}, "claimextensiondetails": {"mcE_ID": 30907892, "mcE_ClaimNo": *********, "mcE_BSIExhausted": false, "mcE_CopayApplicable": false, "mcE_CREATEDBY": 0, "mcE_CREATEDON": "/Date(1749463332000+0530)/", "mcE_MODIFIEDBY": 0, "mcE_MODIFIEDON": "/Date(1749515717000+0530)/", "mcE_ClmIsDeathCase": false, "isTarrifMismatch": 0, "mcE_ClaimNME": 0, "claimPriority": "", "mcE_CorporateContribution": 0, "mcE_FloodReliefClaim": 0, "mcE_ClmApprovedAmtBucket1": 0, "mcE_ClmApprovedAmtBucket2": 0, "mcE_ClmApprovedAmtBucket3": 0, "mcE_ClmApprovedAmtBucket4": 0, "claimAssignedToFlag": false, "claimIsExternalInv": false, "isFraudClaim": false, "isSuspectDone": false, "mcE_PremiumCollected": 0}, "regionName": "TCS", "roOfficeId": 0, "doOfficeId": 0, "boOfficeId": 0, "missingDocuments": ["Please provide the detailed case summary with current status of the patient & further plan of management for further approval IV line mandatory ", "Please provide the detailed case summary with current status of the patient & further plan of management for further approval IV line mandatory "], "isSMSSent": 0, "isPostProcessed": 0, "lastSeenAuditTrailId": 0, "latestPAStatusId": 7, "latestPAInternalStatus": "Settled", "totalPayableAmount": "1,12,842", "totalNonPayableAmount": "32,466", "isPACheckDone": false, "dbType": "MA", "isMongoOnBoardReq": false, "documentDetails": [{"clmDocClmId": *********, "clmDocDocId": 1059, "clmDocId": 168946073, "clmDocIsAvailable": 1, "clmDocIsRequired": 1, "clmDocName": "Please provide the treating doctor letter stating whether the treatment / procedure is diagnostic or therapeutic", "clmDocRemarks": "Please provide the treating doctor letter stating whether the treatment / procedure is diagnostic or therapeutic ", "clmDocSpecialRemarks": "", "irType": "<PERSON><PERSON><PERSON><PERSON>"}, {"clmDocClmId": *********, "clmDocDocId": 1073, "clmDocId": 168967309, "clmDocIsAvailable": 0, "clmDocIsRequired": 1, "clmDocName": "Please provide the detailed case summary with current status of the patient & further plan of management for further approval ", "clmDocRemarks": "Please provide the detailed case summary with current status of the patient & further plan of management for further approval IV line mandatory ", "clmDocSpecialRemarks": "", "irType": "<PERSON><PERSON><PERSON><PERSON>"}, {"clmDocClmId": *********, "clmDocDocId": 1073, "clmDocId": 169015942, "clmDocIsAvailable": 0, "clmDocIsRequired": 1, "clmDocName": "Please provide the detailed case summary with current status of the patient & further plan of management for further approval ", "clmDocRemarks": "Please provide the detailed case summary with current status of the patient & further plan of management for further approval IV line mandatory ", "clmDocSpecialRemarks": "", "irType": "<PERSON><PERSON><PERSON><PERSON>"}, {"clmDocClmId": *********, "clmDocDocId": 1059, "clmDocId": 169015941, "clmDocIsAvailable": 1, "clmDocIsRequired": 1, "clmDocName": "Please provide the treating doctor letter stating whether the treatment / procedure is diagnostic or therapeutic", "clmDocRemarks": "Please provide the treating doctor letter stating whether the treatment / procedure is diagnostic or therapeutic ", "clmDocSpecialRemarks": "", "irType": "<PERSON><PERSON><PERSON><PERSON>"}], "secAilmentSmry": {"code": "N18.0", "group": "", "descs": "Chronic renal failure", "summary": ".Chronic Renal Failure", "ids": "75289", "ailmentBodySystem": "Urinary System"}, "terAilmentSmry": {}, "denialClauses": [], "allPaymentDtls": []}], "totalClaimCount": 0, "isSuccess": true, "claimIntimationDetails": []}