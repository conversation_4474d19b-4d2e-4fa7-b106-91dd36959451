[{"_debug_info": {"total_claims": 1, "filtering_rule": "Only logs with typeOfLog: \"Claim\" will be processed", "alteration_timestamp": "2025-06-13T19:55:26.312774"}}, {"claimNo": 131420765, "logs": [{"data": [], "logCreatedDate": "/Date(1749199259050)/", "typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": [{"timeOfOccurrence": "/Date(1749199258368+0530)/", "category": "BaseConfiguration", "subCategory": "DefautChecks", "subCategoryDisplayName": "Default Configuration", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Claim types considered for discount override: PreHosp,PostHosp,HospBenefit,HealthCheckUp,RI,ReOpenClaim", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Override Discount By <PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON> not executed as no matching rule found.", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Default Configuration - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 0.0, "postExectionTotalDeductionAmount": 0.0, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258369+0530)/", "category": "BaseConfiguration", "subCategory": "PolicyConfigIncaseOfDeath", "subCategoryDisplayName": "Exception On Death Case", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Exception On Death Case - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 0.0, "postExectionTotalDeductionAmount": 0.0, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258370+0530)/", "category": "BaseConfiguration", "subCategory": "OverideDiscount", "subCategoryDisplayName": "Override Discount By Claim Type", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Override Discount By <PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON> not executed as no matching rule found.", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Override Discount By Claim Type - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 0.0, "postExectionTotalDeductionAmount": 0.0, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258371+0530)/", "category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "*****Hospital Discount Calculation Started*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Matching Config Count: 3", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Matching Config Count: 3", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Standard - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "All Bills are pre-post bills so overiding the bill date to DOA", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "MMOU Discount Calculated: 8905.18", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "HMOU Discount Calculated: 9402.08", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "MAX Discount Calculated: 9402.08", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Discount Calculation Is Goving with HMOU", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Calculated Discount Amount: 0.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Calculated Discount Remarks: Pharmacy & Medicine Charges: 2760.22,Hospital Charges: 1827.11,Consultant Charges: 1299.90,Surgery Charges: 94.74,Investigation & Lab Charges: 2460.50,Miscellaneous Charges: 959.57", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "*****HospitalDiscount Calculation Ends*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "*****Calculate UCR Started*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "User Selected Bill UCR Bill Amount : 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "*****CalculateHospitalDiscount Calculation Ends*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Standard - Execution ends", "loggerType": "Information"}], "matchingConfig": [{"claimType": "None"}, {"claimType": "PreAuth"}, {"claimType": "Cashless"}], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 145307.6, "postExectionTotalDeductionAmount": 0.0, "configRuleLogObject": [{"displayName": "ClaimType", "value": "None", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "ClaimType", "value": "PreAuth", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "ClaimType", "value": "Cashless", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}]}, {"timeOfOccurrence": "/Date(1749199258379+0530)/", "category": "NonMedicalExpense", "subCategory": "ManualOverRideBills", "subCategoryDisplayName": "Manual OverRide Bills", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Manual OverRide Bills - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 145307.6, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258379+0530)/", "category": "NonMedicalExpense", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Standard (IRDAI) - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:IV Cannula, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Accu Check ( Glucometery/ Strips), Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Face Mask, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Band Aids, Bandages, Sterile Injections, Needles, Syringes, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Face Mask, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Band Aids, Bandages, Sterile Injections, Needles, Syringes, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Band Aids, Bandages, Sterile Injections, Needles, Syringes, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Miscellaneous Charges, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Miscellaneous Charges, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Nutrition Planning Charges - Dietician Charges- Diet Charges, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Miscellaneous Charges, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Accu Check ( Glucometery/ Strips), Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Miscellaneous Charges, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Accu Check ( Glucometery/ Strips), Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Accu Check ( Glucometery/ Strips), Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Band Aids, Bandages, Sterile Injections, Needles, Syringes, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Medical Records, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Band Aids, Bandages, Sterile Injections, Needles, Syringes, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Accu Check ( Glucometery/ Strips), Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Accu Check ( Glucometery/ Strips), Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Accu Check ( Glucometery/ Strips), Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Face Mask, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Face Mask, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Face Mask, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Bed Under Pad Charges, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Band Aids, Bandages, Sterile Injections, Needles, Syringes, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Accu Check ( Glucometery/ Strips), Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Face Mask, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Face Mask, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Bed Under Pad Charges, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:ECG Electrodes, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Flexi Mask, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Caps, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Band Aids, Bandages, Sterile Injections, Needles, Syringes, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Band Aids, Bandages, Sterile Injections, Needles, Syringes, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Accu Check ( Glucometery/ Strips), Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Standard (IRDAI) - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 145307.6, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258387+0530)/", "category": "NonMedicalExpense", "subCategory": "HandelNMEByPreAuthType", "subCategoryDisplayName": "Estimate For Initial", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "*****Calulating CalculateNMEDeduction Started*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Estimate For Initial - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258387+0530)/", "category": "NonMedicalExpense", "subCategory": "CapAmbulanceCharges", "subCategoryDisplayName": "Ambulance Limit By Amount", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "No deduction found in the historic claims", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Max Payable Ambulance Charge: 2500", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Current Payable Ambulance Charge if Any applicable: 2500", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Ambulance Limit By Amount - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258387+0530)/", "category": "NonMedicalExpense", "subCategory": "MarkAllNMEsAsPayable", "subCategoryDisplayName": "Pay All NMEs", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Rules not applied as no matching data found", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Pay All NMEs - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258390+0530)/", "category": "NonMedicalExpense", "subCategory": "RIPrePostNMEDeductions", "subCategoryDisplayName": "Reimbursement Pre-Post Deductions", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Reimbursement Pre-Post Deductions - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258390+0530)/", "category": "NonMedicalExpense", "subCategory": "VariantNotConfigured", "subCategoryDisplayName": "Condition To Be Reviewed", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Condition To Be Reviewed - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258390+0530)/", "category": "NonMedicalExpense", "subCategory": "OverrideNMEParticularsAmount", "subCategoryDisplayName": "NME limit by Subcharge", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Limit applicable subcharge head: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "NME limit by Subcharge - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "NME limit by Subcharge - Execution ends", "loggerType": "Information"}], "matchingConfig": [{"nmeConfigId": "0", "schemeId": "0", "policyId": "0", "payableType": "<PERSON>", "payableSubType": "Air Ambulance Charges", "isPayable": "True", "payableLimit": "100000", "quatityLimit": "-1", "quantityPriceLimit": "-1", "payablePercentageLimit": "0", "isLower": "False", "deductionLevel": "<PERSON><PERSON><PERSON>", "isActive": "True"}], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 119565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "NMEConfigId", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}, {"displayName": "SchemeId", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}, {"displayName": "PolicyId", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}, {"displayName": "PayableType", "value": "<PERSON>", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "PayableSubType", "value": "Air Ambulance Charges", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "IsPayable", "value": "Applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "PayableLimit", "value": "100000", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "QuatityLimit", "value": "-1", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "QuantityPriceLimit", "value": "-1", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "PayablePercentageLimit", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "DeductionLevel", "value": "<PERSON><PERSON><PERSON>", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "IsActive", "value": "Applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}]}, {"timeOfOccurrence": "/Date(1749199258392+0530)/", "category": "NonMedicalExpense", "subCategory": "ManualOverRideBills", "subCategoryDisplayName": "Manual OverRide Bills", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Manual OverRide Bills - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258393+0530)/", "category": "Repudiation", "subCategory": "ExclusionByGenderAilment", "subCategoryDisplayName": "Gender and ailment", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Gender and ailment - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258394+0530)/", "category": "Repudiation", "subCategory": "OnNumberOfChildren", "subCategoryDisplayName": "Number of living children allowed for maternity coverage", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Number of living children allowed for maternity coverage - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258395+0530)/", "category": "Repudiation", "subCategory": "ExclusionByAilment", "subCategoryDisplayName": "Repudiation By Ailment Category", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Repudiation By Ailment Category - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258428+0530)/", "category": "Repudiation", "subCategory": "RepudiationByRefractiveError", "subCategoryDisplayName": "Refractive Error", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Refractive Error - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258429+0530)/", "category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Waiting Period for Pre-existing Ailments - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Waiting Period for Pre-existing Ailments - Execution ends", "loggerType": "Information"}], "matchingConfig": [{"relation to Primary beneficiary": "Father, <PERSON><PERSON><PERSON>, Mother, <PERSON><PERSON><PERSON>", "grade": "any", "check PED Applicable For Beneficiary": "True", "skip For Demise": "False", "skip For Emergency Admission": "False", "number of days for waiting period": "365", "benefitScope": "Policy"}], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 119565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "Relation to Primary beneficiary", "value": "Father,<PERSON><PERSON><PERSON>,Mother,<PERSON><PERSON><PERSON>", "isFilterApplicable": true, "defaultValue": "ALL", "isToBeDisplayed": true, "claimValue": "<PERSON><PERSON><PERSON>"}, {"displayName": "Grade", "value": "any", "isFilterApplicable": true, "defaultValue": "ANY", "isToBeDisplayed": true, "claimValue": "Gold Plus"}, {"displayName": "Check PED Applicable For Beneficiary", "value": "Applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Skip For Demise", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Skip For Emergency Admission", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Number of days for waiting period", "value": "365", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "BenefitScope", "value": "Policy", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}]}, {"timeOfOccurrence": "/Date(1749199258431+0530)/", "category": "Repudiation", "subCategory": "DenyObesity", "subCategoryDisplayName": "Obesity", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Obesity - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258433+0530)/", "category": "Repudiation", "subCategory": "WaitingPeriodByAilmentsCategory", "subCategoryDisplayName": "Waiting Period by Ailment category", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722291+0530)/", "message": "Waiting Period by Ailment category - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258467+0530)/", "category": "Repudiation", "subCategory": "ExclusionByProcedures", "subCategoryDisplayName": "Repudiation By Procedure", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Repudiation By Procedure - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258467+0530)/", "category": "Repudiation", "subCategory": "WaitingPeriodByProcedures", "subCategoryDisplayName": "Waiting Period by Procedure", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Waiting Period by Procedure - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258468+0530)/", "category": "Repudiation", "subCategory": "RepudiationByHospital", "subCategoryDisplayName": "Repudiation By Hospital And Claim Type", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Repudiation By Hospital And Claim Type - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Repudiation By Hospital And Claim Type - Execution ends", "loggerType": "Information"}], "matchingConfig": [{"relation To Primary Beneficiary": "ALL", "grade": "gold, gold plus, gold plus_r, gold_r, silver, silver_r, platinum, platinum_r", "claim type": "Cashless, PreAuth", "skip For Network Hospital": "False", "skip For PPN Hospital": "False", "hospital": "287585", "skip for Demise": "False", "benefitScope": "Policy"}], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 119565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "Relation To Primary Beneficiary", "value": "ALL", "isFilterApplicable": true, "defaultValue": "ALL", "isToBeDisplayed": true, "claimValue": "<PERSON><PERSON><PERSON>"}, {"displayName": "Grade", "value": "gold,gold plus,gold plus_r,gold_r,silver,silver_r,platinum,platinum_r", "isFilterApplicable": true, "defaultValue": "ANY", "isToBeDisplayed": true, "claimValue": "Gold Plus"}, {"displayName": "Claim type", "value": "Cashless,PreAuth", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "<PERSON><PERSON> For Network Hospital", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Skip For PPN Hospital", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Hospital", "value": "287585", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Skip for Demise", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "BenefitScope", "value": "Policy", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}]}, {"timeOfOccurrence": "/Date(1749199258470+0530)/", "category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Matching Config Count: 2", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Matching Config Count: 2", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Standard (IRDAI) - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Applying Excess of Tariff ", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Calcualtion on Excess of Room Tariff Start", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Hospital room rent master details", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Room category:DeluxeRoom ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3400.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Room category:ICU ,Room Rent PerDay:11100.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Room category:SinglePrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Opted Room Category :SinglePrivateRoom", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Tariff Room Rent From Master:6500.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Tariff ICU Room Rent From Master:6500.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Calcualtion on Excess of Room Tariff Ends", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Calculation On Excess Of Package Starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Calculation On Excess Of Traiff Package", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Calculation On Excess Of Package Ends", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Standard (IRDAI) - Execution ends", "loggerType": "Information"}], "matchingConfig": [{"claimType": "PreAuth"}, {"claimType": "Cashless"}], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 119565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "ClaimType", "value": "PreAuth", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "ClaimType", "value": "Cashless", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}]}, {"timeOfOccurrence": "/Date(1749199258471+0530)/", "category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "*****Calulating Eligibile Room Rent Details Category Started*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Rule Suggested Eligible Room Category SemiPvtAC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Rule Suggested Eligible Room Facility NonAC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Opted Room Category SinglePrivateRoom", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "ICU Category: Intensive,ICU,HDU,Burnward", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "DELUXE Category: Suite,Delux,DeluxeRoom", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Checking Under Room Master", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Hospital room rent master details", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Room category:DeluxeRoom ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3400.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Room category:ICU ,Room Rent PerDay:11100.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Room category:SinglePrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Checking Eligible Room Category Under General Category", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Eligible Room Category not found Under General Category", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Checking Eligible Room Category Under SHARING Category", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Eligible Room Category Found Under Sharing Category", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Found room rent details under SHARING Category", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Checking Eligible Room Category Under ICU Category", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Eligible Room Category not found Under ICU Category", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Checking Eligible Room Category Under PRIVATE Category", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Eligible Room Category not found Under PRIVATE Category", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Checking Eligible Room Category Under DELUXE Category", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Eligible Room Category not found Under DELUXE Category", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Room Rent Fount Count:1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Checking room rent details for GIPSA", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "room rent details for GIPSA", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "After filtering data by AC/NON-AC and taking minimum price", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:0.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Calculating Room Price based on Insurer :- Starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Normal : Room Rent based on insurer nursing,room,room rent,room rent (per day),associated room charges,duty doctor,injection / iv administration charge,gst on room rent", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "ICU : Room Rent based on insurer icu charges,icu nursing charges,icu room rent,icu injection / iv administration charge,intencivist charges,icu monitoring charges", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Normal Room Rent Lenght of Stay : 4.00", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "ICU Room Rent Lenght of Stay : 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Non-Tariff bill as insurer Perday ChargeType :gst on room rent , Units : 4.00, PerDay Price : 604", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Non-Tariff bill as insurer Perday ChargeType :gst on room rent , Units : 4.00, PerDay Price : 604", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Tariff Bill Price as per insurer :5500.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Non-Tariff bill Price as per insurer :302", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Total Room Price as per insurer :5802.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Minimum room rent per day calculated for GIPSA: 5802.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "*****Calulating Eligibile Room Category End*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Room Rent limit based on amount: 5802.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Room Rent limit based on amount: -1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "*****Set Proportionate Calculation Started*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Calculation On Excess Of Package Starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Calculation On Excess Of Eligible Package", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Calculation On Excess Of Package Ends", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Proportionate CalCulation MaxEligibilityNormal: 5802.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Proportionate Calculation MaxEligibilityICU: -1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Proportionate CalCulation Availed ICU Length Of Stay: 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Proportionate CalCulation Availed Normal Length Of Stay: 4.00", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 6802.26", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Proportionate CalCulation Max Normal Room Rent Allowed: 23208.000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Proportionate CalCulation Proportionate Factor Normal: 85.30%", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 85.30%", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Proportionate CalCulation Proportionate Factor ICU: 100%", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "*****Proportionate Calculation End*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "*****Set Proportionate Calculation Ended*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Proportionate deduction limit: 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Proportionate deduction limit: 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Proportionate Calculation Total Admissible Amount Rs: 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "By Ward Type With Ailment Exclusion - Execution ends", "loggerType": "Information"}], "matchingConfig": [{"type of  Room": "Normal", "provider Type": "False", "propotionateApplicable": "True", "skip Proportionate On DeathCase": "False", "skip Proportionate On AccidentCase": "False", "topUp Configured": "False", "relation to Primary beneficiary": "Father, <PERSON><PERSON><PERSON>, Mother, <PERSON><PERSON><PERSON>", "grade": "gold plus, gold", "min Age": "0", "max Age": "0", "room Value Limit Defined By": "Amount", "room Value Type": "5802.0", "room Capped Limit": "0", "proportionate Limit Defined By": "Amount", "proportionate Limit Defined By Value": "0", "min SI": "0", "max SI": "0", "benefitScope": "Policy"}], "preExecutionApprovedAmount": 110163.96, "preExecutionTotalBillAmount": 119565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 110163.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "Type of  Room", "value": "Normal", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Provider Type", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "PropotionateApplicable", "value": "Applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Skip Proportionate On DeathCase", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Skip Proportionate On AccidentCase", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "TopUp Configured", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Relation to Primary beneficiary", "value": "Father,<PERSON><PERSON><PERSON>,Mother,<PERSON><PERSON><PERSON>", "isFilterApplicable": true, "defaultValue": "ALL", "isToBeDisplayed": true, "claimValue": "<PERSON><PERSON><PERSON>"}, {"displayName": "Grade", "value": "gold plus,gold", "isFilterApplicable": true, "defaultValue": "ANY", "isToBeDisplayed": true, "claimValue": "Gold Plus"}, {"displayName": "Min Age", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "61"}, {"displayName": "Max Age", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "61"}, {"displayName": "Room Value Limit Defined By", "value": "Amount", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Room Value Type", "value": "5802.0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Room Capped Limit", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}, {"displayName": "Proportionate Limit Defined By", "value": "Amount", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Proportionate Limit Defined By Value", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}, {"displayName": "Min SI", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "1600000.0"}, {"displayName": "Max SI", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "1600000.0"}, {"displayName": "BenefitScope", "value": "Policy", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}]}, {"timeOfOccurrence": "/Date(1749199258486+0530)/", "category": "PropotinateDeduction", "subCategory": "VariantNotConfigured", "subCategoryDisplayName": "Condition To Be Reviewed", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Condition To Be Reviewed - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 103439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258487+0530)/", "category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Intimation - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Deductions not applied as it is an emergency claim", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Intimation - Execution ends", "loggerType": "Information"}], "matchingConfig": [{"relation To Primary Beneficiary": "ALL", "grade": "gold, gold plus, platinum, silver", "claim type": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>", "intimation Days": "3", "copay Defined By": "PecentageOfAdmissibleAmount", "copay Defined Value(Percentage/Amount)": "10", "upper/Lower Copay Limit For Comparison": "0", "skip For Emergency Admission": "True", "is Min/Max Copay Applicable Per Person": "False", "is Copay Applicable Per Admission Basis": "False", "copay Limit Applicable Per Family": "False", "dedution Remarks": "INTIMATION_COPAY", "benefitScope": "Policy"}], "preExecutionApprovedAmount": 103439.96, "preExecutionTotalBillAmount": 112842.58, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 103439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "Relation To Primary Beneficiary", "value": "ALL", "isFilterApplicable": true, "defaultValue": "ALL", "isToBeDisplayed": true, "claimValue": "<PERSON><PERSON><PERSON>"}, {"displayName": "Grade", "value": "gold,gold plus,platinum,silver", "isFilterApplicable": true, "defaultValue": "ANY", "isToBeDisplayed": true, "claimValue": "Gold Plus"}, {"displayName": "Claim type", "value": "<PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>", "isFilterApplicable": true, "defaultValue": "None", "isToBeDisplayed": true, "claimValue": "PreAuth"}, {"displayName": "Intimation Days", "value": "3", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Copay Defined By", "value": "PecentageOfAdmissibleAmount", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Copay Defined Value(Percentage/Amount)", "value": "10", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Upper/Lower Copay Limit For Comparison", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}, {"displayName": "Skip For Emergency Admission", "value": "Applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Is Min/Max Copay Applicable Per Person", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Is Copay Applicable Per Admission Basis", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Copay Limit Applicable Per Family", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Dedution Remarks", "value": "INTIMATION_COPAY", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "BenefitScope", "value": "Policy", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}]}, {"timeOfOccurrence": "/Date(1749199258490+0530)/", "category": "Copay", "subCategory": "CopayOnNonNetwork", "subCategoryDisplayName": "Non-Network Hospital", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Non-Network Hospital - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 103439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258494+0530)/", "category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "No copay deduction found in the historic claims", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Minimum capped value for copay applicable between 10%  of admissible amount 103439.96 and flat amount 10000 is 10000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Copay applicable based on admission basis", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Copay value: 10000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Copay deduction amount is 10000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Copay deduction amount is: 10000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 10%  of admissible amount 103439.96 and flat amount 10000 is 10000 (Mandatory Copay)", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722292+0530)/", "message": "Grade and Relationship - Execution ends", "loggerType": "Information"}], "matchingConfig": [{"relationship": "ALL", "grade": "gold plus, gold plus_r", "minimum Age": "0", "maximum Age": "0", "min SI": "0", "max SI": "0", "min Claim amount": "0", "max Claim amount": "0", "min Admissible amount": "0", "max Admissible amount": "0", "exculding Procedures": "3119, 3163, 3165, 3467, 3861, 4121, 4430, 4436, 4513, 4560, 4591, 4599, 4671", "hospital Network/Non-Network": "False", "copay Type": "MinOfPercentageOfAdmissibleAndCappedAmount", "copay Percentage": "10", "capped Value": "10000", "skip For Emergency Admission": "False", "applicable on claim event basis": "True", "is Family Floater": "False", "applicable on claim Member basis": "False", "no. of Days": "0", "check PED Applicable For Beneficiary": "False", "isCopayDueToAnyoneIllness": "False", "exculdingAilmentsCategoryOrAilment": "IVF_FOR_INFERTILITY,INFERTILITY,PSYCHIATRIC,GENETICDISORDER,M<PERSON><PERSON><PERSON>LE_SCLEROSIS,MATERNITY_COMPLICATIONS,MATERNITY_MULTIPLE_DELIVERY,MATER<PERSON><PERSON>_NORMAL,MATE<PERSON><PERSON>TY_LSCS,TWIN_DELIVERY,TRIPLET_DELIVERY,MORE_THAN_TRIPLET_DELIVERY,PRE_AND_POST_NATAL,Mental_disorder", "dedution Remarks": "MANDATORY_COPAY", "sum Insured Utilization": "0", "benefitScope": "Policy"}], "preExecutionApprovedAmount": 103439.96, "preExecutionTotalBillAmount": 112842.58, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 103439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "Relationship", "value": "ALL", "isFilterApplicable": true, "defaultValue": "ALL", "isToBeDisplayed": true, "claimValue": "<PERSON><PERSON><PERSON>"}, {"displayName": "Grade", "value": "gold plus,gold plus_r", "isFilterApplicable": true, "defaultValue": "ANY", "isToBeDisplayed": true, "claimValue": "Gold Plus"}, {"displayName": "Minimum Age", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "61"}, {"displayName": "Maximum Age", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "61"}, {"displayName": "Min SI", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "1600000.0"}, {"displayName": "Max SI", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "1600000.0"}, {"displayName": "Min Claim amount", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "145307.0"}, {"displayName": "Max Claim amount", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "145307.0"}, {"displayName": "Min Admissible amount", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "112842"}, {"displayName": "Max Admissible amount", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "112842"}, {"displayName": "Exculding Procedures", "value": "3119,3163,3165,3467,3861,4121,4430,4436,4513,4560,4591,4599,4671", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": true, "claimValue": "4564"}, {"displayName": "Hospital Network/Non-Network", "value": "Not applicable", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": true, "claimValue": "True"}, {"displayName": "Copay Type", "value": "MinOfPercentageOfAdmissibleAndCappedAmount", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Copay Per<PERSON>age", "value": "10", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Capped Value", "value": "10000", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Skip For Emergency Admission", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Applicable on claim event basis", "value": "Applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Is Family Floater", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Applicable on claim Member basis", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "No. of Days", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}, {"displayName": "Check PED Applicable For Beneficiary", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "isCopayDueToAnyoneIllness", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "ExculdingAilmentsCategoryOrAilment", "value": "IVF_FOR_INFERTILITY,INFERTILITY,PSYCHIATRIC,GENETICDISORDER,M<PERSON><PERSON><PERSON>LE_SCLEROSIS,MATERNITY_COMPLICATIONS,MATERNITY_MULTIPLE_DELIVERY,MATER<PERSON><PERSON>_NORMAL,MATE<PERSON><PERSON>TY_LSCS,TWIN_DELIVERY,TRIPLET_DELIVERY,MORE_THAN_TRIPLET_DELIVERY,PRE_AND_POST_NATAL,Mental_disorder", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": true, "claimValue": "INFECTIOUS_BACTERIAL_VIRAL_MEDICAL_MGMT"}, {"displayName": "Dedution Remarks", "value": "MANDATORY_COPAY", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Sum Insured Utilization", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}, {"displayName": "BenefitScope", "value": "Policy", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}]}, {"timeOfOccurrence": "/Date(1749199258572+0530)/", "category": "Copay", "subCategory": "RelationGradeAdmissibleAmount", "subCategoryDisplayName": "Copay By Admissible Amount", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Copay By Admissible Amount - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258578+0530)/", "category": "Copay", "subCategory": "VariantNotConfigured", "subCategoryDisplayName": "Condition To Be Reviewed", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Condition To Be Reviewed - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258578+0530)/", "category": "Copay", "subCategory": "RelationGradeAilment", "subCategoryDisplayName": "Ailment Category", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Ailment Category - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258580+0530)/", "category": "AilmentCapping", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Standard (IRDAI) - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Standard (IRDAI) - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 93439.96, "preExecutionTotalBillAmount": 112842.58, "preExectionTotalDeductionAmount": 19402.04, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258580+0530)/", "category": "AilmentCapping", "subCategory": "AilmentCappingByGrade", "subCategoryDisplayName": "Ailment Category", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Ailment Category - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258763+0530)/", "category": "AilmentCapping", "subCategory": "ProcedureCappingByApproachAndPostion", "subCategoryDisplayName": "Position and Approach", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Position and Approach - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258781+0530)/", "category": "AilmentCapping", "subCategory": "AilmentProcedureCappingBySIType", "subCategoryDisplayName": "Ailment Category and Procedure Combination", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Ailment Category and Procedure Combination - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258929+0530)/", "category": "AilmentCapping", "subCategory": "ProcedureCappingByGrade", "subCategoryDisplayName": "Procedure", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Procedure - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199258952+0530)/", "category": "AilmentCapping", "subCategory": "AilmentCappingByClaimType", "subCategoryDisplayName": "Ailment Capping by <PERSON><PERSON><PERSON>", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Ailment Capping by <PERSON><PERSON>m Type - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199259006+0530)/", "category": "AilmentCapping", "subCategory": "MaternityWellnessBabyCapping", "subCategoryDisplayName": "Maternity, wellness baby care & Pre-Post natal", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Maternity, wellness baby care & Pre-Post natal - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199259047+0530)/", "category": "AilmentCapping", "subCategory": "VariantNotConfigured", "subCategoryDisplayName": "Condition To Be Reviewed", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Condition To Be Reviewed - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199259047+0530)/", "category": "SIRestriction", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Standard - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199259047+0530)/", "category": "SIRestriction", "subCategory": "OtherHealthCareSIRestriction", "subCategoryDisplayName": "SI Restriction By Health Care Types", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "SI Restriction By Health Care Types - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199259050+0530)/", "category": "PolicyBenefits", "subCategory": "NotApplicable", "subCategoryDisplayName": "Not Applicable", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Not Applicable - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199259050+0530)/", "category": "UCRDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "*****Calculation On SOC Started*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "NO SOC Details Found", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "*****Calculation On SOC Ends*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Standard - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}], "_will_be_processed": true}, {"data": [], "logCreatedDate": "/Date(1749199182955)/", "typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": [{"timeOfOccurrence": "/Date(1749199182265+0530)/", "category": "BaseConfiguration", "subCategory": "DefautChecks", "subCategoryDisplayName": "Default Configuration", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Claim types considered for discount override: PreHosp,PostHosp,HospBenefit,HealthCheckUp,RI,ReOpenClaim", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Override Discount By <PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON> not executed as no matching rule found.", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Default Configuration - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 0.0, "postExectionTotalDeductionAmount": 0.0, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182266+0530)/", "category": "BaseConfiguration", "subCategory": "PolicyConfigIncaseOfDeath", "subCategoryDisplayName": "Exception On Death Case", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Exception On Death Case - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 0.0, "postExectionTotalDeductionAmount": 0.0, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182267+0530)/", "category": "BaseConfiguration", "subCategory": "OverideDiscount", "subCategoryDisplayName": "Override Discount By Claim Type", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Override Discount By <PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON> not executed as no matching rule found.", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Override Discount By Claim Type - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 0.0, "postExectionTotalDeductionAmount": 0.0, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182267+0530)/", "category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "*****Hospital Discount Calculation Started*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Matching Config Count: 3", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Matching Config Count: 3", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Standard - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "All Bills are pre-post bills so overiding the bill date to DOA", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "MMOU Discount Calculated: 8905.18", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "HMOU Discount Calculated: 9402.08", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "MAX Discount Calculated: 9402.08", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Discount Calculation Is Goving with HMOU", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Calculated Discount Amount: 0.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Calculated Discount Remarks: Pharmacy & Medicine Charges: 2760.22,Hospital Charges: 1827.11,Consultant Charges: 1299.90,Surgery Charges: 94.74,Investigation & Lab Charges: 2460.50,Miscellaneous Charges: 959.57", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "*****HospitalDiscount Calculation Ends*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "*****Calculate UCR Started*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "User Selected Bill UCR Bill Amount : 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "*****CalculateHospitalDiscount Calculation Ends*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Standard - Execution ends", "loggerType": "Information"}], "matchingConfig": [{"claimType": "None"}, {"claimType": "PreAuth"}, {"claimType": "Cashless"}], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 145307.6, "postExectionTotalDeductionAmount": 0.0, "configRuleLogObject": [{"displayName": "ClaimType", "value": "None", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "ClaimType", "value": "PreAuth", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "ClaimType", "value": "Cashless", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}]}, {"timeOfOccurrence": "/Date(1749199182276+0530)/", "category": "NonMedicalExpense", "subCategory": "ManualOverRideBills", "subCategoryDisplayName": "Manual OverRide Bills", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Manual OverRide Bills - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 145307.6, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182276+0530)/", "category": "NonMedicalExpense", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Standard (IRDAI) - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:IV Cannula, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Accu Check ( Glucometery/ Strips), Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Face Mask, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Band Aids, Bandages, Sterile Injections, Needles, Syringes, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Face Mask, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Band Aids, Bandages, Sterile Injections, Needles, Syringes, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Band Aids, Bandages, Sterile Injections, Needles, Syringes, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Miscellaneous Charges, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Miscellaneous Charges, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Nutrition Planning Charges - Dietician Charges- Diet Charges, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Miscellaneous Charges, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Accu Check ( Glucometery/ Strips), Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Miscellaneous Charges, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Accu Check ( Glucometery/ Strips), Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Accu Check ( Glucometery/ Strips), Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Band Aids, Bandages, Sterile Injections, Needles, Syringes, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Medical Records, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Band Aids, Bandages, Sterile Injections, Needles, Syringes, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Accu Check ( Glucometery/ Strips), Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Accu Check ( Glucometery/ Strips), Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Accu Check ( Glucometery/ Strips), Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Face Mask, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Face Mask, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Face Mask, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Bed Under Pad Charges, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Band Aids, Bandages, Sterile Injections, Needles, Syringes, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Accu Check ( Glucometery/ Strips), Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Face Mask, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Face Mask, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Bed Under Pad Charges, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:ECG Electrodes, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Flexi Mask, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722293+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Caps, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Band Aids, Bandages, Sterile Injections, Needles, Syringes, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Band Aids, Bandages, Sterile Injections, Needles, Syringes, Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Accu Check ( Glucometery/ Strips), Marking Bill Item Non Payable", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Standard (IRDAI) - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 145307.6, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182284+0530)/", "category": "NonMedicalExpense", "subCategory": "HandelNMEByPreAuthType", "subCategoryDisplayName": "Estimate For Initial", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "*****Calulating CalculateNMEDeduction Started*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Estimate For Initial - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182284+0530)/", "category": "NonMedicalExpense", "subCategory": "CapAmbulanceCharges", "subCategoryDisplayName": "Ambulance Limit By Amount", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "No deduction found in the historic claims", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Max Payable Ambulance Charge: 2500", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Current Payable Ambulance Charge if Any applicable: 2500", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Ambulance Limit By Amount - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182284+0530)/", "category": "NonMedicalExpense", "subCategory": "MarkAllNMEsAsPayable", "subCategoryDisplayName": "Pay All NMEs", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Rules not applied as no matching data found", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Pay All NMEs - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182286+0530)/", "category": "NonMedicalExpense", "subCategory": "RIPrePostNMEDeductions", "subCategoryDisplayName": "Reimbursement Pre-Post Deductions", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Reimbursement Pre-Post Deductions - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182286+0530)/", "category": "NonMedicalExpense", "subCategory": "VariantNotConfigured", "subCategoryDisplayName": "Condition To Be Reviewed", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Condition To Be Reviewed - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182286+0530)/", "category": "NonMedicalExpense", "subCategory": "OverrideNMEParticularsAmount", "subCategoryDisplayName": "NME limit by Subcharge", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Limit applicable subcharge head: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "NME limit by Subcharge - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "NME limit by Subcharge - Execution ends", "loggerType": "Information"}], "matchingConfig": [{"nmeConfigId": "0", "schemeId": "0", "policyId": "0", "payableType": "<PERSON>", "payableSubType": "Air Ambulance Charges", "isPayable": "True", "payableLimit": "100000", "quatityLimit": "-1", "quantityPriceLimit": "-1", "payablePercentageLimit": "0", "isLower": "False", "deductionLevel": "<PERSON><PERSON><PERSON>", "isActive": "True"}], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 119565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "NMEConfigId", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}, {"displayName": "SchemeId", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}, {"displayName": "PolicyId", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}, {"displayName": "PayableType", "value": "<PERSON>", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "PayableSubType", "value": "Air Ambulance Charges", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "IsPayable", "value": "Applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "PayableLimit", "value": "100000", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "QuatityLimit", "value": "-1", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "QuantityPriceLimit", "value": "-1", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "PayablePercentageLimit", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "DeductionLevel", "value": "<PERSON><PERSON><PERSON>", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "IsActive", "value": "Applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}]}, {"timeOfOccurrence": "/Date(1749199182289+0530)/", "category": "NonMedicalExpense", "subCategory": "ManualOverRideBills", "subCategoryDisplayName": "Manual OverRide Bills", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Manual OverRide Bills - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182289+0530)/", "category": "Repudiation", "subCategory": "ExclusionByGenderAilment", "subCategoryDisplayName": "Gender and ailment", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Gender and ailment - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182291+0530)/", "category": "Repudiation", "subCategory": "OnNumberOfChildren", "subCategoryDisplayName": "Number of living children allowed for maternity coverage", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Number of living children allowed for maternity coverage - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182292+0530)/", "category": "Repudiation", "subCategory": "ExclusionByAilment", "subCategoryDisplayName": "Repudiation By Ailment Category", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Repudiation By Ailment Category - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182324+0530)/", "category": "Repudiation", "subCategory": "RepudiationByRefractiveError", "subCategoryDisplayName": "Refractive Error", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Refractive Error - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182325+0530)/", "category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Waiting Period for Pre-existing Ailments - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Waiting Period for Pre-existing Ailments - Execution ends", "loggerType": "Information"}], "matchingConfig": [{"relation to Primary beneficiary": "Father, <PERSON><PERSON><PERSON>, Mother, <PERSON><PERSON><PERSON>", "grade": "any", "check PED Applicable For Beneficiary": "True", "skip For Demise": "False", "skip For Emergency Admission": "False", "number of days for waiting period": "365", "benefitScope": "Policy"}], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 119565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "Relation to Primary beneficiary", "value": "Father,<PERSON><PERSON><PERSON>,Mother,<PERSON><PERSON><PERSON>", "isFilterApplicable": true, "defaultValue": "ALL", "isToBeDisplayed": true, "claimValue": "<PERSON><PERSON><PERSON>"}, {"displayName": "Grade", "value": "any", "isFilterApplicable": true, "defaultValue": "ANY", "isToBeDisplayed": true, "claimValue": "Gold Plus"}, {"displayName": "Check PED Applicable For Beneficiary", "value": "Applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Skip For Demise", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Skip For Emergency Admission", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Number of days for waiting period", "value": "365", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "BenefitScope", "value": "Policy", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}]}, {"timeOfOccurrence": "/Date(1749199182327+0530)/", "category": "Repudiation", "subCategory": "DenyObesity", "subCategoryDisplayName": "Obesity", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Obesity - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182328+0530)/", "category": "Repudiation", "subCategory": "WaitingPeriodByAilmentsCategory", "subCategoryDisplayName": "Waiting Period by Ailment category", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Waiting Period by Ailment category - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182363+0530)/", "category": "Repudiation", "subCategory": "ExclusionByProcedures", "subCategoryDisplayName": "Repudiation By Procedure", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Repudiation By Procedure - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182363+0530)/", "category": "Repudiation", "subCategory": "WaitingPeriodByProcedures", "subCategoryDisplayName": "Waiting Period by Procedure", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Waiting Period by Procedure - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182363+0530)/", "category": "Repudiation", "subCategory": "RepudiationByHospital", "subCategoryDisplayName": "Repudiation By Hospital And Claim Type", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Repudiation By Hospital And Claim Type - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Repudiation By Hospital And Claim Type - Execution ends", "loggerType": "Information"}], "matchingConfig": [{"relation To Primary Beneficiary": "ALL", "grade": "gold, gold plus, gold plus_r, gold_r, silver, silver_r, platinum, platinum_r", "claim type": "Cashless, PreAuth", "skip For Network Hospital": "False", "skip For PPN Hospital": "False", "hospital": "287585", "skip for Demise": "False", "benefitScope": "Policy"}], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 119565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "Relation To Primary Beneficiary", "value": "ALL", "isFilterApplicable": true, "defaultValue": "ALL", "isToBeDisplayed": true, "claimValue": "<PERSON><PERSON><PERSON>"}, {"displayName": "Grade", "value": "gold,gold plus,gold plus_r,gold_r,silver,silver_r,platinum,platinum_r", "isFilterApplicable": true, "defaultValue": "ANY", "isToBeDisplayed": true, "claimValue": "Gold Plus"}, {"displayName": "Claim type", "value": "Cashless,PreAuth", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "<PERSON><PERSON> For Network Hospital", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Skip For PPN Hospital", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Hospital", "value": "287585", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Skip for Demise", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "BenefitScope", "value": "Policy", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}]}, {"timeOfOccurrence": "/Date(1749199182365+0530)/", "category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Matching Config Count: 2", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Matching Config Count: 2", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Standard (IRDAI) - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Applying Excess of Tariff ", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Calcualtion on Excess of Room Tariff Start", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Hospital room rent master details", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Room category:DeluxeRoom ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3400.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Room category:ICU ,Room Rent PerDay:11100.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Room category:SinglePrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Opted Room Category :SemiPrivateRoom", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Tariff Room Rent From Master:5500.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Tariff ICU Room Rent From Master:5500.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 4000.000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Calcualtion on Excess of Room Tariff Ends", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Calculation On Excess Of Package Starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Calculation On Excess Of Traiff Package", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Calculation On Excess Of Package Ends", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Standard (IRDAI) - Execution ends", "loggerType": "Information"}], "matchingConfig": [{"claimType": "PreAuth"}, {"claimType": "Cashless"}], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 119565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "ClaimType", "value": "PreAuth", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "ClaimType", "value": "Cashless", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}]}, {"timeOfOccurrence": "/Date(1749199182367+0530)/", "category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "*****Calulating Eligibile Room Rent Details Category Started*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Rule Suggested Eligible Room Category SemiPvtAC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Rule Suggested Eligible Room Facility NonAC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Opted Room Category SemiPrivateRoom", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "ICU Category: Intensive,ICU,HDU,Burnward", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "DELUXE Category: Suite,Delux,DeluxeRoom", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Checking Under Room Master", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Hospital room rent master details", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Room category:DeluxeRoom ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3400.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Room category:ICU ,Room Rent PerDay:11100.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Room category:SinglePrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Checking Eligible Room Category Under General Category", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Eligible Room Category not found Under General Category", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Checking Eligible Room Category Under SHARING Category", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Eligible Room Category Found Under Sharing Category", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Found room rent details under SHARING Category", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Checking Eligible Room Category Under ICU Category", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Eligible Room Category not found Under ICU Category", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Checking Eligible Room Category Under PRIVATE Category", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Eligible Room Category not found Under PRIVATE Category", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Checking Eligible Room Category Under DELUXE Category", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Eligible Room Category not found Under DELUXE Category", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Room Rent Fount Count:1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Checking room rent details for GIPSA", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "room rent details for GIPSA", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "After filtering data by AC/NON-AC and taking minimum price", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:0.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Calculating Room Price based on Insurer :- Starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Normal : Room Rent based on insurer nursing,room,room rent,room rent (per day),associated room charges,duty doctor,injection / iv administration charge,gst on room rent", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "ICU : Room Rent based on insurer icu charges,icu nursing charges,icu room rent,icu injection / iv administration charge,intencivist charges,icu monitoring charges", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Normal Room Rent Lenght of Stay : 4.00", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "ICU Room Rent Lenght of Stay : 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Non-Tariff bill as insurer Perday ChargeType :gst on room rent , Units : 4.00, PerDay Price : 604", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Non-Tariff bill as insurer Perday ChargeType :gst on room rent , Units : 4.00, PerDay Price : 604", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Tariff Bill Price as per insurer :5500.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Non-Tariff bill Price as per insurer :302", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Total Room Price as per insurer :5802.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Minimum room rent per day calculated for GIPSA: 5802.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "*****Calulating Eligibile Room Category End*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Room Rent limit based on amount: 5802.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Room Rent limit based on amount: -1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "*****Set Proportionate Calculation Started*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Calculation On Excess Of Package Starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Calculation On Excess Of Eligible Package", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Calculation On Excess Of Package Ends", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Proportionate CalCulation MaxEligibilityNormal: 5802.0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Proportionate Calculation MaxEligibilityICU: -1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Proportionate CalCulation Availed ICU Length Of Stay: 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Proportionate CalCulation Availed Normal Length Of Stay: 4.00", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 5802.26", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Proportionate CalCulation Max Normal Room Rent Allowed: 23208.000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Proportionate CalCulation Proportionate Factor Normal: 100.00%", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100.00%", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Proportionate CalCulation Proportionate Factor ICU: 100%", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "*****Proportionate Calculation End*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "*****Set Proportionate Calculation Ended*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Proportionate deduction limit: 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Proportionate deduction limit: 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Proportionate Calculation Total Admissible Amount Rs: 0", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "By Ward Type With Ailment Exclusion - Execution ends", "loggerType": "Information"}], "matchingConfig": [{"type of  Room": "Normal", "provider Type": "False", "propotionateApplicable": "True", "skip Proportionate On DeathCase": "False", "skip Proportionate On AccidentCase": "False", "topUp Configured": "False", "relation to Primary beneficiary": "Father, <PERSON><PERSON><PERSON>, Mother, <PERSON><PERSON><PERSON>", "grade": "gold plus, gold", "min Age": "0", "max Age": "0", "room Value Limit Defined By": "Amount", "room Value Type": "5802.0", "room Capped Limit": "0", "proportionate Limit Defined By": "Amount", "proportionate Limit Defined By Value": "0", "min SI": "0", "max SI": "0", "benefitScope": "Policy"}], "preExecutionApprovedAmount": 106163.96, "preExecutionTotalBillAmount": 115565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 106163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "Type of  Room", "value": "Normal", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Provider Type", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "PropotionateApplicable", "value": "Applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Skip Proportionate On DeathCase", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Skip Proportionate On AccidentCase", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "TopUp Configured", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Relation to Primary beneficiary", "value": "Father,<PERSON><PERSON><PERSON>,Mother,<PERSON><PERSON><PERSON>", "isFilterApplicable": true, "defaultValue": "ALL", "isToBeDisplayed": true, "claimValue": "<PERSON><PERSON><PERSON>"}, {"displayName": "Grade", "value": "gold plus,gold", "isFilterApplicable": true, "defaultValue": "ANY", "isToBeDisplayed": true, "claimValue": "Gold Plus"}, {"displayName": "Min Age", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "61"}, {"displayName": "Max Age", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "61"}, {"displayName": "Room Value Limit Defined By", "value": "Amount", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Room Value Type", "value": "5802.0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Room Capped Limit", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}, {"displayName": "Proportionate Limit Defined By", "value": "Amount", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Proportionate Limit Defined By Value", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}, {"displayName": "Min SI", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "1600000.0"}, {"displayName": "Max SI", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "1600000.0"}, {"displayName": "BenefitScope", "value": "Policy", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}]}, {"timeOfOccurrence": "/Date(1749199182382+0530)/", "category": "PropotinateDeduction", "subCategory": "VariantNotConfigured", "subCategoryDisplayName": "Condition To Be Reviewed", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Condition To Be Reviewed - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 106163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182382+0530)/", "category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Intimation - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Deductions not applied as it is an emergency claim", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Intimation - Execution ends", "loggerType": "Information"}], "matchingConfig": [{"relation To Primary Beneficiary": "ALL", "grade": "gold, gold plus, platinum, silver", "claim type": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>", "intimation Days": "3", "copay Defined By": "PecentageOfAdmissibleAmount", "copay Defined Value(Percentage/Amount)": "10", "upper/Lower Copay Limit For Comparison": "0", "skip For Emergency Admission": "True", "is Min/Max Copay Applicable Per Person": "False", "is Copay Applicable Per Admission Basis": "False", "copay Limit Applicable Per Family": "False", "dedution Remarks": "INTIMATION_COPAY", "benefitScope": "Policy"}], "preExecutionApprovedAmount": 106163.96, "preExecutionTotalBillAmount": 115565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 106163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "Relation To Primary Beneficiary", "value": "ALL", "isFilterApplicable": true, "defaultValue": "ALL", "isToBeDisplayed": true, "claimValue": "<PERSON><PERSON><PERSON>"}, {"displayName": "Grade", "value": "gold,gold plus,platinum,silver", "isFilterApplicable": true, "defaultValue": "ANY", "isToBeDisplayed": true, "claimValue": "Gold Plus"}, {"displayName": "Claim type", "value": "<PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>", "isFilterApplicable": true, "defaultValue": "None", "isToBeDisplayed": true, "claimValue": "PreAuth"}, {"displayName": "Intimation Days", "value": "3", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Copay Defined By", "value": "PecentageOfAdmissibleAmount", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Copay Defined Value(Percentage/Amount)", "value": "10", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Upper/Lower Copay Limit For Comparison", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}, {"displayName": "Skip For Emergency Admission", "value": "Applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Is Min/Max Copay Applicable Per Person", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Is Copay Applicable Per Admission Basis", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Copay Limit Applicable Per Family", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Dedution Remarks", "value": "INTIMATION_COPAY", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "BenefitScope", "value": "Policy", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}]}, {"timeOfOccurrence": "/Date(1749199182386+0530)/", "category": "Copay", "subCategory": "CopayOnNonNetwork", "subCategoryDisplayName": "Non-Network Hospital", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Non-Network Hospital - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 106163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182390+0530)/", "category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Matching Config Count: 1", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "No copay deduction found in the historic claims", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Minimum capped value for copay applicable between 10%  of admissible amount 106163.96 and flat amount 10000 is 10000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Copay applicable based on admission basis", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Copay value: 10000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Copay deduction amount is 10000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Copay deduction amount is: 10000", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 10%  of admissible amount 106163.96 and flat amount 10000 is 10000 (Mandatory Copay)", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Grade and Relationship - Execution ends", "loggerType": "Information"}], "matchingConfig": [{"relationship": "ALL", "grade": "gold plus, gold plus_r", "minimum Age": "0", "maximum Age": "0", "min SI": "0", "max SI": "0", "min Claim amount": "0", "max Claim amount": "0", "min Admissible amount": "0", "max Admissible amount": "0", "exculding Procedures": "3119, 3163, 3165, 3467, 3861, 4121, 4430, 4436, 4513, 4560, 4591, 4599, 4671", "hospital Network/Non-Network": "False", "copay Type": "MinOfPercentageOfAdmissibleAndCappedAmount", "copay Percentage": "10", "capped Value": "10000", "skip For Emergency Admission": "False", "applicable on claim event basis": "True", "is Family Floater": "False", "applicable on claim Member basis": "False", "no. of Days": "0", "check PED Applicable For Beneficiary": "False", "isCopayDueToAnyoneIllness": "False", "exculdingAilmentsCategoryOrAilment": "IVF_FOR_INFERTILITY,INFERTILITY,PSYCHIATRIC,GENETICDISORDER,M<PERSON><PERSON><PERSON>LE_SCLEROSIS,MATERNITY_COMPLICATIONS,MATERNITY_MULTIPLE_DELIVERY,MATER<PERSON><PERSON>_NORMAL,MATE<PERSON><PERSON>TY_LSCS,TWIN_DELIVERY,TRIPLET_DELIVERY,MORE_THAN_TRIPLET_DELIVERY,PRE_AND_POST_NATAL,Mental_disorder", "dedution Remarks": "MANDATORY_COPAY", "sum Insured Utilization": "0", "benefitScope": "Policy"}], "preExecutionApprovedAmount": 106163.96, "preExecutionTotalBillAmount": 115565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 106163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "Relationship", "value": "ALL", "isFilterApplicable": true, "defaultValue": "ALL", "isToBeDisplayed": true, "claimValue": "<PERSON><PERSON><PERSON>"}, {"displayName": "Grade", "value": "gold plus,gold plus_r", "isFilterApplicable": true, "defaultValue": "ANY", "isToBeDisplayed": true, "claimValue": "Gold Plus"}, {"displayName": "Minimum Age", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "61"}, {"displayName": "Maximum Age", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "61"}, {"displayName": "Min SI", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "1600000.0"}, {"displayName": "Max SI", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "1600000.0"}, {"displayName": "Min Claim amount", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "145307.0"}, {"displayName": "Max Claim amount", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "145307.0"}, {"displayName": "Min Admissible amount", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "115566"}, {"displayName": "Max Admissible amount", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "115566"}, {"displayName": "Exculding Procedures", "value": "3119,3163,3165,3467,3861,4121,4430,4436,4513,4560,4591,4599,4671", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": true, "claimValue": "4564"}, {"displayName": "Hospital Network/Non-Network", "value": "Not applicable", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": true, "claimValue": "True"}, {"displayName": "Copay Type", "value": "MinOfPercentageOfAdmissibleAndCappedAmount", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Copay Per<PERSON>age", "value": "10", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Capped Value", "value": "10000", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Skip For Emergency Admission", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Applicable on claim event basis", "value": "Applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Is Family Floater", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Applicable on claim Member basis", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "No. of Days", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}, {"displayName": "Check PED Applicable For Beneficiary", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "isCopayDueToAnyoneIllness", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "ExculdingAilmentsCategoryOrAilment", "value": "IVF_FOR_INFERTILITY,INFERTILITY,PSYCHIATRIC,GENETICDISORDER,M<PERSON><PERSON><PERSON>LE_SCLEROSIS,MATERNITY_COMPLICATIONS,MATERNITY_MULTIPLE_DELIVERY,MATER<PERSON><PERSON>_NORMAL,MATE<PERSON><PERSON>TY_LSCS,TWIN_DELIVERY,TRIPLET_DELIVERY,MORE_THAN_TRIPLET_DELIVERY,PRE_AND_POST_NATAL,Mental_disorder", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": true, "claimValue": "INFECTIOUS_BACTERIAL_VIRAL_MEDICAL_MGMT"}, {"displayName": "Dedution Remarks", "value": "MANDATORY_COPAY", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Sum Insured Utilization", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}, {"displayName": "BenefitScope", "value": "Policy", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}]}, {"timeOfOccurrence": "/Date(1749199182465+0530)/", "category": "Copay", "subCategory": "RelationGradeAdmissibleAmount", "subCategoryDisplayName": "Copay By Admissible Amount", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Copay By Admissible Amount - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182470+0530)/", "category": "Copay", "subCategory": "VariantNotConfigured", "subCategoryDisplayName": "Condition To Be Reviewed", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Condition To Be Reviewed - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182471+0530)/", "category": "Copay", "subCategory": "RelationGradeAilment", "subCategoryDisplayName": "Ailment Category", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Ailment Category - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182472+0530)/", "category": "AilmentCapping", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Standard (IRDAI) - Execution starts", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Standard (IRDAI) - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 96163.96, "preExecutionTotalBillAmount": 115565.9, "preExectionTotalDeductionAmount": 19402.04, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182473+0530)/", "category": "AilmentCapping", "subCategory": "AilmentCappingByGrade", "subCategoryDisplayName": "Ailment Category", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Ailment Category - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182672+0530)/", "category": "AilmentCapping", "subCategory": "ProcedureCappingByApproachAndPostion", "subCategoryDisplayName": "Position and Approach", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Position and Approach - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182690+0530)/", "category": "AilmentCapping", "subCategory": "AilmentProcedureCappingBySIType", "subCategoryDisplayName": "Ailment Category and Procedure Combination", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Ailment Category and Procedure Combination - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182840+0530)/", "category": "AilmentCapping", "subCategory": "ProcedureCappingByGrade", "subCategoryDisplayName": "Procedure", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Procedure - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182860+0530)/", "category": "AilmentCapping", "subCategory": "AilmentCappingByClaimType", "subCategoryDisplayName": "Ailment Capping by <PERSON><PERSON><PERSON>", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Ailment Capping by <PERSON><PERSON>m Type - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182913+0530)/", "category": "AilmentCapping", "subCategory": "MaternityWellnessBabyCapping", "subCategoryDisplayName": "Maternity, wellness baby care & Pre-Post natal", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Maternity, wellness baby care & Pre-Post natal - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182952+0530)/", "category": "AilmentCapping", "subCategory": "VariantNotConfigured", "subCategoryDisplayName": "Condition To Be Reviewed", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Condition To Be Reviewed - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182952+0530)/", "category": "SIRestriction", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Standard - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182952+0530)/", "category": "SIRestriction", "subCategory": "OtherHealthCareSIRestriction", "subCategoryDisplayName": "SI Restriction By Health Care Types", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "SI Restriction By Health Care Types - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182955+0530)/", "category": "PolicyBenefits", "subCategory": "NotApplicable", "subCategoryDisplayName": "Not Applicable", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Not Applicable - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"timeOfOccurrence": "/Date(1749199182955+0530)/", "category": "UCRDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "loggerType": "Information", "logMessages": [{"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "*****Calculation On SOC Started*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "NO SOC Details Found", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "*****Calculation On SOC Ends*****", "loggerType": "Information"}, {"timeOfOccurrence": "/Date(1749824722294+0530)/", "message": "Standard - Execution ends", "loggerType": "Information"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}], "_will_be_processed": true}], "_processing_metadata": {"filtered_for_claim_logs_only": true, "processing_timestamp": "2025-06-13T19:55:26.312713", "total_logs": 2, "claim_logs_count": 2}}]