{"processed_claims_data": [{"_debug_info": {"total_claims": 8, "filtering_rule": "Only logs with typeOfLog: \"Claim\" will be processed", "alteration_timestamp": "2025-06-13T21:54:02.936367"}}, {"claimNo": *********, "logs": [{"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "preExecutionApprovedAmount": 214559.73, "preExecutionTotalBillAmount": 236928.95, "preExectionTotalDeductionAmount": 22369.27, "postExecutionApprovedAmount": 214559.73, "postExecutionTotalBillAmount": 236928.95, "postExectionTotalDeductionAmount": 22369.27, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "preExecutionApprovedAmount": 214559.73, "preExecutionTotalBillAmount": 236928.95, "preExectionTotalDeductionAmount": 22369.27, "postExecutionApprovedAmount": 214559.73, "postExecutionTotalBillAmount": 236928.95, "postExectionTotalDeductionAmount": 22369.27, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 214559.73 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 214559.73 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 259133.55, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 24002.29"}, {"message": "HMOU Discount Calculated: 22369.28"}, {"message": "MAX Discount Calculated: 24002.29"}, {"message": "Discount Calculation Is Goving with HMOU"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Consultant Charges: 5958.03,Investigation & Lab Charges: 6095.29,Miscellaneous Charges: 2779.84,Pharmacy & Medicine Charges: 2356.71,Hospital Charges: 5179.40"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 239928.95, "preExectionTotalDeductionAmount": 22369.27, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 236928.95, "postExectionTotalDeductionAmount": 22369.27, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9500.0"}, {"message": "Tariff ICU Room Rent From Master:9500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 3000.000"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 214559.73, "preExecutionTotalBillAmount": 236928.95, "preExectionTotalDeductionAmount": 22369.27, "postExecutionApprovedAmount": 214559.73, "postExecutionTotalBillAmount": 236928.95, "postExectionTotalDeductionAmount": 22369.27, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer room rent"}, {"message": "ICU : Room Rent based on insurer "}, {"message": "Normal Room Rent Lenght of Stay : 6.00"}, {"message": "ICU Room Rent Lenght of Stay : 0"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 6.00"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 9500.0"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 57000.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 239928.95, "preExectionTotalDeductionAmount": 22369.27, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 239928.95, "postExectionTotalDeductionAmount": 22369.27, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}], "_cleaning_metadata": {"fields_removed": ["logCreatedDate (from logs)", "data (from logs)", "timeOfOccurrence (from data entries and rule logs)", "loggerType (from data entries and rule logs)", "configRuleLogObject (from rule engine logs)", "matchingConfig (from rule engine logs)"], "fields_renamed": ["logMessages → reasons (in rule engine logs)"], "filtering_applied": ["Only rule engine logs with non-empty matchingConfig arrays are included", "Only rule engine logs with non-zero postExecutionTotalBillAmount are included"], "grouping_applied": ["Rule engine logs grouped by category as dictionary structure with metadata support"], "cleaning_timestamp": "2025-06-13T21:54:02.798934", "purpose": "Remove unnecessary fields, rename for clarity, filter for relevant rule logs, and group by category"}, "_processing_metadata": {"filtered_for_claim_logs_only": true, "processing_timestamp": "2025-06-13T21:54:02.936319", "total_logs": 1, "claim_logs_count": 1}}, {"claimNo": *********, "logs": [{"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "preExecutionApprovedAmount": 249428.16, "preExecutionTotalBillAmount": 275660.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 249428.16, "postExecutionTotalBillAmount": 275660.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "preExecutionApprovedAmount": 249428.16, "preExecutionTotalBillAmount": 275660.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 249428.16, "postExecutionTotalBillAmount": 275660.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 249428.16 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 249428.16 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 294657.45, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 26231.84"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 26231.84"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Miscellaneous Charges: 1380.00,Investigation & Lab Charges: 7684.00,Consultant Charges: 7896.80,Pharmacy & Medicine Charges: 1386.04,Hospital Charges: 7885.00"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 278660.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 275660.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9500.0"}, {"message": "Tariff ICU Room Rent From Master:9500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.000"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 249428.16, "preExecutionTotalBillAmount": 275660.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 249428.16, "postExecutionTotalBillAmount": 275660.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer nursing,room rent"}, {"message": "ICU : Room Rent based on insurer icu charges,icu monitoring charges"}, {"message": "Normal Room Rent Lenght of Stay : 5.50"}, {"message": "ICU Room Rent Lenght of Stay : 2.00"}, {"message": "Nursing Charges PerDay : 0.0"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 2.00"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 11800"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 5.50"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 9499.99"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 52250.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 23600.00"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 278660.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 278660.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}, {"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "preExecutionApprovedAmount": 229628.16, "preExecutionTotalBillAmount": 255860.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 229628.16, "postExecutionTotalBillAmount": 255860.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "preExecutionApprovedAmount": 229628.16, "preExecutionTotalBillAmount": 255860.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 229628.16, "postExecutionTotalBillAmount": 255860.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 229628.16 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 229628.16 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 294657.45, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 26231.84"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 26231.84"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Miscellaneous Charges: 1380.00,Investigation & Lab Charges: 7684.00,Consultant Charges: 7896.80,Pharmacy & Medicine Charges: 1386.04,Hospital Charges: 7885.00"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 258860.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 255860.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9500.0"}, {"message": "Tariff ICU Room Rent From Master:9500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.000"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 229628.16, "preExecutionTotalBillAmount": 255860.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 229628.16, "postExecutionTotalBillAmount": 255860.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer nursing,room rent"}, {"message": "ICU : Room Rent based on insurer icu charges,icu monitoring charges"}, {"message": "Normal Room Rent Lenght of Stay : 5.50"}, {"message": "ICU Room Rent Lenght of Stay : 2.00"}, {"message": "Nursing Charges PerDay : 0.0"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 2.00"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 11800"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 5.50"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 9499.99"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 52250.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 23600.00"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 258860.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 258860.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}, {"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "preExecutionApprovedAmount": 229628.16, "preExecutionTotalBillAmount": 255860.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 229628.16, "postExecutionTotalBillAmount": 255860.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "preExecutionApprovedAmount": 229628.16, "preExecutionTotalBillAmount": 255860.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 229628.16, "postExecutionTotalBillAmount": 255860.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 229628.16 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 229628.16 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 294657.45, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 26231.84"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 26231.84"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Miscellaneous Charges: 1380.00,Investigation & Lab Charges: 7684.00,Consultant Charges: 7896.80,Pharmacy & Medicine Charges: 1386.04,Hospital Charges: 7885.00"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 258860.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 255860.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9500.0"}, {"message": "Tariff ICU Room Rent From Master:9500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.000"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 229628.16, "preExecutionTotalBillAmount": 255860.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 229628.16, "postExecutionTotalBillAmount": 255860.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer nursing,room rent"}, {"message": "ICU : Room Rent based on insurer icu charges,icu monitoring charges"}, {"message": "Normal Room Rent Lenght of Stay : 5.50"}, {"message": "ICU Room Rent Lenght of Stay : 2.00"}, {"message": "Nursing Charges PerDay : 0.0"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 2.00"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 11800"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 5.50"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 9499.99"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 52250.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 23600.00"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 258860.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 258860.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}, {"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "preExecutionApprovedAmount": 229628.16, "preExecutionTotalBillAmount": 255860.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 229628.16, "postExecutionTotalBillAmount": 255860.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "preExecutionApprovedAmount": 229628.16, "preExecutionTotalBillAmount": 255860.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 229628.16, "postExecutionTotalBillAmount": 255860.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 229628.16 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 229628.16 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 294657.45, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 26231.84"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 26231.84"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Miscellaneous Charges: 1380.00,Investigation & Lab Charges: 7684.00,Consultant Charges: 7896.80,Pharmacy & Medicine Charges: 1386.04,Hospital Charges: 7885.00"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 258860.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 255860.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9500.0"}, {"message": "Tariff ICU Room Rent From Master:9500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.000"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 229628.16, "preExecutionTotalBillAmount": 255860.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 229628.16, "postExecutionTotalBillAmount": 255860.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer nursing,room rent"}, {"message": "ICU : Room Rent based on insurer icu charges,icu monitoring charges"}, {"message": "Normal Room Rent Lenght of Stay : 5.50"}, {"message": "ICU Room Rent Lenght of Stay : 2.00"}, {"message": "Nursing Charges PerDay : 0.0"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 2.00"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 11800"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 5.50"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 9499.99"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 52250.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 23600.00"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 258860.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 258860.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}, {"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "preExecutionApprovedAmount": 228028.16, "preExecutionTotalBillAmount": 254260.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 228028.16, "postExecutionTotalBillAmount": 254260.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "preExecutionApprovedAmount": 228028.16, "preExecutionTotalBillAmount": 254260.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 228028.16, "postExecutionTotalBillAmount": 254260.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 228028.16 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 228028.16 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 294657.45, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 26231.84"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 26231.84"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Hospital Charges: 7885.00,Pharmacy & Medicine Charges: 1386.04,Investigation & Lab Charges: 7684.00,Miscellaneous Charges: 1380.00,Consultant Charges: 7896.80"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 258860.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 254260.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9500.0"}, {"message": "Tariff ICU Room Rent From Master:9500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.000"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "ICU Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 800.000"}, {"message": "ICU Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 800.00"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 228028.16, "preExecutionTotalBillAmount": 254260.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 228028.16, "postExecutionTotalBillAmount": 254260.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer room rent,nursing"}, {"message": "ICU : Room Rent based on insurer icu charges,icu nursing charges"}, {"message": "Normal Room Rent Lenght of Stay : 5.50"}, {"message": "ICU Room Rent Lenght of Stay : 2.00"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Nursing Charges PerDay : 0.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 2.00"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 11000.0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 5.50"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 9499.99"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 52250.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 22000.000"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 258860.35, "preExectionTotalDeductionAmount": 26231.84, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 258860.35, "postExectionTotalDeductionAmount": 26231.84, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}], "_cleaning_metadata": {"fields_removed": ["logCreatedDate (from logs)", "data (from logs)", "timeOfOccurrence (from data entries and rule logs)", "loggerType (from data entries and rule logs)", "configRuleLogObject (from rule engine logs)", "matchingConfig (from rule engine logs)"], "fields_renamed": ["logMessages → reasons (in rule engine logs)"], "filtering_applied": ["Only rule engine logs with non-empty matchingConfig arrays are included", "Only rule engine logs with non-zero postExecutionTotalBillAmount are included"], "grouping_applied": ["Rule engine logs grouped by category as dictionary structure with metadata support"], "cleaning_timestamp": "2025-06-13T21:54:02.846224", "purpose": "Remove unnecessary fields, rename for clarity, filter for relevant rule logs, and group by category"}, "_processing_metadata": {"filtered_for_claim_logs_only": true, "processing_timestamp": "2025-06-13T21:54:02.936346", "total_logs": 5, "claim_logs_count": 5}}, {"claimNo": *********, "logs": [{"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "preExecutionApprovedAmount": 209439.95, "preExecutionTotalBillAmount": 259801.2, "preExectionTotalDeductionAmount": 50361.05, "postExecutionApprovedAmount": 209439.95, "postExecutionTotalBillAmount": 259801.2, "postExectionTotalDeductionAmount": 50361.05, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "preExecutionApprovedAmount": 209439.95, "preExecutionTotalBillAmount": 259801.2, "preExectionTotalDeductionAmount": 50361.05, "postExecutionApprovedAmount": 209439.95, "postExecutionTotalBillAmount": 259801.2, "postExectionTotalDeductionAmount": 50361.05, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 209439.95 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 209439.95 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 292520.8, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 27177.62"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 27177.62"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Investigation & Lab Charges: 12690.185955487609769971913110,Consultant Charges: 15132.125244782593237814199880,Hospital Charges: 13263.598678138443488463042630,Pharmacy & Medicine Charges: 3400.6437938635474810680129419,Miscellaneous Charges: 5874.4963277278060226828314438"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 270801.2, "preExectionTotalDeductionAmount": 50361.05, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 259801.2, "postExectionTotalDeductionAmount": 50361.05, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9500.0"}, {"message": "Tariff ICU Room Rent From Master:9500.0"}, {"message": "ICU Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 11000.000"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 209439.95, "preExecutionTotalBillAmount": 259801.2, "preExectionTotalDeductionAmount": 50361.05, "postExecutionApprovedAmount": 209439.95, "postExecutionTotalBillAmount": 259801.2, "postExectionTotalDeductionAmount": 50361.05, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer room rent"}, {"message": "ICU : Room Rent based on insurer icu charges"}, {"message": "Normal Room Rent Lenght of Stay : 6.00"}, {"message": "ICU Room Rent Lenght of Stay : 1.00"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 1.00"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 11000.0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 6.00"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 8708.33"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 57000.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 11000.000"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 270801.2, "preExectionTotalDeductionAmount": 50361.05, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 270801.2, "postExectionTotalDeductionAmount": 50361.05, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}, {"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "preExecutionApprovedAmount": 181449.63, "preExecutionTotalBillAmount": 208841.2, "preExectionTotalDeductionAmount": 27391.37, "postExecutionApprovedAmount": 181449.63, "postExecutionTotalBillAmount": 208841.2, "postExectionTotalDeductionAmount": 27391.37, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "preExecutionApprovedAmount": 181449.63, "preExecutionTotalBillAmount": 208841.2, "preExectionTotalDeductionAmount": 27391.37, "postExecutionApprovedAmount": 181449.63, "postExecutionTotalBillAmount": 208841.2, "postExectionTotalDeductionAmount": 27391.37, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 181449.63 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 181449.63 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 294658.3, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 27391.37"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 27391.37"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Consultant Charges: 8471.00,Investigation & Lab Charges: 7104.00,Miscellaneous Charges: 3288.56,Pharmacy & Medicine Charges: 889.06,Hospital Charges: 7638.75"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 275728.7, "preExectionTotalDeductionAmount": 27391.37, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 208841.2, "postExectionTotalDeductionAmount": 27391.37, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9500.0"}, {"message": "Tariff ICU Room Rent From Master:9500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 66887.500"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 181449.63, "preExecutionTotalBillAmount": 208841.2, "preExectionTotalDeductionAmount": 27391.37, "postExecutionApprovedAmount": 181449.63, "postExecutionTotalBillAmount": 208841.2, "postExectionTotalDeductionAmount": 27391.37, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer room rent"}, {"message": "ICU : Room Rent based on insurer "}, {"message": "Normal Room Rent Lenght of Stay : 1.00"}, {"message": "ICU Room Rent Lenght of Stay : 0"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 1.00"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 9500.0"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 9500.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 275728.7, "preExectionTotalDeductionAmount": 27391.37, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 275728.7, "postExectionTotalDeductionAmount": 27391.37, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}, {"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "preExecutionApprovedAmount": 181449.63, "preExecutionTotalBillAmount": 208841.2, "preExectionTotalDeductionAmount": 27391.37, "postExecutionApprovedAmount": 181449.63, "postExecutionTotalBillAmount": 208841.2, "postExectionTotalDeductionAmount": 27391.37, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "preExecutionApprovedAmount": 181449.63, "preExecutionTotalBillAmount": 208841.2, "preExectionTotalDeductionAmount": 27391.37, "postExecutionApprovedAmount": 181449.63, "postExecutionTotalBillAmount": 208841.2, "postExectionTotalDeductionAmount": 27391.37, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 181449.63 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 181449.63 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 294658.3, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 27391.37"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 27391.37"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Consultant Charges: 8471.00,Investigation & Lab Charges: 7104.00,Miscellaneous Charges: 3288.56,Pharmacy & Medicine Charges: 889.06,Hospital Charges: 7638.75"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 275728.7, "preExectionTotalDeductionAmount": 27391.37, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 208841.2, "postExectionTotalDeductionAmount": 27391.37, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9500.0"}, {"message": "Tariff ICU Room Rent From Master:9500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 66887.500"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 181449.63, "preExecutionTotalBillAmount": 208841.2, "preExectionTotalDeductionAmount": 27391.37, "postExecutionApprovedAmount": 181449.63, "postExecutionTotalBillAmount": 208841.2, "postExectionTotalDeductionAmount": 27391.37, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer room rent"}, {"message": "ICU : Room Rent based on insurer "}, {"message": "Normal Room Rent Lenght of Stay : 1.00"}, {"message": "ICU Room Rent Lenght of Stay : 0"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 1.00"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 9500.0"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 9500.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 275728.7, "preExectionTotalDeductionAmount": 27391.37, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 275728.7, "postExectionTotalDeductionAmount": 27391.37, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}], "_cleaning_metadata": {"fields_removed": ["logCreatedDate (from logs)", "data (from logs)", "timeOfOccurrence (from data entries and rule logs)", "loggerType (from data entries and rule logs)", "configRuleLogObject (from rule engine logs)", "matchingConfig (from rule engine logs)"], "fields_renamed": ["logMessages → reasons (in rule engine logs)"], "filtering_applied": ["Only rule engine logs with non-empty matchingConfig arrays are included", "Only rule engine logs with non-zero postExecutionTotalBillAmount are included"], "grouping_applied": ["Rule engine logs grouped by category as dictionary structure with metadata support"], "cleaning_timestamp": "2025-06-13T21:54:02.872226", "purpose": "Remove unnecessary fields, rename for clarity, filter for relevant rule logs, and group by category"}, "_processing_metadata": {"filtered_for_claim_logs_only": true, "processing_timestamp": "2025-06-13T21:54:02.936350", "total_logs": 3, "claim_logs_count": 3}}, {"claimNo": *********, "logs": [{"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "preExecutionApprovedAmount": 144204.34, "preExecutionTotalBillAmount": 163672.97, "preExectionTotalDeductionAmount": 19468.66, "postExecutionApprovedAmount": 144204.34, "postExecutionTotalBillAmount": 163672.97, "postExectionTotalDeductionAmount": 19468.66, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "preExecutionApprovedAmount": 144204.34, "preExecutionTotalBillAmount": 163672.97, "preExectionTotalDeductionAmount": 19468.66, "postExecutionApprovedAmount": 144204.34, "postExecutionTotalBillAmount": 163672.97, "postExectionTotalDeductionAmount": 19468.66, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 144204.34 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 144204.34 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 211364.07, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 19468.66"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 19468.66"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Consultant Charges: 5349.00,Investigation & Lab Charges: 6449.00,Miscellaneous Charges: 2855.91,Pharmacy & Medicine Charges: 714.75,Hospital Charges: 4100.00"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 195172.97, "preExectionTotalDeductionAmount": 19468.66, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 163672.97, "postExectionTotalDeductionAmount": 19468.66, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9500.0"}, {"message": "Tariff ICU Room Rent From Master:9500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 31500.000"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 144204.34, "preExecutionTotalBillAmount": 163672.97, "preExectionTotalDeductionAmount": 19468.66, "postExecutionApprovedAmount": 144204.34, "postExecutionTotalBillAmount": 163672.97, "postExectionTotalDeductionAmount": 19468.66, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer room rent"}, {"message": "ICU : Room Rent based on insurer "}, {"message": "Normal Room Rent Lenght of Stay : 1.00"}, {"message": "ICU Room Rent Lenght of Stay : 0"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 1.00"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 9500.0"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 9500.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 195172.97, "preExectionTotalDeductionAmount": 19468.66, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 195172.97, "postExectionTotalDeductionAmount": 19468.66, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}], "_cleaning_metadata": {"fields_removed": ["logCreatedDate (from logs)", "data (from logs)", "timeOfOccurrence (from data entries and rule logs)", "loggerType (from data entries and rule logs)", "configRuleLogObject (from rule engine logs)", "matchingConfig (from rule engine logs)"], "fields_renamed": ["logMessages → reasons (in rule engine logs)"], "filtering_applied": ["Only rule engine logs with non-empty matchingConfig arrays are included", "Only rule engine logs with non-zero postExecutionTotalBillAmount are included"], "grouping_applied": ["Rule engine logs grouped by category as dictionary structure with metadata support"], "cleaning_timestamp": "2025-06-13T21:54:02.880902", "purpose": "Remove unnecessary fields, rename for clarity, filter for relevant rule logs, and group by category"}, "_processing_metadata": {"filtered_for_claim_logs_only": true, "processing_timestamp": "2025-06-13T21:54:02.936352", "total_logs": 1, "claim_logs_count": 1}}, {"claimNo": *********, "logs": [{"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "preExecutionApprovedAmount": 111214.85, "preExecutionTotalBillAmount": 125826.25, "preExectionTotalDeductionAmount": 14611.15, "postExecutionApprovedAmount": 111214.85, "postExecutionTotalBillAmount": 125826.25, "postExectionTotalDeductionAmount": 14611.15, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "preExecutionApprovedAmount": 111214.85, "preExecutionTotalBillAmount": 125826.25, "preExectionTotalDeductionAmount": 14611.15, "postExecutionApprovedAmount": 111214.85, "postExecutionTotalBillAmount": 125826.25, "postExectionTotalDeductionAmount": 14611.15, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 111214.85 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 111214.85 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 161349.25, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 14611.15"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 14611.15"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Consultant Charges: 3799.00,Investigation & Lab Charges: 5616.00,Miscellaneous Charges: 1393.10,Pharmacy & Medicine Charges: 653.05,Hospital Charges: 3150.00"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 150826.25, "preExectionTotalDeductionAmount": 14611.15, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 125826.25, "postExectionTotalDeductionAmount": 14611.15, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SemiPrivateRoom"}, {"message": "Tariff Room Rent From Master:6500.0"}, {"message": "Tariff ICU Room Rent From Master:6500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 25000.000"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 111214.85, "preExecutionTotalBillAmount": 125826.25, "preExectionTotalDeductionAmount": 14611.15, "postExecutionApprovedAmount": 111214.85, "postExecutionTotalBillAmount": 125826.25, "postExectionTotalDeductionAmount": 14611.15, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SemiPrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer room rent"}, {"message": "ICU : Room Rent based on insurer "}, {"message": "Normal Room Rent Lenght of Stay : 1.00"}, {"message": "ICU Room Rent Lenght of Stay : 0"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 1.00"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 6500.0"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 9500.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 150826.25, "preExectionTotalDeductionAmount": 14611.15, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 150826.25, "postExectionTotalDeductionAmount": 14611.15, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}], "_cleaning_metadata": {"fields_removed": ["logCreatedDate (from logs)", "data (from logs)", "timeOfOccurrence (from data entries and rule logs)", "loggerType (from data entries and rule logs)", "configRuleLogObject (from rule engine logs)", "matchingConfig (from rule engine logs)"], "fields_renamed": ["logMessages → reasons (in rule engine logs)"], "filtering_applied": ["Only rule engine logs with non-empty matchingConfig arrays are included", "Only rule engine logs with non-zero postExecutionTotalBillAmount are included"], "grouping_applied": ["Rule engine logs grouped by category as dictionary structure with metadata support"], "cleaning_timestamp": "2025-06-13T21:54:02.889354", "purpose": "Remove unnecessary fields, rename for clarity, filter for relevant rule logs, and group by category"}, "_processing_metadata": {"filtered_for_claim_logs_only": true, "processing_timestamp": "2025-06-13T21:54:02.936354", "total_logs": 1, "claim_logs_count": 1}}, {"claimNo": *********, "logs": [{"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "preExecutionApprovedAmount": 151429.19, "preExecutionTotalBillAmount": 172724.74, "preExectionTotalDeductionAmount": 21295.81, "postExecutionApprovedAmount": 151429.19, "postExecutionTotalBillAmount": 172724.74, "postExectionTotalDeductionAmount": 21295.81, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "preExecutionApprovedAmount": 151429.19, "preExecutionTotalBillAmount": 172724.74, "preExectionTotalDeductionAmount": 21295.81, "postExecutionApprovedAmount": 151429.19, "postExecutionTotalBillAmount": 172724.74, "postExectionTotalDeductionAmount": 21295.81, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 151429.19 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 151429.19 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 231190.84, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 21295.81"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 21295.81"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Consultant Charges: 5673.00,Investigation & Lab Charges: 6808.00,Miscellaneous Charges: 2983.41,Pharmacy & Medicine Charges: 781.40,Hospital Charges: 5050.00"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 213724.74, "preExectionTotalDeductionAmount": 21295.81, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 172724.74, "postExectionTotalDeductionAmount": 21295.81, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9500.0"}, {"message": "Tariff ICU Room Rent From Master:9500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 41000.000"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 151429.19, "preExecutionTotalBillAmount": 172724.74, "preExectionTotalDeductionAmount": 21295.81, "postExecutionApprovedAmount": 151429.19, "postExecutionTotalBillAmount": 172724.74, "postExectionTotalDeductionAmount": 21295.81, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer room rent"}, {"message": "ICU : Room Rent based on insurer "}, {"message": "Normal Room Rent Lenght of Stay : 1.00"}, {"message": "ICU Room Rent Lenght of Stay : 0"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 1.00"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 9500.0"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 9500.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 213724.74, "preExectionTotalDeductionAmount": 21295.81, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 213724.74, "postExectionTotalDeductionAmount": 21295.81, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}], "_cleaning_metadata": {"fields_removed": ["logCreatedDate (from logs)", "data (from logs)", "timeOfOccurrence (from data entries and rule logs)", "loggerType (from data entries and rule logs)", "configRuleLogObject (from rule engine logs)", "matchingConfig (from rule engine logs)"], "fields_renamed": ["logMessages → reasons (in rule engine logs)"], "filtering_applied": ["Only rule engine logs with non-empty matchingConfig arrays are included", "Only rule engine logs with non-zero postExecutionTotalBillAmount are included"], "grouping_applied": ["Rule engine logs grouped by category as dictionary structure with metadata support"], "cleaning_timestamp": "2025-06-13T21:54:02.897799", "purpose": "Remove unnecessary fields, rename for clarity, filter for relevant rule logs, and group by category"}, "_processing_metadata": {"filtered_for_claim_logs_only": true, "processing_timestamp": "2025-06-13T21:54:02.936356", "total_logs": 1, "claim_logs_count": 1}}, {"claimNo": *********, "logs": [{"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "preExecutionApprovedAmount": 66935.0, "preExecutionTotalBillAmount": 79105.0, "preExectionTotalDeductionAmount": 12170.0, "postExecutionApprovedAmount": 66935.0, "postExecutionTotalBillAmount": 79105.0, "postExectionTotalDeductionAmount": 12170.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "preExecutionApprovedAmount": 66935.0, "preExecutionTotalBillAmount": 79105.0, "preExectionTotalDeductionAmount": 12170.0, "postExecutionApprovedAmount": 66935.0, "postExecutionTotalBillAmount": 79105.0, "postExectionTotalDeductionAmount": 12170.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 66935.00 and flat amount 12500 is 8366.875"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 8366.875"}, {"message": "Copay deduction amount is 8366.875"}, {"message": "Copay deduction amount is: 8366.875"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 66935.00 and flat amount 12500 is 8366.875 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 121700.0, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 12170.00"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 12170.00"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Package: 12170.00"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "NonMedicalExpense": {"rules": [{"category": "NonMedicalExpense", "subCategory": "HandelNMEByPreAuthType", "subCategoryDisplayName": "Estimate For Initial", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 121700.0, "preExectionTotalDeductionAmount": 12170.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 79105.0, "postExectionTotalDeductionAmount": 12170.0, "reasons": [{"message": "*****Calulating CalculateNMEDeduction Started*****"}, {"message": "Matching Config Count: 1"}, {"message": "Estimate For Initial - Execution starts"}, {"message": "CalculateNMEDeduction isInitialPA: TRUE"}, {"message": "NMEDeduction percentage: 35"}, {"message": "*****Calulating CalculateNMEDeduction End*****"}, {"message": "Estimate For Initial - Execution ends"}]}], "count": 1, "category_name": "NonMedicalExpense"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 79105.0, "preExectionTotalDeductionAmount": 12170.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 79105.0, "postExectionTotalDeductionAmount": 12170.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :ICU"}, {"message": "Tariff ICU Room Rent From Master:0"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 66935.0, "preExecutionTotalBillAmount": 79105.0, "preExectionTotalDeductionAmount": 12170.0, "postExecutionApprovedAmount": 66935.0, "postExecutionTotalBillAmount": 79105.0, "postExectionTotalDeductionAmount": 12170.0, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category ICU"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer "}, {"message": "ICU : Room Rent based on insurer "}, {"message": "Due to Initial PA LengthOfStay calculated on DOD - DOA "}, {"message": "Normal Room Rent Lenght of Stay : 1"}, {"message": "ICU Room Rent Lenght of Stay : 1"}, {"message": "Tariff Bill Price as per insurer :0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Due to Initial PA LengthOfStay calculated on DOD - DOA "}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 1"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 1"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 9500.0"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 79105.0, "preExectionTotalDeductionAmount": 12170.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 79105.0, "postExectionTotalDeductionAmount": 12170.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}], "_cleaning_metadata": {"fields_removed": ["logCreatedDate (from logs)", "data (from logs)", "timeOfOccurrence (from data entries and rule logs)", "loggerType (from data entries and rule logs)", "configRuleLogObject (from rule engine logs)", "matchingConfig (from rule engine logs)"], "fields_renamed": ["logMessages → reasons (in rule engine logs)"], "filtering_applied": ["Only rule engine logs with non-empty matchingConfig arrays are included", "Only rule engine logs with non-zero postExecutionTotalBillAmount are included"], "grouping_applied": ["Rule engine logs grouped by category as dictionary structure with metadata support"], "cleaning_timestamp": "2025-06-13T21:54:02.906426", "purpose": "Remove unnecessary fields, rename for clarity, filter for relevant rule logs, and group by category"}, "_processing_metadata": {"filtered_for_claim_logs_only": true, "processing_timestamp": "2025-06-13T21:54:02.936358", "total_logs": 1, "claim_logs_count": 1}}, {"claimNo": *********, "logs": [{"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "preExecutionApprovedAmount": 252799.1, "preExecutionTotalBillAmount": 278358.45, "preExectionTotalDeductionAmount": 25558.9, "postExecutionApprovedAmount": 252799.1, "postExecutionTotalBillAmount": 278358.45, "postExectionTotalDeductionAmount": 25558.9, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Copay applicable by: PecentageOfAdmissibleAmount on each claim basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "10% of copay applied on 252799.10 and capped value is 25279.91"}, {"message": "Copay applicable based on each claim basis"}, {"message": "Copay value: 25279.91"}, {"message": "Copay deduction amount is 25279.91"}, {"message": "Copay deduction amount is: 25279.91"}, {"message": "Copay Deduction Remarks: 10% of copay applied on 252799.10 and capped value is 25279.91 (Intimation Copay)"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "preExecutionApprovedAmount": 227519.19, "preExecutionTotalBillAmount": 278358.45, "preExectionTotalDeductionAmount": 50838.81, "postExecutionApprovedAmount": 227519.19, "postExecutionTotalBillAmount": 278358.45, "postExectionTotalDeductionAmount": 50838.81, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 227519.19 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 227519.19 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 294658.45, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 26231.87"}, {"message": "HMOU Discount Calculated: 25559"}, {"message": "MAX Discount Calculated: 26231.87"}, {"message": "Discount Calculation Is Goving with HMOU"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Hospital Charges: 6839.50,Investigation & Lab Charges: 6665.13,Miscellaneous Charges: 1197.01,Consultant Charges: 6849.76,Pharmacy & Medicine Charges: 4007.50"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 281358.45, "preExectionTotalDeductionAmount": 25558.9, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 278358.45, "postExectionTotalDeductionAmount": 25558.9, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9500.0"}, {"message": "Tariff ICU Room Rent From Master:9500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.000"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 252799.1, "preExecutionTotalBillAmount": 278358.45, "preExectionTotalDeductionAmount": 25558.9, "postExecutionApprovedAmount": 252799.1, "postExecutionTotalBillAmount": 278358.45, "postExectionTotalDeductionAmount": 25558.9, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer nursing,room rent"}, {"message": "ICU : Room Rent based on insurer icu charges,icu monitoring charges"}, {"message": "Normal Room Rent Lenght of Stay : 5.50"}, {"message": "ICU Room Rent Lenght of Stay : 2.00"}, {"message": "Nursing Charges PerDay : 0.0"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 2.00"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 11800"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 5.50"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 9499.99"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 52250.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 23600.00"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 281358.45, "preExectionTotalDeductionAmount": 25558.9, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 281358.45, "postExectionTotalDeductionAmount": 25558.9, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}, {"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "preExecutionApprovedAmount": 252799.1, "preExecutionTotalBillAmount": 278358.45, "preExectionTotalDeductionAmount": 25558.9, "postExecutionApprovedAmount": 252799.1, "postExecutionTotalBillAmount": 278358.45, "postExectionTotalDeductionAmount": 25558.9, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Copay applicable by: PecentageOfAdmissibleAmount on each claim basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "10% of copay applied on 252799.10 and capped value is 25279.91"}, {"message": "Copay applicable based on each claim basis"}, {"message": "Copay value: 25279.91"}, {"message": "Copay deduction amount is 25279.91"}, {"message": "Copay deduction amount is: 25279.91"}, {"message": "Copay Deduction Remarks: 10% of copay applied on 252799.10 and capped value is 25279.91 (Intimation Copay)"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "preExecutionApprovedAmount": 227519.19, "preExecutionTotalBillAmount": 278358.45, "preExectionTotalDeductionAmount": 50838.81, "postExecutionApprovedAmount": 227519.19, "postExecutionTotalBillAmount": 278358.45, "postExectionTotalDeductionAmount": 50838.81, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 227519.19 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 227519.19 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 294658.45, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 26231.87"}, {"message": "HMOU Discount Calculated: 25559"}, {"message": "MAX Discount Calculated: 26231.87"}, {"message": "Discount Calculation Is Goving with HMOU"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Hospital Charges: 6839.50,Investigation & Lab Charges: 6665.13,Miscellaneous Charges: 1197.01,Consultant Charges: 6849.76,Pharmacy & Medicine Charges: 4007.50"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 281358.45, "preExectionTotalDeductionAmount": 25558.9, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 278358.45, "postExectionTotalDeductionAmount": 25558.9, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9500.0"}, {"message": "Tariff ICU Room Rent From Master:9500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.000"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "preExecutionApprovedAmount": 252799.1, "preExecutionTotalBillAmount": 278358.45, "preExectionTotalDeductionAmount": 25558.9, "postExecutionApprovedAmount": 252799.1, "postExecutionTotalBillAmount": 278358.45, "postExectionTotalDeductionAmount": 25558.9, "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer nursing,room rent"}, {"message": "ICU : Room Rent based on insurer icu charges,icu monitoring charges"}, {"message": "Normal Room Rent Lenght of Stay : 5.50"}, {"message": "ICU Room Rent Lenght of Stay : 2.00"}, {"message": "Nursing Charges PerDay : 0.0"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 2.00"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 11800"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 5.50"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 9499.99"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 52250.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 23600.00"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 281358.45, "preExectionTotalDeductionAmount": 25558.9, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 281358.45, "postExectionTotalDeductionAmount": 25558.9, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}], "_cleaning_metadata": {"fields_removed": ["logCreatedDate (from logs)", "data (from logs)", "timeOfOccurrence (from data entries and rule logs)", "loggerType (from data entries and rule logs)", "configRuleLogObject (from rule engine logs)", "matchingConfig (from rule engine logs)"], "fields_renamed": ["logMessages → reasons (in rule engine logs)"], "filtering_applied": ["Only rule engine logs with non-empty matchingConfig arrays are included", "Only rule engine logs with non-zero postExecutionTotalBillAmount are included"], "grouping_applied": ["Rule engine logs grouped by category as dictionary structure with metadata support"], "cleaning_timestamp": "2025-06-13T21:54:02.924932", "purpose": "Remove unnecessary fields, rename for clarity, filter for relevant rule logs, and group by category"}, "_processing_metadata": {"filtered_for_claim_logs_only": true, "processing_timestamp": "2025-06-13T21:54:02.936361", "total_logs": 2, "claim_logs_count": 2}}], "processed_claim_details": {"claimDetails": [{"relatedPreAuths": [{"id": *********, "recdate": "/Date(1738242852000+0530)/", "typeId": 0, "doa": "/Date(*************+0530)/", "dod": "/Date(*************+0530)/", "type": "Enhancement PA", "strAmount": "2,92,521", "strTotalBillAmt": "2,92,521", "strApprovedAmt": "4,615", "amount": 292521, "statusId": 12, "statusName": "Cancelled", "totalBillAmt": 0, "approvedAmt": 4615, "letterRemarks": "APPROVED RS 12500/- MA<PERSON><PERSON>ORY CO PAY DEDCUTED AS PER POLICY TERMS AND CONDITIONS", "infoReqDate": "/Date(-**************)/", "docRecDate": "/Date(-**************)/", "appDate": "/Date(-**************)/", "settlementDate": "/Date(-**************)/", "denialDate": "/Date(-**************)/", "lastAuditDate": "/Date(-**************)/", "regionId": 14, "emailId": "13255149,13255290", "faxMsgId": "NULL", "provisionalDiag": "Left ventricular failure", "budget": 292521, "isFinal": true, "isLessThanIntial": false, "check": false, "ailmentCode": "11181", "paDeductionDetails": [{"slNo": 62833607, "claimId": *********, "deductionId": 5, "deductionDesc": "Hospital Discount", "amount": 25558.0, "remarks": "HOSPITAL DISCOUNT"}, {"slNo": 62833608, "claimId": *********, "deductionId": 1, "deductionDesc": "Copay", "amount": 12500.0, "remarks": "MANDATORY CO PAY"}, {"slNo": 62833649, "claimId": *********, "deductionId": 2, "deductionDesc": "Excess of Defined / Ailment Limit", "amount": 0.0, "remarks": ""}, {"slNo": 62833650, "claimId": *********, "deductionId": 9, "deductionDesc": "Deductible Amount", "amount": 0.0, "remarks": ""}, {"slNo": 62833651, "claimId": *********, "deductionId": 6, "deductionDesc": "Paid by the Patient", "amount": 0.0, "remarks": ""}, {"slNo": 62833652, "claimId": *********, "deductionId": 10, "deductionDesc": "Intimation Deductible", "amount": 0.0, "remarks": ""}, {"slNo": 62833653, "claimId": *********, "deductionId": 11, "deductionDesc": "Usual Customary Rates", "amount": 0.0, "remarks": ""}, {"slNo": 62833654, "claimId": *********, "deductionId": 3, "deductionDesc": "Policy Excess / Deductible", "amount": 0.0, "remarks": ""}], "paDenialClauses": [], "bills": [{"id": 279581245, "for": "consultation / visit", "date": "/Date(**********000+0530)/", "number": "100325 IPCR 004585", "amt": 59500, "payableAmt": 59500, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 10, "clmBillLOS": 1}, {"id": 279583925, "for": "service charges", "date": "/Date(**********000+0530)/", "number": "100325 IPCR 004585", "amt": 13956, "payableAmt": 11166, "dedReasonDesc": "NONMEDICAL ITEMS AMOUNT CLAIMED:-2790.00", "nonPayableAmt": 2790, "packageID": 0, "isDeleted": false, "ctype": 4, "clmBillLOS": 2}, {"id": 279574540, "for": "bio chemistry (lft,rf,lipid)", "date": "/Date(**********000+0530)/", "number": "100325 IPCR 004585", "amt": 71040, "payableAmt": 71040, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 2, "clmBillLOS": 1}, {"id": 279581246, "for": "icu charges", "date": "/Date(**********000+0530)/", "number": "100325 IPCR 004585", "amt": 22000, "payableAmt": 11000, "dedReasonDesc": "ICU Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 11000.000:-11000.000", "nonPayableAmt": 11000, "packageID": 0, "isDeleted": false, "ctype": 1, "clmBillLOS": 1}, {"id": 279581247, "for": "medicines/drugs", "date": "/Date(**********000+0530)/", "number": "100325 IPCR 004585", "amt": 29635, "payableAmt": 29635, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 14, "clmBillLOS": 2}, {"id": 279581248, "for": "other miscellaneous charges", "date": "/Date(**********000+0530)/", "number": "100325 IPCR 004585", "amt": 18930, "payableAmt": 0, "dedReasonDesc": "consumable:-18929.60", "nonPayableAmt": 18930, "packageID": 0, "isDeleted": false, "ctype": 4, "clmBillLOS": 1}, {"id": 279581249, "for": "physiotherapy charges", "date": "/Date(**********000+0530)/", "number": "100325 IPCR 004585", "amt": 8960, "payableAmt": 8960, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 10, "clmBillLOS": 1}, {"id": 279581250, "for": "procedures charges", "date": "/Date(**********000+0530)/", "number": "100325 IPCR 004585", "amt": 16250, "payableAmt": 16250, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 10, "clmBillLOS": 1}, {"id": 279581251, "for": "room rent", "date": "/Date(**********000+0530)/", "number": "1", "amt": 52250, "payableAmt": 52250, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 1, "clmBillLOS": 6}], "isClaimMovetoInvestigation": 0, "pd": "Left ventricular failure", "plt": "Conservative Management", "rcl": "Left ventricular failure", "settledAmt": 0, "treatmentName": "Conservative Management", "claimPriority": "CRITICAL", "clmSourceName": "Hospital Portal", "paExternalStatus": "Pre-Auth Cancelled", "paInsurerStatus": "Pre-Auth Cancelled", "clmOtherTreatmentDetails": "{\"HealthCareSystem\":\"Allopathy\",\"PrimaryApproach\":\"\",\"PrimaryPosition\":\"\",\"PrimaryAilmentDuration\":\"1\",\"SecondaryApproach\":null,\"SecondaryPosition\":null,\"SecondaryAilmentDuration\":null,\"TertiaryApproach\":null,\"TertiaryPosition\":null,\"TertiaryAilmentDuration\":null,\"EligibleNormalRoomRent\":9500,\"EligibleICURoomRent\":0,\"IsRuleConfigured\":true,\"IsEmergency\":false,\"IsDigitized\":true,\"IsRuleEngineDenied\":false}", "clmTotalCostofCare": 292521, "clmNetClaimedAmt": 292521, "clmHospDiscountOnBill": 25558}, {"id": *********, "recdate": "/Date(1738237222000+0530)/", "typeId": 0, "doa": "/Date(*************+0530)/", "dod": "/Date(*************+0530)/", "type": "Enhancement PA", "strAmount": "2,94,657", "strTotalBillAmt": "2,94,657", "strApprovedAmt": "34,868", "amount": 294657, "statusId": 7, "statusName": "Settled", "totalBillAmt": 0, "approvedAmt": 34868, "letterRemarks": "Rs.12500  deducted as 12.5% or maximum up to Rs.12500 copay applicable as per policy T &C. ROOM RENT INCLUSIVE OF NURSING BUT BILLED EXCESS KINDLY DO NOT COLLECT EXCESS OF NURSING TARIFF FROM PATIENT PARTY.\n", "infoReqDate": "/Date(-**************)/", "docRecDate": "/Date(-**************)/", "appDate": "/Date(-**************)/", "settlementDate": "/Date(-**************)/", "denialDate": "/Date(-**************)/", "lastAuditDate": "/Date(-**************)/", "regionId": 14, "faxMsgId": "NULL", "provisionalDiag": "Left ventricular failure", "budget": 294657, "isFinal": true, "isLessThanIntial": false, "check": false, "ailmentCode": "11181", "paDeductionDetails": [{"slNo": 62834233, "claimId": *********, "deductionId": 5, "deductionDesc": "Hospital Discount", "amount": 26231.0, "remarks": "Miscellaneous Charges: 1380.00,Investigation & Lab Charges: 7684.00,Consultant Charges: 7896.80,Pharmacy & Medicine Charges: 1386.04,Hospital Charges: 7885.00 on Rs. 249429"}, {"slNo": 62834234, "claimId": *********, "deductionId": 1, "deductionDesc": "Copay", "amount": 12501.0, "remarks": "Minimum capped value for copay applicable between 12.5%  of admissible amount 249428.16 and flat amount 12500 is 12500 (Mandatory Copay) on Rs. 249429"}], "paDenialClauses": [], "bills": [{"id": 279558316, "for": "admission charges", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 1500, "payableAmt": 0, "dedReasonDesc": "admission charges:-1500.00", "nonPayableAmt": 1500, "packageID": 0, "isDeleted": false, "ctype": 4, "clmBillLOS": 1}, {"id": 279558320, "for": "external durable appliances", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 626, "payableAmt": 0, "dedReasonDesc": "nebulizer kits,polymed,nebuliser mask, adult,pvc,20111,polymed:-626.00", "nonPayableAmt": 626, "packageID": 0, "isDeleted": false, "ctype": 14, "clmBillLOS": 1}, {"id": 279558321, "for": "haematology (blood count)", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 11100, "payableAmt": 11100, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 2, "clmBillLOS": 11}, {"id": 279558322, "for": "icu charges", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 22000, "payableAmt": 22000, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 1, "clmBillLOS": 2}, {"id": 279558323, "for": "icu consultant", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 5400, "payableAmt": 5400, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 10, "clmBillLOS": 3}, {"id": 279558324, "for": "icu monitoring charges", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 1600, "payableAmt": 1600, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 1, "clmBillLOS": 2}, {"id": 279558325, "for": "iv fluids / disposables", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 11706, "payableAmt": 7133, "dedReasonDesc": "electrocardiography ecg) ) 12 lead:-600.00,syringe disposable,lifelong blister pack,1246,50 ml without needle,luer slip,lifelong meditech ltd:-325.00,syringe disposable,lifelong blister pack,1827,2 ml with mounted needle,luer slip,lifelong meditech p:-96.00,ecg electrode, adult solid gel,msglt- 11g, medico electrodes international:-216.00,insulin syringe,40 iu,5112,,polymed:-60.00,electrode, adult solid gel,msglt- 11g,medic0 electrodes internationa:-180.00,syringe disposable,lifelong- blister pack,10 ml 1834,with mounted needle luer lock,lifelong meditech:-267.50,syringe disposable,lifelong- blister pack,1831,5 ml with mounted needle,luer slip,lifelong meditech :-165.00,pen needles,disp0 van,4mmx32g (0.23mm),,,universal fit,hmd:-450.00,syringe disposable,lifelong blister pack,10 ml ,1834,with mounted needle luer lock, lifelong meditec:-267.50,syringe disposable,lifelong- blister pack,1831,5 ml with mounted needle,luer slip, lifelong meditech:-165.00,syringe disposable,lifelong blister pack,10 ml ,1834,with mounted needle luer lock,lifelong meditech:-53.50,body wipes (pack of 10),glider aqua bath,240mm x300mm,,,,aicare industries pvt ltd:-530.00,syringe disposable,lifelong blister pack,50 ml,hithout needle luer lock,,lifelong meditech ltd:-332.00,needle sterile,hypodermic,26gx1 .5\",ss-6088,stainless steel,romsons:-3.60,syringe disposable,lifelong- blister pack,1833,10 ml with mounted needle,luer slip,lifelong meditech:-252.50,syringe disposable,lifelong blister pack,1831,5 ml with mounted needle,luer slip,lifelong meditech p:-99.00,bed pan with cover, (disposable) naulakha:-190.00,syringe disposable,lifelong- blister pack,10 ml ,1834, with mounted needle luer lock,lifelong medite:-160.50,urine pot naulakha:-160.00", "nonPayableAmt": 4573, "packageID": 0, "isDeleted": false, "ctype": 14, "clmBillLOS": 194}, {"id": 279558326, "for": "medicines/drugs", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 31927, "payableAmt": 31927, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 14, "clmBillLOS": 150}, {"id": 279558327, "for": "microbiology (culture & sensitivity)", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 7775, "payableAmt": 7775, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 2, "clmBillLOS": 6}, {"id": 279558329, "for": "nursing", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 3000, "payableAmt": 0, "dedReasonDesc": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.000:-600.000,Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00:-2400.00", "nonPayableAmt": 3000, "packageID": 0, "isDeleted": false, "ctype": 1, "clmBillLOS": 5}, {"id": 279558334, "for": "physiotherapy charges", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 8960, "payableAmt": 8960, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 10, "clmBillLOS": 8}, {"id": 279558317, "for": "bio chemistry (lft,rf,lipid)", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 36135, "payableAmt": 36135, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 2, "clmBillLOS": 39}, {"id": 279558318, "for": "consultation / visit", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 48800, "payableAmt": 48100, "dedReasonDesc": "dietician visit:-700.00", "nonPayableAmt": 700, "packageID": 0, "isDeleted": false, "ctype": 10, "clmBillLOS": 31}, {"id": 279558319, "for": "documentation charges", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 600, "payableAmt": 0, "dedReasonDesc": "documentation charges:-600.00", "nonPayableAmt": 600, "packageID": 0, "isDeleted": false, "ctype": 4, "clmBillLOS": 1}, {"id": 279558328, "for": "neurology (eeg,emg)", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 1800, "payableAmt": 1800, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 2, "clmBillLOS": 3}, {"id": 279558330, "for": "ot consumables", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 1940, "payableAmt": 1190, "dedReasonDesc": "room-common item charges ward/double/single:-750.00", "nonPayableAmt": 750, "packageID": 0, "isDeleted": false, "ctype": 14, "clmBillLOS": 6}, {"id": 279558331, "for": "other miscellaneous charges", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 6100, "payableAmt": 0, "dedReasonDesc": "cardio 3 panel,triage,,97400eu,tes tkit,alere medical:-3600.00,patient linen & laundry charges:-1400.00,tpa evaluation charges:-800.00,common item charges icu:-300.00", "nonPayableAmt": 6100, "packageID": 0, "isDeleted": false, "ctype": 4, "clmBillLOS": 11}, {"id": 279558332, "for": "pathology (hpe,biopsy)", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 1730, "payableAmt": 1730, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 2, "clmBillLOS": 1}, {"id": 279558333, "for": "patient / attendant food charges", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 5600, "payableAmt": 5600, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 4, "clmBillLOS": 7}, {"id": 279558335, "for": "procedures charges", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 15808, "payableAmt": 14660, "dedReasonDesc": "intravenous cannulation:-1148.00", "nonPayableAmt": 1148, "packageID": 0, "isDeleted": false, "ctype": 10, "clmBillLOS": 20}, {"id": 279558336, "for": "radiology (mri/ct/x-ray)", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 18300, "payableAmt": 18300, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 2, "clmBillLOS": 7}, {"id": 279558337, "for": "room rent", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 52250, "payableAmt": 52250, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 1, "clmBillLOS": 6}], "isClaimMOS": 0, "isClaimLCM": 0, "isClaimMovetoInvestigation": 0, "pd": "Left ventricular failure", "plt": "Conservative Management", "rcl": "Left ventricular failure", "settledAmt": 0, "treatmentName": "Conservative Management", "claimPriority": "", "clmSourceName": "Hospital Portal", "paExternalStatus": "Pre-Auth Processed", "paInsurerStatus": "Pre-Auth Processed", "clmOtherTreatmentDetails": "{\"HealthCareSystem\":\"Allopathy\",\"PrimaryApproach\":\"\",\"PrimaryPosition\":\"\",\"PrimaryAilmentDuration\":\"365\",\"SecondaryApproach\":null,\"SecondaryPosition\":null,\"SecondaryAilmentDuration\":\"365\",\"TertiaryApproach\":null,\"TertiaryPosition\":null,\"TertiaryAilmentDuration\":null,\"EligibleNormalRoomRent\":9500,\"EligibleICURoomRent\":0,\"IsRuleConfigured\":true,\"IsEmergency\":false,\"IsDigitized\":true,\"IsRuleEngineDenied\":false}", "clmTotalCostofCare": 294657, "clmNetClaimedAmt": 294657, "clmHospDiscountOnBill": 0}, {"id": *********, "recdate": "/Date(1738159757000+0530)/", "typeId": 0, "doa": "/Date(*************+0530)/", "dod": "/Date(*************+0530)/", "type": "Enhancement PA", "strAmount": "2,59,134", "strTotalBillAmt": "2,59,134", "strApprovedAmt": "63,131", "amount": 259134, "statusId": 7, "statusName": "Settled", "totalBillAmt": 0, "approvedAmt": 63131, "letterRemarks": "Rs. 12500/- deducted as 12.5% or maximum up to Rs.12500 copay applicable as per policy T & C. \n\nEnhancement approval given, kindly provide FB & DS at the time of discharge.  FB will be settled as per Agreed Tariff & policy T&C  ", "infoReqDate": "/Date(-**************)/", "docRecDate": "/Date(-**************)/", "appDate": "/Date(-**************)/", "settlementDate": "/Date(-**************)/", "denialDate": "/Date(-**************)/", "lastAuditDate": "/Date(-**************)/", "regionId": 14, "faxMsgId": "NULL", "provisionalDiag": "Left ventricular failure", "budget": 259134, "isFinal": false, "isLessThanIntial": false, "check": false, "ailmentCode": "11181", "paDeductionDetails": [{"slNo": 62800509, "claimId": *********, "deductionId": 5, "deductionDesc": "Hospital Discount", "amount": 22369.0, "remarks": "Consultant Charges: 5958.03,Investigation & Lab Charges: 6095.29,Miscellaneous Charges: 2779.84,Pharmacy & Medicine Charges: 2356.71,Hospital Charges: 5179.40 on Rs. 214560"}, {"slNo": 62800510, "claimId": *********, "deductionId": 1, "deductionDesc": "Copay", "amount": 12500.0, "remarks": "Minimum capped value for copay applicable between 12.5%  of admissible amount 214559.73 and flat amount 12500 is 12500 (Mandatory Copay) on Rs. 214560"}], "paDenialClauses": [], "bills": [{"id": 279302943, "for": "consultation / visit", "date": "/Date(1738089000000+0530)/", "number": "Not Available", "amt": 47600, "payableAmt": 47600, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 10, "clmBillLOS": 6}, {"id": 279309156, "for": "medicines/drugs", "date": "/Date(1738089000000+0530)/", "number": "Not Available", "amt": 27301, "payableAmt": 27301, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 14, "clmBillLOS": 2}, {"id": 279309157, "for": "room rent", "date": "/Date(1738089000000+0530)/", "number": "Not Available", "amt": 60000, "payableAmt": 57000, "dedReasonDesc": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 3000.000:-3000.000", "nonPayableAmt": 3000, "packageID": 0, "isDeleted": false, "ctype": 1, "clmBillLOS": 6}, {"id": 279309158, "for": "service charges", "date": "/Date(1738089000000+0530)/", "number": "Not Available", "amt": 12998, "payableAmt": 12998, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 4, "clmBillLOS": 1}, {"id": 279309160, "for": "physiotherapy charges", "date": "/Date(1738089000000+0530)/", "number": "Not Available", "amt": 6720, "payableAmt": 6720, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 10, "clmBillLOS": 1}, {"id": 279302944, "for": "bio chemistry (lft,rf,lipid)", "date": "/Date(1738089000000+0530)/", "number": "Not Available", "amt": 70610, "payableAmt": 70610, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 2, "clmBillLOS": 1}, {"id": 279309155, "for": "other miscellaneous charges", "date": "/Date(1738089000000+0530)/", "number": "Not Available", "amt": 19205, "payableAmt": 0, "dedReasonDesc": "consumable:-19204.60", "nonPayableAmt": 19205, "packageID": 0, "isDeleted": false, "ctype": 4, "clmBillLOS": 1}, {"id": 279309159, "for": "procedures charges", "date": "/Date(1738089000000+0530)/", "number": "Not Available", "amt": 14700, "payableAmt": 14700, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 10, "clmBillLOS": 1}], "isClaimMovetoInvestigation": 0, "pd": "Left ventricular failure", "plt": "", "rcl": "Left ventricular failure", "settledAmt": 0, "treatmentName": "Conservative Management", "claimPriority": "", "clmSourceName": "Hospital Portal", "paExternalStatus": "Pre-Auth Processed", "paInsurerStatus": "Pre-Auth Processed", "clmOtherTreatmentDetails": "{\"HealthCareSystem\":\"Allopathy\",\"PrimaryApproach\":\"\",\"PrimaryPosition\":\"\",\"PrimaryAilmentDuration\":\"365\",\"SecondaryApproach\":null,\"SecondaryPosition\":null,\"SecondaryAilmentDuration\":null,\"TertiaryApproach\":null,\"TertiaryPosition\":null,\"TertiaryAilmentDuration\":null,\"EligibleNormalRoomRent\":9500,\"EligibleICURoomRent\":0,\"IsRuleConfigured\":true,\"IsEmergency\":false,\"IsDigitized\":true,\"IsRuleEngineDenied\":false}", "clmTotalCostofCare": 259134, "clmNetClaimedAmt": 259134, "clmHospDiscountOnBill": 0}, {"id": *********, "recdate": "/Date(1738072872000+0530)/", "typeId": 0, "doa": "/Date(*************+0530)/", "dod": "/Date(*************+0530)/", "type": "Enhancement PA", "strAmount": "2,31,191", "strTotalBillAmt": "2,31,191", "strApprovedAmt": "7,225", "amount": 231191, "statusId": 7, "statusName": "Settled", "totalBillAmt": 0, "approvedAmt": 7225, "letterRemarks": "Kindly note that Rs. 12500 deducted as 12.5% or maximum up to Rs.12500 copay applicable as per policy T & C.", "infoReqDate": "/Date(-**************)/", "docRecDate": "/Date(-**************)/", "appDate": "/Date(-**************)/", "settlementDate": "/Date(-**************)/", "denialDate": "/Date(-**************)/", "lastAuditDate": "/Date(-**************)/", "regionId": 14, "faxMsgId": "NULL", "provisionalDiag": "Left ventricular failure", "budget": 231191, "isFinal": false, "isLessThanIntial": false, "check": false, "ailmentCode": "11181", "paDeductionDetails": [{"slNo": 62766385, "claimId": *********, "deductionId": 1, "deductionDesc": "Copay", "amount": 12501.0, "remarks": "Minimum capped value for copay applicable between 12.5%  of admissible amount 151429.19 and flat amount 12500 is 12500 (Mandatory Copay) on Rs. 151430"}, {"slNo": 62766384, "claimId": *********, "deductionId": 5, "deductionDesc": "Hospital Discount", "amount": 21295.0, "remarks": "Consultant Charges: 5673.00,Investigation & Lab Charges: 6808.00,Miscellaneous Charges: 2983.41,Pharmacy & Medicine Charges: 781.40,Hospital Charges: 5050.00 on Rs. 151430"}], "paDenialClauses": [], "bills": [{"id": 278993038, "for": "consultation / visit", "date": "/Date(1738002600000+0530)/", "number": "Not Available", "amt": 39300, "payableAmt": 39300, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 10, "clmBillLOS": 1}, {"id": 279014676, "for": "bio chemistry (lft,rf,lipid)", "date": "/Date(1738002600000+0530)/", "number": "Not Available", "amt": 68080, "payableAmt": 68080, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 2, "clmBillLOS": 1}, {"id": 279014678, "for": "medicines/drugs", "date": "/Date(1738002600000+0530)/", "number": "Not Available", "amt": 26047, "payableAmt": 26047, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 14, "clmBillLOS": 2}, {"id": 279014679, "for": "room rent", "date": "/Date(1738002600000+0530)/", "number": "Not Available", "amt": 50500, "payableAmt": 9500, "dedReasonDesc": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 41000.000:-41000.000", "nonPayableAmt": 41000, "packageID": 0, "isDeleted": false, "ctype": 1, "clmBillLOS": 1}, {"id": 279014681, "for": "procedures charges", "date": "/Date(1738002600000+0530)/", "number": "Not Available", "amt": 12950, "payableAmt": 12950, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 10, "clmBillLOS": 1}, {"id": 279014677, "for": "other miscellaneous charges", "date": "/Date(1738002600000+0530)/", "number": "Not Available", "amt": 17466, "payableAmt": 0, "dedReasonDesc": "consumable:-17466.10", "nonPayableAmt": 17466, "packageID": 0, "isDeleted": false, "ctype": 4, "clmBillLOS": 1}, {"id": 279014680, "for": "service charges", "date": "/Date(1738002600000+0530)/", "number": "Not Available", "amt": 12368, "payableAmt": 12368, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 4, "clmBillLOS": 1}, {"id": 279014682, "for": "physiotherapy charges", "date": "/Date(1738002600000+0530)/", "number": "Not Available", "amt": 4480, "payableAmt": 4480, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 10, "clmBillLOS": 1}], "isClaimMovetoInvestigation": 0, "pd": "Left ventricular failure", "plt": "", "rcl": "Left ventricular failure", "settledAmt": 0, "treatmentName": "Conservative Management", "claimPriority": "", "clmSourceName": "Hospital Portal", "paExternalStatus": "Pre-Auth Processed", "paInsurerStatus": "Pre-Auth Processed", "clmOtherTreatmentDetails": "{\"HealthCareSystem\":\"Allopathy\",\"PrimaryApproach\":\"\",\"PrimaryPosition\":\"\",\"PrimaryAilmentDuration\":\"365\",\"SecondaryApproach\":null,\"SecondaryPosition\":null,\"SecondaryAilmentDuration\":null,\"TertiaryApproach\":null,\"TertiaryPosition\":null,\"TertiaryAilmentDuration\":null,\"EligibleNormalRoomRent\":9500,\"EligibleICURoomRent\":0,\"IsRuleConfigured\":true,\"IsEmergency\":false,\"IsDigitized\":true,\"IsRuleEngineDenied\":false}", "clmTotalCostofCare": 231191, "clmNetClaimedAmt": 231191, "clmHospDiscountOnBill": 0}, {"id": *********, "recdate": "/Date(1737985632000+0530)/", "typeId": 0, "doa": "/Date(*************+0530)/", "dod": "/Date(*************+0530)/", "type": "Enhancement PA", "strAmount": "2,11,364", "strTotalBillAmt": "2,11,364", "strApprovedAmt": "32,989", "amount": 211364, "statusId": 7, "statusName": "Settled", "totalBillAmt": 0, "approvedAmt": 32989, "letterRemarks": "Enhancement approval given, kindly provide FB & DS at the time of discharge.  FB will be settled as per Agreed Tariff & policy T&C \n\nRs. 12500/- deducted as 12.5% or maximum up to Rs.12500 copay applicable as per policy T & C.", "infoReqDate": "/Date(-**************)/", "docRecDate": "/Date(-**************)/", "appDate": "/Date(-**************)/", "settlementDate": "/Date(-**************)/", "denialDate": "/Date(-**************)/", "lastAuditDate": "/Date(-**************)/", "regionId": 14, "faxMsgId": "NULL", "provisionalDiag": "Left ventricular failure", "budget": 211364, "isFinal": false, "isLessThanIntial": false, "check": false, "ailmentCode": "11181", "paDeductionDetails": [{"slNo": 62729856, "claimId": *********, "deductionId": 5, "deductionDesc": "Hospital Discount", "amount": 19468.0, "remarks": "Consultant Charges: 5349.00,Investigation & Lab Charges: 6449.00,Miscellaneous Charges: 2855.91,Pharmacy & Medicine Charges: 714.75,Hospital Charges: 4100.00 on Rs. 144205"}, {"slNo": 62729857, "claimId": *********, "deductionId": 1, "deductionDesc": "Copay", "amount": 12501.0, "remarks": "Minimum capped value for copay applicable between 12.5%  of admissible amount 144204.34 and flat amount 12500 is 12500 (Mandatory Copay) on Rs. 144205"}], "paDenialClauses": [], "bills": [{"id": 278712108, "for": "bio chemistry (lft,rf,lipid)", "date": "/Date(1737916200000+0530)/", "number": "Not Available", "amt": 64490, "payableAmt": 64490, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 2, "clmBillLOS": 1}, {"id": 278712110, "for": "medicines/drugs", "date": "/Date(1737916200000+0530)/", "number": "Not Available", "amt": 23825, "payableAmt": 23825, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 14, "clmBillLOS": 2}, {"id": 278712111, "for": "room rent", "date": "/Date(1737916200000+0530)/", "number": "Not Available", "amt": 41000, "payableAmt": 9500, "dedReasonDesc": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 31500.000:-31500.000", "nonPayableAmt": 31500, "packageID": 0, "isDeleted": false, "ctype": 1, "clmBillLOS": 1}, {"id": 278712112, "for": "service charges", "date": "/Date(1737916200000+0530)/", "number": "Not Available", "amt": 12368, "payableAmt": 12368, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 4, "clmBillLOS": 1}, {"id": 278712114, "for": "physiotherapy charges", "date": "/Date(1737916200000+0530)/", "number": "Not Available", "amt": 2240, "payableAmt": 2240, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 10, "clmBillLOS": 1}, {"id": 278694004, "for": "consultation / visit", "date": "/Date(1737916200000+0530)/", "number": "Not Available", "amt": 39300, "payableAmt": 39300, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 10, "clmBillLOS": 1}, {"id": 278712109, "for": "other miscellaneous charges", "date": "/Date(1737916200000+0530)/", "number": "Not Available", "amt": 16191, "payableAmt": 0, "dedReasonDesc": "consumable:-16191.10", "nonPayableAmt": 16191, "packageID": 0, "isDeleted": false, "ctype": 4, "clmBillLOS": 1}, {"id": 278712113, "for": "procedures charges", "date": "/Date(1737916200000+0530)/", "number": "Not Available", "amt": 11950, "payableAmt": 11950, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 10, "clmBillLOS": 1}], "isClaimMovetoInvestigation": 0, "pd": "Left ventricular failure", "plt": "", "rcl": "Left ventricular failure", "settledAmt": 0, "treatmentName": "Conservative Management", "claimPriority": "", "clmSourceName": "Hospital Portal", "paExternalStatus": "Pre-Auth Processed", "paInsurerStatus": "Pre-Auth Processed", "clmOtherTreatmentDetails": "{\"HealthCareSystem\":\"Allopathy\",\"PrimaryApproach\":\"\",\"PrimaryPosition\":\"\",\"PrimaryAilmentDuration\":\"365\",\"SecondaryApproach\":null,\"SecondaryPosition\":null,\"SecondaryAilmentDuration\":null,\"TertiaryApproach\":null,\"TertiaryPosition\":null,\"TertiaryAilmentDuration\":null,\"EligibleNormalRoomRent\":9500,\"EligibleICURoomRent\":0,\"IsRuleConfigured\":true,\"IsEmergency\":false,\"IsDigitized\":true,\"IsRuleEngineDenied\":false}", "clmTotalCostofCare": 211364, "clmNetClaimedAmt": 211364, "clmHospDiscountOnBill": 0}, {"id": *********, "recdate": "/Date(1737892633000+0530)/", "typeId": 0, "doa": "/Date(*************+0530)/", "dod": "/Date(*************+0530)/", "type": "Enhancement PA", "strAmount": "1,61,349", "strTotalBillAmt": "1,61,349", "strApprovedAmt": "40,147", "amount": 161349, "statusId": 7, "statusName": "Settled", "totalBillAmt": 0, "approvedAmt": 40147, "letterRemarks": "rs12,500/-deducted as 12.5% or Max upto 12500 copay is applicable as per policy T&C, final approval will be based on fb and ds and as per package and as per policy terms and conditions", "infoReqDate": "/Date(-**************)/", "docRecDate": "/Date(-**************)/", "appDate": "/Date(-**************)/", "settlementDate": "/Date(-**************)/", "denialDate": "/Date(-**************)/", "lastAuditDate": "/Date(-**************)/", "regionId": 14, "faxMsgId": "NULL", "provisionalDiag": "Left ventricular failure", "budget": 161349, "isFinal": false, "isLessThanIntial": false, "check": false, "ailmentCode": "11181", "paDeductionDetails": [{"slNo": 62693355, "claimId": *********, "deductionId": 1, "deductionDesc": "Copay", "amount": 12500.0, "remarks": "Minimum capped value for copay applicable between 12.5%  of admissible amount 111214.85 and flat amount 12500 is 12500 (Mandatory Copay) on Rs. 111215"}, {"slNo": 62693354, "claimId": *********, "deductionId": 5, "deductionDesc": "Hospital Discount", "amount": 14611.0, "remarks": "Consultant Charges: 3799.00,Investigation & Lab Charges: 5616.00,Miscellaneous Charges: 1393.10,Pharmacy & Medicine Charges: 653.05,Hospital Charges: 3150.00 on Rs. 111215"}], "paDenialClauses": [], "bills": [{"id": 278413109, "for": "medicines/drugs", "date": "/Date(1737829800000+0530)/", "number": "Not Available", "amt": 21768, "payableAmt": 21768, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 14, "clmBillLOS": 2}, {"id": 278413110, "for": "room rent", "date": "/Date(1737829800000+0530)/", "number": "Not Available", "amt": 31500, "payableAmt": 6500, "dedReasonDesc": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 25000.000:-25000.000", "nonPayableAmt": 25000, "packageID": 0, "isDeleted": false, "ctype": 1, "clmBillLOS": 1}, {"id": 278413112, "for": "procedures charges", "date": "/Date(1737829800000+0530)/", "number": "Not Available", "amt": 9450, "payableAmt": 9450, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 10, "clmBillLOS": 1}, {"id": 278413113, "for": "physiotherapy charges", "date": "/Date(1737829800000+0530)/", "number": "Not Available", "amt": 2240, "payableAmt": 2240, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 10, "clmBillLOS": 1}, {"id": 278410609, "for": "consultation / visit", "date": "/Date(1737829800000+0530)/", "number": "Not Available", "amt": 26300, "payableAmt": 26300, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 10, "clmBillLOS": 1}, {"id": 278413107, "for": "bio chemistry (lft,rf,lipid)", "date": "/Date(1737829800000+0530)/", "number": "Not Available", "amt": 56160, "payableAmt": 56160, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 2, "clmBillLOS": 1}, {"id": 278413108, "for": "other miscellaneous charges", "date": "/Date(1737829800000+0530)/", "number": "Not Available", "amt": 10523, "payableAmt": 0, "dedReasonDesc": "consumable:-10523.00", "nonPayableAmt": 10523, "packageID": 0, "isDeleted": false, "ctype": 4, "clmBillLOS": 1}, {"id": 278413111, "for": "service charges", "date": "/Date(1737829800000+0530)/", "number": "Not Available", "amt": 3408, "payableAmt": 3408, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 4, "clmBillLOS": 1}], "isClaimMovetoInvestigation": 0, "pd": "Left ventricular failure", "plt": "", "rcl": "Left ventricular failure", "settledAmt": 0, "treatmentName": "Conservative Management", "claimPriority": "", "clmSourceName": "Hospital Portal", "paExternalStatus": "Pre-Auth Processed", "paInsurerStatus": "Pre-Auth Processed", "clmOtherTreatmentDetails": "{\"HealthCareSystem\":\"Allopathy\",\"PrimaryApproach\":\"\",\"PrimaryPosition\":\"\",\"PrimaryAilmentDuration\":\"1\",\"SecondaryApproach\":null,\"SecondaryPosition\":null,\"SecondaryAilmentDuration\":null,\"TertiaryApproach\":null,\"TertiaryPosition\":null,\"TertiaryAilmentDuration\":null,\"EligibleNormalRoomRent\":9500,\"EligibleICURoomRent\":0,\"IsRuleConfigured\":true,\"IsEmergency\":false,\"IsDigitized\":true,\"IsRuleEngineDenied\":false}", "clmTotalCostofCare": 161349, "clmNetClaimedAmt": 161349, "clmHospDiscountOnBill": 0}, {"id": *********, "recdate": "/Date(1737725574000+0530)/", "typeId": 0, "doa": "/Date(*************+0530)/", "dod": "/Date(*************+0530)/", "type": "Initial PA", "strAmount": "1,21,700", "strTotalBillAmt": "1,21,700", "strApprovedAmt": "58,568", "amount": 121700, "statusId": 7, "statusName": "Settled", "totalBillAmt": 0, "approvedAmt": 58568, "letterRemarks": "Rs.¬8367_ deducted as 12.5% or maximum up to Rs.12500 copay applicable as per policy T & C.\nconditional approval given finally approval will be based on FB & DS with detailed IV medications , with reports, ICPs notes for further enhancement", "infoReqDate": "/Date(-**************)/", "docRecDate": "/Date(-**************)/", "appDate": "/Date(-**************)/", "settlementDate": "/Date(-**************)/", "denialDate": "/Date(-**************)/", "lastAuditDate": "/Date(-**************)/", "regionId": 14, "emailId": "0", "faxMsgId": "513", "provisionalDiag": "Other medical line of Treatment", "budget": 121700, "isFinal": false, "isLessThanIntial": false, "check": false, "ailmentCode": "11181", "paDeductionDetails": [{"slNo": 62645040, "claimId": *********, "deductionId": 1, "deductionDesc": "Copay", "amount": 8367.0, "remarks": "Minimum capped value for copay applicable between 12.5%  of admissible amount 66935.00 and flat amount 12500 is 8366.875 (Mandatory Copay) on Rs. 66935"}, {"slNo": 62645039, "claimId": *********, "deductionId": 5, "deductionDesc": "Hospital Discount", "amount": 12170.0, "remarks": "Package: 12170.00 on Rs. 66935"}], "paDenialClauses": [], "bills": [{"id": 278079215, "for": "package", "date": "/Date(1737595800000+0530)/", "number": "278079215", "amt": 121700, "payableAmt": 79105, "dedReasonDesc": " Estimated Approval", "nonPayableAmt": 42595, "packageID": 0, "isDeleted": false, "ctype": 15, "clmBillLOS": 1}], "pd": "Other medical line of Treatment", "rcl": "Other medical line of Treatment", "rentPerDay": 0, "stayDays": 0, "treatmentName": "Conservative Management", "claimPriority": "", "clmSourceName": "IHX", "paExternalStatus": "Pre-Auth Processed", "paInsurerStatus": "Pre-Auth Processed", "clmOtherTreatmentDetails": "{\"HealthCareSystem\":\"Allopathy\",\"PrimaryApproach\":\"\",\"PrimaryPosition\":\"\",\"PrimaryAilmentDuration\":\"365\",\"SecondaryApproach\":null,\"SecondaryPosition\":null,\"SecondaryAilmentDuration\":null,\"TertiaryApproach\":null,\"TertiaryPosition\":null,\"TertiaryAilmentDuration\":null,\"EligibleNormalRoomRent\":9500,\"EligibleICURoomRent\":0,\"IsRuleConfigured\":true,\"IsEmergency\":false,\"IsDigitized\":true,\"IsRuleEngineDenied\":false}", "clmTotalCostofCare": 121700, "clmNetClaimedAmt": 121700, "clmHospDiscountOnBill": 0}], "billSummary": [{"id": 279586049, "typeDesc": "Hospital Charges", "for": "icu charges", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 22000, "payableAmt": 22000, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 1, "clmBillLOS": 2}, {"id": 279586052, "typeDesc": "Hospital Charges", "for": "room rent", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 52250, "payableAmt": 52250, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 1, "clmBillLOS": 6}, {"id": 279586055, "typeDesc": "Investigation & Lab Charges", "for": "microbiology (culture & sensitivity)", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 7775, "payableAmt": 7775, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 2, "clmBillLOS": 6}, {"id": 279586056, "typeDesc": "Investigation & Lab Charges", "for": "neurology (eeg,emg)", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 1800, "payableAmt": 1800, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 2, "clmBillLOS": 3}, {"id": 279586057, "typeDesc": "Investigation & Lab Charges", "for": "pathology (hpe,biopsy)", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 1730, "payableAmt": 1730, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 2, "clmBillLOS": 1}, {"id": 279586060, "typeDesc": "Miscellaneous Charges", "for": "documentation charges", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 600, "payableAmt": 0, "dedReasonDesc": "documentation charges:-600.00", "nonPayableAmt": 600, "packageID": 0, "isDeleted": false, "ctype": 4, "clmBillLOS": 1}, {"id": 279586061, "typeDesc": "Miscellaneous Charges", "for": "other miscellaneous charges", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 6100, "payableAmt": 0, "dedReasonDesc": "cardio 3 panel,triage,,97400eu,tes tkit,alere medical:-3600.00,patient linen & laundry charges:-1400.00,tpa evaluation charges:-800.00,common item charges icu:-300.00", "nonPayableAmt": 6100, "packageID": 0, "isDeleted": false, "ctype": 4, "clmBillLOS": 11}, {"id": 279586062, "typeDesc": "Miscellaneous Charges", "for": "patient / attendant food charges", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 5600, "payableAmt": 5600, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 4, "clmBillLOS": 7}, {"id": 279586063, "typeDesc": "Consultant Charges", "for": "consultation / visit", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 48800, "payableAmt": 48100, "dedReasonDesc": "dietician visit:-700.00", "nonPayableAmt": 700, "packageID": 0, "isDeleted": false, "ctype": 10, "clmBillLOS": 31}, {"id": 279586064, "typeDesc": "Consultant Charges", "for": "icu consultant", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 5400, "payableAmt": 5400, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 10, "clmBillLOS": 3}, {"id": 279586065, "typeDesc": "Consultant Charges", "for": "physiotherapy charges", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 8960, "payableAmt": 8960, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 10, "clmBillLOS": 8}, {"id": 279586066, "typeDesc": "Consultant Charges", "for": "procedures charges", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 15808, "payableAmt": 14660, "dedReasonDesc": "intravenous cannulation:-1148.00", "nonPayableAmt": 1148, "packageID": 0, "isDeleted": false, "ctype": 10, "clmBillLOS": 20}, {"id": 279586067, "typeDesc": "Pharmacy & Medicine Charges", "for": "external durable appliances", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 626, "payableAmt": 0, "dedReasonDesc": "nebulizer kits,polymed,nebuliser mask, adult,pvc,20111,polymed:-626.00", "nonPayableAmt": 626, "packageID": 0, "isDeleted": false, "ctype": 14, "clmBillLOS": 1}, {"id": 279586069, "typeDesc": "Pharmacy & Medicine Charges", "for": "medicines/drugs", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 31927, "payableAmt": 31927, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 14, "clmBillLOS": 150}, {"id": 279586070, "typeDesc": "Pharmacy & Medicine Charges", "for": "ot consumables", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 1941, "payableAmt": 1191, "dedReasonDesc": "room-common item charges ward/double/single:-750.00", "nonPayableAmt": 750, "packageID": 0, "isDeleted": false, "ctype": 14, "clmBillLOS": 6}, {"id": 279586050, "typeDesc": "Hospital Charges", "for": "icu monitoring charges", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 1600, "payableAmt": 1600, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 1, "clmBillLOS": 2}, {"id": 279586051, "typeDesc": "Hospital Charges", "for": "nursing", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 3000, "payableAmt": 0, "dedReasonDesc": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.000:-600.000,Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00:-2400.00", "nonPayableAmt": 3000, "packageID": 0, "isDeleted": false, "ctype": 1, "clmBillLOS": 5}, {"id": 279586053, "typeDesc": "Investigation & Lab Charges", "for": "bio chemistry (lft,rf,lipid)", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 36135, "payableAmt": 36135, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 2, "clmBillLOS": 39}, {"id": 279586054, "typeDesc": "Investigation & Lab Charges", "for": "haematology (blood count)", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 11100, "payableAmt": 11100, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 2, "clmBillLOS": 11}, {"id": 279586058, "typeDesc": "Investigation & Lab Charges", "for": "radiology (mri/ct/x-ray)", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 18300, "payableAmt": 18300, "dedReasonDesc": "", "nonPayableAmt": 0, "packageID": 0, "isDeleted": false, "ctype": 2, "clmBillLOS": 7}, {"id": 279586059, "typeDesc": "Miscellaneous Charges", "for": "admission charges", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 1500, "payableAmt": 0, "dedReasonDesc": "admission charges:-1500.00", "nonPayableAmt": 1500, "packageID": 0, "isDeleted": false, "ctype": 4, "clmBillLOS": 1}, {"id": 279586068, "typeDesc": "Pharmacy & Medicine Charges", "for": "iv fluids / disposables", "date": "/Date(**********000+0530)/", "number": "Not Available", "amt": 11706, "payableAmt": 9830, "dedReasonDesc": "electrocardiography ecg) ) 12 lead:-600.00,ecg electrode, adult solid gel,msglt- 11g, medico electrodes international:-216.00,electrode, adult solid gel,msglt- 11g,medic0 electrodes internationa:-180.00,body wipes (pack of 10),glider aqua bath,240mm x300mm,,,,aicare industries pvt ltd:-530.00,bed pan with cover, (disposable) naulakha:-190.00,urine pot naulakha:-160.00", "nonPayableAmt": 1876, "packageID": 0, "isDeleted": false, "ctype": 14, "clmBillLOS": 194}], "deductionSummary": [{"slNo": 63706882, "claimId": *********, "deductionId": 6, "deductionDesc": "Paid by the Patient", "amount": 0.0, "remarks": ""}, {"slNo": 63706883, "claimId": *********, "deductionId": 10, "deductionDesc": "Intimation Deductible", "amount": 0.0, "remarks": ""}, {"slNo": 63706822, "claimId": *********, "deductionId": 5, "deductionDesc": "Hospital Discount", "amount": 25558.0, "remarks": "Hospital discount\t"}, {"slNo": 63706823, "claimId": *********, "deductionId": 1, "deductionDesc": "Copay", "amount": 0.0, "remarks": ""}, {"slNo": 63706824, "claimId": *********, "deductionId": 1, "deductionDesc": "Copay", "amount": 12500.0, "remarks": "Co-pay\t"}, {"slNo": 63706880, "claimId": *********, "deductionId": 2, "deductionDesc": "Excess of Defined / Ailment Limit", "amount": 0.0, "remarks": ""}, {"slNo": 63706881, "claimId": *********, "deductionId": 9, "deductionDesc": "Deductible Amount", "amount": 0.0, "remarks": ""}, {"slNo": 63706884, "claimId": *********, "deductionId": 11, "deductionDesc": "Usual Customary Rates", "amount": 0.0, "remarks": ""}, {"slNo": 63706885, "claimId": *********, "deductionId": 3, "deductionDesc": "Policy Excess / Deductible", "amount": 3372.0, "remarks": "Policy Excess \t"}], "paDeductionSummary": [{"slNo": 62833607, "claimId": *********, "deductionId": 5, "deductionDesc": "Hospital Discount", "amount": 25558.0, "remarks": "HOSPITAL DISCOUNT"}, {"slNo": 62833608, "claimId": *********, "deductionId": 1, "deductionDesc": "Copay", "amount": 12500.0, "remarks": "MANDATORY CO PAY"}, {"slNo": 62833649, "claimId": *********, "deductionId": 2, "deductionDesc": "Excess of Defined / Ailment Limit", "amount": 0.0, "remarks": ""}, {"slNo": 62833650, "claimId": *********, "deductionId": 9, "deductionDesc": "Deductible Amount", "amount": 0.0, "remarks": ""}, {"slNo": 62833651, "claimId": *********, "deductionId": 6, "deductionDesc": "Paid by the Patient", "amount": 0.0, "remarks": ""}, {"slNo": 62833652, "claimId": *********, "deductionId": 10, "deductionDesc": "Intimation Deductible", "amount": 0.0, "remarks": ""}, {"slNo": 62833653, "claimId": *********, "deductionId": 11, "deductionDesc": "Usual Customary Rates", "amount": 0.0, "remarks": ""}, {"slNo": 62833654, "claimId": *********, "deductionId": 3, "deductionDesc": "Policy Excess / Deductible", "amount": 0.0, "remarks": ""}, {"slNo": 62834233, "claimId": *********, "deductionId": 5, "deductionDesc": "Hospital Discount", "amount": 26231.0, "remarks": "Miscellaneous Charges: 1380.00,Investigation & Lab Charges: 7684.00,Consultant Charges: 7896.80,Pharmacy & Medicine Charges: 1386.04,Hospital Charges: 7885.00 on Rs. 249429"}, {"slNo": 62834234, "claimId": *********, "deductionId": 1, "deductionDesc": "Copay", "amount": 12501.0, "remarks": "Minimum capped value for copay applicable between 12.5%  of admissible amount 249428.16 and flat amount 12500 is 12500 (Mandatory Copay) on Rs. 249429"}, {"slNo": 62800509, "claimId": *********, "deductionId": 5, "deductionDesc": "Hospital Discount", "amount": 22369.0, "remarks": "Consultant Charges: 5958.03,Investigation & Lab Charges: 6095.29,Miscellaneous Charges: 2779.84,Pharmacy & Medicine Charges: 2356.71,Hospital Charges: 5179.40 on Rs. 214560"}, {"slNo": 62800510, "claimId": *********, "deductionId": 1, "deductionDesc": "Copay", "amount": 12500.0, "remarks": "Minimum capped value for copay applicable between 12.5%  of admissible amount 214559.73 and flat amount 12500 is 12500 (Mandatory Copay) on Rs. 214560"}, {"slNo": 62766385, "claimId": *********, "deductionId": 1, "deductionDesc": "Copay", "amount": 12501.0, "remarks": "Minimum capped value for copay applicable between 12.5%  of admissible amount 151429.19 and flat amount 12500 is 12500 (Mandatory Copay) on Rs. 151430"}, {"slNo": 62766384, "claimId": *********, "deductionId": 5, "deductionDesc": "Hospital Discount", "amount": 21295.0, "remarks": "Consultant Charges: 5673.00,Investigation & Lab Charges: 6808.00,Miscellaneous Charges: 2983.41,Pharmacy & Medicine Charges: 781.40,Hospital Charges: 5050.00 on Rs. 151430"}, {"slNo": 62729856, "claimId": *********, "deductionId": 5, "deductionDesc": "Hospital Discount", "amount": 19468.0, "remarks": "Consultant Charges: 5349.00,Investigation & Lab Charges: 6449.00,Miscellaneous Charges: 2855.91,Pharmacy & Medicine Charges: 714.75,Hospital Charges: 4100.00 on Rs. 144205"}, {"slNo": 62729857, "claimId": *********, "deductionId": 1, "deductionDesc": "Copay", "amount": 12501.0, "remarks": "Minimum capped value for copay applicable between 12.5%  of admissible amount 144204.34 and flat amount 12500 is 12500 (Mandatory Copay) on Rs. 144205"}, {"slNo": 62693355, "claimId": *********, "deductionId": 1, "deductionDesc": "Copay", "amount": 12500.0, "remarks": "Minimum capped value for copay applicable between 12.5%  of admissible amount 111214.85 and flat amount 12500 is 12500 (Mandatory Copay) on Rs. 111215"}, {"slNo": 62693354, "claimId": *********, "deductionId": 5, "deductionDesc": "Hospital Discount", "amount": 14611.0, "remarks": "Consultant Charges: 3799.00,Investigation & Lab Charges: 5616.00,Miscellaneous Charges: 1393.10,Pharmacy & Medicine Charges: 653.05,Hospital Charges: 3150.00 on Rs. 111215"}, {"slNo": 62645040, "claimId": *********, "deductionId": 1, "deductionDesc": "Copay", "amount": 8367.0, "remarks": "Minimum capped value for copay applicable between 12.5%  of admissible amount 66935.00 and flat amount 12500 is 8366.875 (Mandatory Copay) on Rs. 66935"}, {"slNo": 62645039, "claimId": *********, "deductionId": 5, "deductionDesc": "Hospital Discount", "amount": 12170.0, "remarks": "Package: 12170.00 on Rs. 66935"}], "priBenefDtls": {"empCode": "1100745", "name": "<PERSON><PERSON><PERSON>", "floaterSum": 1900000, "totalPremium": 0, "totalAmtSpent": 49847, "totalAmtAuthorised": 0, "balanceSI": 1850153, "bufferAmtTotal": 0, "bufferAmtAnnual": 0, "bufferAmtIncident": 0, "bufferAmtAuthorized": 0, "paBuffAmt": 0, "bufferAmtSpent": 0, "isVRS": false, "domLimit": 0, "isSIProtected": false, "priBenefID": 56870402}, "claimInfo": {"domiDenialReason": "approved", "isClaimLocked": false, "isTechError": false, "isSuspected": false, "isInwarded": true, "eventId": *********, "insurerClmRefNo": "TP00392000024902992137", "infoReqSt1Date": "/Date(-**************)/", "infoReqSt2Date": "/Date(-**************)/", "docRecDate": "/Date(-**************+0553)/", "appDate": "/Date(-**************)/", "settlementDate": "/Date(1740940200000+0530)/", "actualReceivedDate": "/Date(1737725574000+0530)/", "denialDate": "/Date(-**************)/", "closedDate": "/Date(-**************)/", "clsDate": "/Date(-**************)/", "netPayAmount": "2,13,235", "taxAmount": "23,693", "chequeAmount": "2,13,235", "serviceTaxAmt": "0", "admissibleAmt": "0", "approverRemarks": " Left ventricular failure", "id": *********, "typeDesc": "Cashless", "type": 3, "statusName": "Settled", "statusId": 7, "receivedDate": "/Date(1737725574000+0530)/", "preAuths": "*********, *********, *********, *********, *********, *********, *********", "compRefNo": "11477261", "irDate": "/Date(-**************)/", "settledDate": "/Date(-**************)/", "latestAuditDate": "/Date(-**************)/", "doa": "/Date(*************+0530)/", "dod": "/Date(*************+0530)/", "doctorName": "<PERSON><PERSON> Dr. <PERSON>. <PERSON>          ", "doctorRegno": "", "lengthofStay": 8, "amount": 294658, "approvedAmt": 236928, "paApprovedAmt": "2,41,543", "varianceAmt": "₹2,13,235", "contactNumber": "99******07", "unMaskContactNumber": "**********", "mainNumber": *********, "claimRegionId": 14, "reciptdate": "01-Jan-0001", "epd": 0, "roomCategory": "Single private room", "accountVerified": "Account verified", "inwardNo": ",XAP*********", "lastDocRecdate": "/Date(*************+0530)/", "lastScanDocRecdate": "/Date(*************+0530)/", "latestPreAuthStatusId": 0, "roomCategoryId": 26, "isClaimLCM": 0, "clmSourceName": "Hospital Portal", "claimMainClaim": 0, "claimExternalStatus": "<PERSON><PERSON><PERSON>", "claimInsurerStatus": "<PERSON><PERSON><PERSON>", "clmOtherTreatmentDetails": "{\"HealthCareSystem\":\"Allopathy\",\"PrimaryApproach\":\"\",\"PrimaryPosition\":\"\",\"PrimaryAilmentDuration\":\"8\",\"SecondaryApproach\":null,\"SecondaryPosition\":null,\"SecondaryAilmentDuration\":null,\"TertiaryApproach\":null,\"TertiaryPosition\":null,\"TertiaryAilmentDuration\":null,\"EligibleNormalRoomRent\":9500,\"EligibleICURoomRent\":0,\"IsRuleConfigured\":true,\"IsEmergency\":false,\"IsDigitized\":true}", "clmEligibleRoomCategory": 3, "eligibleRoomType": "Single Ward ( Private / Special / Executive Ward)", "clmTotalCostofCare": 294658, "clmNetClaimedAmt": 294658, "clmHospDiscountOnBill": 0, "chequeAmount_Claim": "", "chequeAmount_Interest": ""}, "ailmentSmry": {"code": "I50.1", "group": "", "treatmentName": "Conservative Management", "descs": "Left ventricular failure", "summary": ".Left ventricular failure", "ids": "11181", "ailmentBodySystem": "Cardiovascular System"}, "hospHeader": {"id": 95748, "name": "Fortis Hospital", "cityId": 0, "city": "New Delhi", "stateId": 0, "state": "Delhi", "pincode": "110088", "isPPN": "GIPSA", "typeOfCare": "Tertiary", "hospTariffURL": "0", "hospGeneralTariffURL": "0", "hospisVerified": true}, "benefDtls": {"policyHeader": {"polId": 62633282, "polCorpId": 1006639, "groupCorpId": 1049400, "polNo": "92000034240400000025", "polDevelopmentOfficer": "", "polDevelopmentAgent": "", "polInsCompanyID": 1, "polTypeID": 1, "polSubTypeID": 100, "polInsCompanyName": "The New India Assurance Co. Ltd", "polStartDate": "/Date(1711909800000+0530)/", "polEndDate": "/Date(1743359400000+0530)/", "polHldrName": "Tata Consultancy Services Ltd", "isFloater": false, "isRetail": false, "isMixed": false, "isActive": false, "polFloaterType": "Non Floater", "polTPAId": 93, "polSumInsured": "0", "polPaymentDirect": false, "polBlackListed": false, "maServicingBranchId": "2", "polTotalPremium": 0, "isPartialPending": false, "polRenewalRating": 0, "polCategoryId": 0, "polDeleteStatus": false, "tpaName": "Medi Assist Insurance TPA Pvt. Ltd.", "policySequence": 0, "isSuccess": false}, "mediassistId": 4009955823, "active": true, "name": "<PERSON><PERSON>", "contactNo": "99******07", "unMaskContactNo": "**********", "sex": "M", "dob": "/Date(-************+0530)/", "age": 84, "relId": 5, "relName": "Father", "occId": 0, "cumBonusPer": 0, "domiLimit": 12000, "premium": 0, "balanceSI": 49279, "sumInsured": 300000, "bsi": 49279, "totalSumInsured": 0, "amtSpent": 250721, "amtAuthorised": 0, "wef": "01-Apr-2024", "areaCode": "New Delhi", "email": "vi**********@tcs.com", "addedOn": "/Date(-**************)/", "addedBy": 0, "niaPersonID": "MEMBER412857", "modifiedUser": 0, "modifiedOn": "/Date(-**************)/", "benefUserId": 128239305, "unmaskedEmail": "<EMAIL>"}, "claimextensiondetails": {"mcE_ID": 27996851, "mcE_ClaimNo": *********, "mcE_BSIExhausted": false, "mcE_CopayApplicable": false, "mcE_CREATEDBY": 0, "mcE_CREATEDON": "/Date(1737759773000+0530)/", "mcE_MODIFIEDBY": 0, "mcE_MODIFIEDON": "/Date(1740785139000+0530)/", "mcE_ClmIsDeathCase": false, "isTarrifMismatch": 0, "mcE_ClaimNME": 0, "claimPriority": "", "mcE_CorporateContribution": 0, "mcE_FloodReliefClaim": 0, "mcE_ClmApprovedAmtBucket1": 0, "mcE_ClmApprovedAmtBucket2": 0, "mcE_ClmApprovedAmtBucket3": 0, "mcE_ClmApprovedAmtBucket4": 0, "claimAssignedToFlag": false, "claimIsExternalInv": false, "isFraudClaim": false, "isSuspectDone": false, "mcE_PremiumCollected": 0}, "regionName": "TCS", "roOfficeId": 0, "doOfficeId": 0, "boOfficeId": 0, "missingDocuments": [], "isSMSSent": 0, "isPostProcessed": 0, "lastSeenAuditTrailId": 0, "latestPAStatusId": 7, "latestPAInternalStatus": "Settled", "totalPayableAmount": "2,78,358", "totalNonPayableAmount": "16,300", "isPACheckDone": false, "dbType": "MA", "isMongoOnBoardReq": false, "documentDetails": [], "secAilmentSmry": {}, "terAilmentSmry": {}, "denialClauses": []}], "totalClaimCount": 0, "isSuccess": true, "_claim_details_metadata": {"fields_removed": ["canMoveToLC", "accountInfo", "paymentDtls", "allPaymentDtls", "claimIntimationDetails"], "preprocessing_applied": ["Removed canMoveToLC, accountInfo, paymentDtls, allPaymentDtls, claimIntimationDetails", "Removed canMoveToLC from relatedPreAuths entries"], "preprocessing_timestamp": "2025-06-13T21:54:02.940565", "purpose": "Remove unnecessary fields from claim details for cleaner processing"}}}