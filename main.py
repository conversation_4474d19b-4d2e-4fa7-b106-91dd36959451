from fastapi import <PERSON><PERSON><PERSON>, HTTPException, UploadFile, File, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON><PERSON>esponse
from fastapi.staticfiles import StaticFiles
import json
import logging
from typing import Dict, Any, List
import uvicorn

from models import QuestionRequest, QuestionResponse, ClaimsResponse, FetchClaimsRequest
from data_processor import ClaimsDataProcessor
from vector_store import VectorStore
from qa_engine import QAEngine
from proclaim_client import ProclaimAPIClient
from config import settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Claims Data Q&A API",
    description="API for processing insurance claims data and answering questions using LLM",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Initialize components
data_processor = ClaimsDataProcessor()
vector_store = VectorStore()
qa_engine = QAEngine()
proclaim_client = ProclaimAPIClient()

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Claims Data Q&A API",
        "version": "1.0.0",
        "endpoints": {
            "POST /upload-claims": "Upload and process claims data",
            "POST /fetch-and-process": "Fetch claims from API and process",
            "POST /ask": "Ask questions about claims data",
            "GET /claim/{claim_no}/summary": "Get claim summary",
            "GET /claim/{claim_no}/questions": "Get suggested questions for a claim",
            "GET /stats": "Get database statistics",
            "GET /proclaim-status": "Check Proclaim API status",
            "DELETE /claim/{claim_no}": "Delete claim data"
        }
    }

@app.post("/upload-claims")
async def upload_claims(file: UploadFile = File(...)):
    """Upload and process claims data from JSON file"""
    try:
        # Validate file type
        if not file.filename.endswith('.json'):
            raise HTTPException(status_code=400, detail="Only JSON files are supported")
        
        # Read and parse JSON
        content = await file.read()
        try:
            claims_data = json.loads(content)
        except json.JSONDecodeError as e:
            raise HTTPException(status_code=400, detail=f"Invalid JSON format: {str(e)}")
        
        # Process the claims data
        logger.info("Processing claims data...")
        processed_chunks = data_processor.process_claims_data(claims_data)
        
        if not processed_chunks:
            raise HTTPException(status_code=400, detail="No valid data found in the uploaded file")
        
        # Store in vector database
        logger.info(f"Storing {len(processed_chunks)} chunks in vector database...")
        success = vector_store.add_chunks(processed_chunks)
        
        if not success:
            raise HTTPException(status_code=500, detail="Failed to store data in vector database")
        
        # Analyze processed data
        claim_numbers = list(set(chunk.claim_no for chunk in processed_chunks))
        chunk_types = {}
        for chunk in processed_chunks:
            chunk_types[chunk.chunk_type] = chunk_types.get(chunk.chunk_type, 0) + 1
        
        return {
            "message": "Claims data processed and stored successfully",
            "processed_chunks": len(processed_chunks),
            "claims_processed": claim_numbers,
            "chunk_types": chunk_types
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing claims data: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.post("/upload-claims-json")
async def upload_claims_json(claims_data: Union[Dict[str, Any], List[Dict[str, Any]]]):
    """Upload and process claims data from JSON payload"""
    try:
        # Process the claims data
        logger.info("Processing claims data from JSON payload...")
        processed_chunks = data_processor.process_claims_data(claims_data)
        
        if not processed_chunks:
            raise HTTPException(status_code=400, detail="No valid data found in the payload")
        
        # Store in vector database
        logger.info(f"Storing {len(processed_chunks)} chunks in vector database...")
        success = vector_store.add_chunks(processed_chunks)
        
        if not success:
            raise HTTPException(status_code=500, detail="Failed to store data in vector database")
        
        # Analyze processed data
        claim_numbers = list(set(chunk.claim_no for chunk in processed_chunks))
        chunk_types = {}
        for chunk in processed_chunks:
            chunk_types[chunk.chunk_type] = chunk_types.get(chunk.chunk_type, 0) + 1
        
        return {
            "message": "Claims data processed and stored successfully",
            "processed_chunks": len(processed_chunks),
            "claims_processed": claim_numbers,
            "chunk_types": chunk_types
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing claims data: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.post("/fetch-and-process")
async def fetch_and_process_claims(request: FetchClaimsRequest):
    """Fetch claims data from Proclaim API and process it"""
    try:
        claim_numbers = request.claim_numbers
        if not claim_numbers:
            raise HTTPException(status_code=400, detail="At least one claim number is required")

        logger.info(f"Fetching claims data for: {claim_numbers}")

        # Validate Proclaim API configuration
        if not proclaim_client.validate_configuration():
            raise HTTPException(status_code=500, detail="Proclaim API is not properly configured")

        # Fetch data from Proclaim API
        claims_data = proclaim_client.fetch_claims_data(
            claim_numbers,
            login_name=request.login_name,
            login_email=request.login_email,
            login_user_id=request.login_user_id
        )

        if not claims_data:
            raise HTTPException(status_code=500, detail="Failed to fetch data from Proclaim API")

        # Process the claims data
        logger.info("Processing fetched claims data...")
        processed_chunks = data_processor.process_claims_data(claims_data)

        if not processed_chunks:
            raise HTTPException(status_code=400, detail="No valid data found in the fetched response")

        # Store in vector database
        logger.info(f"Storing {len(processed_chunks)} chunks in vector database...")
        success = vector_store.add_chunks(processed_chunks)

        if not success:
            raise HTTPException(status_code=500, detail="Failed to store data in vector database")

        # Analyze processed data
        processed_claim_numbers = list(set(chunk.claim_no for chunk in processed_chunks))
        chunk_types = {}
        for chunk in processed_chunks:
            chunk_types[chunk.chunk_type] = chunk_types.get(chunk.chunk_type, 0) + 1

        return {
            "message": "Claims data fetched and processed successfully",
            "requested_claims": claim_numbers,
            "processed_claims": processed_claim_numbers,
            "processed_chunks": len(processed_chunks),
            "chunk_types": chunk_types
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching and processing claims data: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/proclaim-status")
async def get_proclaim_status():
    """Get Proclaim API status and configuration"""
    try:
        api_info = proclaim_client.get_api_info()

        # Test connection if configuration is valid
        connection_test = False
        if api_info["configuration_valid"]:
            connection_test = proclaim_client.test_connection()

        return {
            "api_info": api_info,
            "connection_test_passed": connection_test,
            "status": "ready" if connection_test else "not_ready"
        }

    except Exception as e:
        logger.error(f"Error checking Proclaim status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.post("/ask", response_model=QuestionResponse)
async def ask_question(request: QuestionRequest):
    """Ask a question about the claims data"""
    try:
        if not request.question.strip():
            raise HTTPException(status_code=400, detail="Question cannot be empty")
        
        logger.info(f"Processing question: {request.question}")
        
        # Get answer from QA engine
        result = qa_engine.answer_question(
            question=request.question,
            claim_no=request.claim_no,
            max_results=request.max_results
        )
        
        return QuestionResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing question: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/claim/{claim_no}/summary")
async def get_claim_summary(claim_no: int):
    """Get a comprehensive summary of a specific claim"""
    try:
        logger.info(f"Generating summary for claim {claim_no}")
        
        summary = qa_engine.get_claim_summary(claim_no)
        return summary
        
    except Exception as e:
        logger.error(f"Error generating claim summary: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/claim/{claim_no}/questions")
async def get_suggested_questions(claim_no: int):
    """Get suggested questions for a specific claim"""
    try:
        logger.info(f"Getting suggested questions for claim {claim_no}")
        
        questions = qa_engine.suggest_questions(claim_no)
        return {
            "claim_no": claim_no,
            "suggested_questions": questions
        }
        
    except Exception as e:
        logger.error(f"Error getting suggested questions: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/questions")
async def get_general_questions():
    """Get general suggested questions"""
    try:
        questions = qa_engine.suggest_questions()
        return {
            "suggested_questions": questions
        }
        
    except Exception as e:
        logger.error(f"Error getting general questions: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/stats")
async def get_stats():
    """Get database statistics"""
    try:
        stats = vector_store.get_collection_stats()
        return {
            "database_stats": stats,
            "api_info": {
                "embedding_model": settings.EMBEDDING_MODEL,
                "llm_model": settings.OPENAI_MODEL,
                "max_tokens": settings.MAX_TOKENS
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting stats: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.delete("/claim/{claim_no}")
async def delete_claim_data(claim_no: int):
    """Delete all data for a specific claim"""
    try:
        logger.info(f"Deleting data for claim {claim_no}")
        
        success = vector_store.delete_claim_data(claim_no)
        
        if success:
            return {"message": f"Successfully deleted data for claim {claim_no}"}
        else:
            raise HTTPException(status_code=500, detail="Failed to delete claim data")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting claim data: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Test vector store connection
        stats = vector_store.get_collection_stats()
        
        return {
            "status": "healthy",
            "vector_store": "connected",
            "total_chunks": stats.get("total_chunks", 0)
        }
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e)
        }

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.API_HOST,
        port=8001,
        reload=True
    )
