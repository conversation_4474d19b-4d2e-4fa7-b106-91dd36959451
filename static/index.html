<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Claims Data Q&A</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h2 {
            color: #555;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .sample-questions {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
        }
        .sample-question {
            cursor: pointer;
            padding: 8px;
            margin: 5px 0;
            background: white;
            border: 1px solid #ddd;
            border-radius: 3px;
            transition: background-color 0.2s;
        }
        .sample-question:hover {
            background-color: #e9ecef;
        }
        .data-source-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
            margin-left: 10px;
        }
        .badge-structured {
            background-color: #28a745;
            color: white;
        }
        .badge-chunks {
            background-color: #17a2b8;
            color: white;
        }
        .badge-error {
            background-color: #dc3545;
            color: white;
        }
        .confidence-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 5px 0;
        }
        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, #dc3545 0%, #ffc107 50%, #28a745 100%);
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Claims Data Q&A System</h1>
        
        <!-- Status Section -->
        <div class="section">
            <h2>System Status</h2>
            <button onclick="checkHealth()">Check Health</button>
            <button onclick="getStats()">Get Statistics</button>
            <div id="statusResult" class="result" style="display: none;"></div>
        </div>

        <!-- Upload Section -->
        <div class="section">
            <h2>Upload Claims Data</h2>
            <div class="form-group">
                <label for="fileInput">Select JSON File:</label>
                <input type="file" id="fileInput" accept=".json">
            </div>
            <button onclick="uploadFile()">Upload Claims Data</button>
            <div id="uploadResult" class="result" style="display: none;"></div>
        </div>

        <!-- Fetch from API Section -->
        <div class="section">
            <h2>Fetch Claims from Proclaim API</h2>
            <div class="form-group">
                <label for="claimNumbersInput">Claim Numbers (comma-separated):</label>
                <input type="text" id="claimNumbersInput" placeholder="e.g., 999999, 131420765" value="999999">
            </div>
            <button onclick="checkProclaimStatus()">Check API Status</button>
            <button onclick="fetchAndProcessClaims()">Fetch & Process Claims</button>
            <div id="fetchResult" class="result" style="display: none;"></div>
        </div>

        <!-- Q&A Section -->
        <div class="section">
            <h2>Ask Questions</h2>
            <div class="form-group">
                <label for="questionInput">Your Question:</label>
                <textarea id="questionInput" rows="3" placeholder="e.g., What deductions were applied to my claim and why?"></textarea>
            </div>
            <div class="form-group">
                <label for="claimNoInput">Claim Number (optional):</label>
                <input type="number" id="claimNoInput" placeholder="e.g., 999999">
            </div>
            <div class="form-group">
                <label for="qaMode">Question Mode:</label>
                <select id="qaMode">
                    <option value="smart" selected>Smart (Structured Data First)</option>
                    <option value="structured">Structured Data Only</option>
                    <option value="chunks">Chunks Only</option>
                </select>
            </div>
            <div class="form-group" id="maxResultsGroup">
                <label for="maxResultsInput">Max Results (for chunks mode):</label>
                <select id="maxResultsInput">
                    <option value="3">3</option>
                    <option value="5" selected>5</option>
                    <option value="10">10</option>
                </select>
            </div>
            <button onclick="askQuestion()">Ask Question</button>
            <button onclick="getSuggestedQuestions()">Get Suggested Questions</button>
            <button onclick="loadSampleQuestions()">Load Sample Questions</button>
            <div class="loading" id="qaLoading">
                <div class="spinner"></div>
                <p>Processing your question...</p>
            </div>
            <div id="qaResult" class="result" style="display: none;"></div>
        </div>

        <!-- Structured Data Testing Section -->
        <div class="section">
            <h2>Structured Data Testing</h2>
            <div class="form-group">
                <label for="structuredClaimNo">Claim Number:</label>
                <input type="number" id="structuredClaimNo" placeholder="e.g., 999999">
            </div>
            <button onclick="testStructuredData()">Test Structured Data Availability</button>
            <button onclick="viewStructuredData()">View Structured Data</button>
            <div id="structuredResult" class="result" style="display: none;"></div>
        </div>

        <!-- Claim Summary Section -->
        <div class="section">
            <h2>Claim Summary</h2>
            <div class="form-group">
                <label for="summaryClaimNo">Claim Number:</label>
                <input type="number" id="summaryClaimNo" placeholder="e.g., 999999">
            </div>
            <button onclick="getClaimSummary()">Get Summary</button>
            <div id="summaryResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8001';

        async function checkHealth() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                showResult('statusResult', JSON.stringify(data, null, 2), 'success');
            } catch (error) {
                showResult('statusResult', `Error: ${error.message}`, 'error');
            }
        }

        async function getStats() {
            try {
                const response = await fetch(`${API_BASE}/stats`);
                const data = await response.json();
                
                let statsHtml = '<div class="stats">';
                if (data.database_stats) {
                    const stats = data.database_stats;
                    statsHtml += `
                        <div class="stat-card">
                            <div class="stat-number">${stats.total_chunks || 0}</div>
                            <div class="stat-label">Total Chunks</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${stats.unique_claims || 0}</div>
                            <div class="stat-label">Unique Claims</div>
                        </div>
                    `;
                }
                statsHtml += '</div>';
                statsHtml += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                
                showResult('statusResult', statsHtml, 'info');
            } catch (error) {
                showResult('statusResult', `Error: ${error.message}`, 'error');
            }
        }

        async function uploadFile() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];

            if (!file) {
                showResult('uploadResult', 'Please select a file', 'error');
                return;
            }

            try {
                const formData = new FormData();
                formData.append('file', file);

                const response = await fetch(`${API_BASE}/upload-claims`, {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (response.ok) {
                    showResult('uploadResult', JSON.stringify(data, null, 2), 'success');
                } else {
                    showResult('uploadResult', `Error: ${data.detail}`, 'error');
                }
            } catch (error) {
                showResult('uploadResult', `Error: ${error.message}`, 'error');
            }
        }

        async function checkProclaimStatus() {
            try {
                const response = await fetch(`${API_BASE}/proclaim-status`);
                const data = await response.json();

                if (response.ok) {
                    let statusHtml = `<strong>Proclaim API Status:</strong> ${data.status}\n\n`;
                    statusHtml += `<strong>Configuration:</strong>\n`;
                    statusHtml += `- API URL: ${data.api_info.api_url}\n`;
                    statusHtml += `- Has Access Token: ${data.api_info.has_access_token}\n`;
                    statusHtml += `- Payer: ${data.api_info.payer}\n`;
                    statusHtml += `- Configuration Valid: ${data.api_info.configuration_valid}\n`;
                    statusHtml += `- Connection Test: ${data.connection_test_passed ? 'PASSED' : 'FAILED'}\n`;

                    const resultType = data.connection_test_passed ? 'success' : 'error';
                    showResult('fetchResult', statusHtml, resultType);
                } else {
                    showResult('fetchResult', `Error: ${data.detail}`, 'error');
                }
            } catch (error) {
                showResult('fetchResult', `Error: ${error.message}`, 'error');
            }
        }

        async function fetchAndProcessClaims() {
            const claimNumbersInput = document.getElementById('claimNumbersInput').value.trim();

            if (!claimNumbersInput) {
                showResult('fetchResult', 'Please enter claim numbers', 'error');
                return;
            }

            try {
                // Parse claim numbers
                const claimNumbers = claimNumbersInput.split(',').map(num => parseInt(num.trim())).filter(num => !isNaN(num));

                if (claimNumbers.length === 0) {
                    showResult('fetchResult', 'Please enter valid claim numbers', 'error');
                    return;
                }

                const requestBody = {
                    claim_number: claimNumbers[0]
                };

                const response = await fetch(`${API_BASE}/fetch-and-process`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });

                const data = await response.json();

                if (response.ok) {
                    let resultHtml = `<strong>Fetch and Process Results:</strong>\n\n`;
                    resultHtml += `Message: ${data.message}\n`;
                    resultHtml += `Requested Claims: ${data.requested_claims}\n`;
                    resultHtml += `Processed Claims: ${data.processed_claims}\n`;
                    resultHtml += `Total Chunks: ${data.processed_chunks}\n\n`;
                    resultHtml += `<strong>Chunk Types:</strong>\n`;
                    for (const [type, count] of Object.entries(data.chunk_types)) {
                        resultHtml += `- ${type}: ${count}\n`;
                    }

                    showResult('fetchResult', resultHtml, 'success');
                } else {
                    showResult('fetchResult', `Error: ${data.detail}`, 'error');
                }
            } catch (error) {
                showResult('fetchResult', `Error: ${error.message}`, 'error');
            }
        }

        async function askQuestion() {
            const question = document.getElementById('questionInput').value.trim();
            const claimNo = document.getElementById('claimNoInput').value;
            const maxResults = document.getElementById('maxResultsInput').value;
            const qaMode = document.getElementById('qaMode').value;

            if (!question) {
                showResult('qaResult', 'Please enter a question', 'error');
                return;
            }

            const loading = document.getElementById('qaLoading');
            loading.style.display = 'block';
            document.getElementById('qaResult').style.display = 'none';

            try {
                let endpoint = `${API_BASE}/ask`;
                let requestBody = {
                    question: question
                };

                if (claimNo) {
                    requestBody.claim_no = parseInt(claimNo);
                }

                // Choose endpoint based on mode
                if (qaMode === 'structured') {
                    endpoint = `${API_BASE}/ask-structured`;
                    requestBody.use_structured_data = true;
                } else if (qaMode === 'chunks') {
                    requestBody.max_results = parseInt(maxResults);
                } else {
                    // Smart mode - uses the regular /ask endpoint which now uses smart approach
                    requestBody.max_results = parseInt(maxResults);
                }

                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });

                const data = await response.json();
                loading.style.display = 'none';

                if (response.ok) {
                    let resultHtml = `<strong>Question:</strong> ${data.question}\n\n`;

                    // Add data source badge
                    const dataSource = data.data_source || 'unknown';
                    const badgeClass = dataSource === 'structured_claim_data' ? 'badge-structured' :
                                     dataSource === 'chunks' ? 'badge-chunks' : 'badge-error';
                    resultHtml += `<strong>Data Source:</strong> <span class="data-source-badge ${badgeClass}">${dataSource}</span>\n\n`;

                    resultHtml += `<strong>Answer:</strong>\n${data.answer}\n\n`;

                    // Enhanced confidence display
                    if (data.confidence_score !== undefined) {
                        const confidence = (data.confidence_score * 100).toFixed(1);
                        resultHtml += `<strong>Confidence Score:</strong> ${confidence}%\n`;
                        resultHtml += `<div class="confidence-bar"><div class="confidence-fill" style="width: ${confidence}%"></div></div>\n`;
                    }

                    // Show structured data info if available
                    if (data.available_sections && data.total_sections) {
                        resultHtml += `<strong>Data Completeness:</strong> ${data.available_sections}/${data.total_sections} sections available\n\n`;
                    }

                    // Show chunk info if available
                    if (data.relevant_chunks && data.relevant_chunks.length > 0) {
                        resultHtml += `<strong>Relevant Chunks:</strong> ${data.relevant_chunks.length}\n`;
                        resultHtml += '\n<strong>Sources:</strong>\n';
                        data.relevant_chunks.forEach((chunk, index) => {
                            resultHtml += `${index + 1}. Claim ${chunk.claim_no} (${chunk.chunk_type}) - Similarity: ${(chunk.similarity_score * 100).toFixed(1)}%\n`;
                        });
                    }

                    showResult('qaResult', resultHtml, 'success');
                } else {
                    showResult('qaResult', `Error: ${data.detail}`, 'error');
                }
            } catch (error) {
                loading.style.display = 'none';
                showResult('qaResult', `Error: ${error.message}`, 'error');
            }
        }

        async function getSuggestedQuestions() {
            const claimNo = document.getElementById('claimNoInput').value;
            
            try {
                let url = `${API_BASE}/questions`;
                if (claimNo) {
                    url = `${API_BASE}/claim/${claimNo}/questions`;
                }

                const response = await fetch(url);
                const data = await response.json();

                if (response.ok) {
                    let resultHtml = '<strong>Suggested Questions:</strong>\n\n';
                    data.suggested_questions.forEach((question, index) => {
                        resultHtml += `${index + 1}. ${question}\n`;
                    });

                    showResult('qaResult', resultHtml, 'info');
                } else {
                    showResult('qaResult', `Error: ${data.detail}`, 'error');
                }
            } catch (error) {
                showResult('qaResult', `Error: ${error.message}`, 'error');
            }
        }

        async function getClaimSummary() {
            const claimNo = document.getElementById('summaryClaimNo').value;

            if (!claimNo) {
                showResult('summaryResult', 'Please enter a claim number', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/claim/${claimNo}/summary`);
                const data = await response.json();

                if (response.ok) {
                    let resultHtml = `<strong>Claim ${data.claim_no} Summary:</strong>\n\n`;
                    resultHtml += data.summary;

                    if (data.details) {
                        resultHtml += '\n\n<strong>Details:</strong>\n';
                        resultHtml += JSON.stringify(data.details, null, 2);
                    }

                    showResult('summaryResult', resultHtml, 'success');
                } else {
                    showResult('summaryResult', `Error: ${data.detail}`, 'error');
                }
            } catch (error) {
                showResult('summaryResult', `Error: ${error.message}`, 'error');
            }
        }

        async function testStructuredData() {
            const claimNo = document.getElementById('structuredClaimNo').value;

            if (!claimNo) {
                showResult('structuredResult', 'Please enter a claim number', 'error');
                return;
            }

            try {
                // Test by making a simple structured question
                const response = await fetch(`${API_BASE}/ask-structured`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        question: "Is structured data available for this claim?",
                        claim_no: parseInt(claimNo),
                        use_structured_data: true
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    let resultHtml = `<strong>Structured Data Test for Claim ${claimNo}:</strong>\n\n`;
                    resultHtml += `Data Source: ${data.data_source}\n`;
                    resultHtml += `Structured Data Used: ${data.structured_data_used ? 'Yes' : 'No'}\n`;

                    if (data.available_sections && data.total_sections) {
                        resultHtml += `Available Sections: ${data.available_sections}/${data.total_sections}\n`;
                        const completeness = (data.available_sections / data.total_sections * 100).toFixed(1);
                        resultHtml += `Data Completeness: ${completeness}%\n`;
                    }

                    if (data.confidence_score !== undefined) {
                        resultHtml += `Confidence Score: ${(data.confidence_score * 100).toFixed(1)}%\n`;
                    }

                    const resultType = data.structured_data_used ? 'success' : 'info';
                    showResult('structuredResult', resultHtml, resultType);
                } else {
                    showResult('structuredResult', `Error: ${data.detail}`, 'error');
                }
            } catch (error) {
                showResult('structuredResult', `Error: ${error.message}`, 'error');
            }
        }

        async function viewStructuredData() {
            const claimNo = document.getElementById('structuredClaimNo').value;

            if (!claimNo) {
                showResult('structuredResult', 'Please enter a claim number', 'error');
                return;
            }

            try {
                // This is a workaround - we'll ask for a comprehensive overview to see the structured data
                const response = await fetch(`${API_BASE}/ask-structured`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        question: "Show me a comprehensive overview of all sections in my claim data",
                        claim_no: parseInt(claimNo),
                        use_structured_data: true
                    })
                });

                const data = await response.json();

                if (response.ok && data.structured_data_used) {
                    let resultHtml = `<strong>Structured Data Overview for Claim ${claimNo}:</strong>\n\n`;
                    resultHtml += data.answer;
                    showResult('structuredResult', resultHtml, 'success');
                } else {
                    showResult('structuredResult', 'No structured data available for this claim', 'info');
                }
            } catch (error) {
                showResult('structuredResult', `Error: ${error.message}`, 'error');
            }
        }

        function loadSampleQuestions() {
            const sampleQuestions = [
                "What deductions were applied to my claim and why?",
                "How much was the total amount deducted from my claim?",
                "What hospital was I treated at and what were the details?",
                "What was my medical condition and diagnosis?",
                "What policy details apply to my claim?",
                "Why were certain amounts not covered?",
                "What non-medical charges were applied?",
                "Give me a comprehensive overview of my claim",
                "What copay amount was applied and why?",
                "What hospital discounts were given?"
            ];

            let resultHtml = '<div class="sample-questions"><strong>Sample Questions (click to use):</strong>\n';
            sampleQuestions.forEach((question, index) => {
                resultHtml += `<div class="sample-question" onclick="selectSampleQuestion('${question.replace(/'/g, "\\'")}')">${index + 1}. ${question}</div>`;
            });
            resultHtml += '</div>';

            showResult('qaResult', resultHtml, 'info');
        }

        function selectSampleQuestion(question) {
            document.getElementById('questionInput').value = question;
            document.getElementById('qaResult').style.display = 'none';
        }

        function showResult(elementId, content, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = content;
            element.className = `result ${type}`;
            element.style.display = 'block';
        }

        // Auto-fill claim number from sample data and setup event listeners
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('claimNoInput').value = '999999';
            document.getElementById('summaryClaimNo').value = '999999';
            document.getElementById('structuredClaimNo').value = '999999';

            // Update max results visibility based on QA mode
            document.getElementById('qaMode').addEventListener('change', function() {
                const maxResultsGroup = document.getElementById('maxResultsGroup');
                if (this.value === 'structured') {
                    maxResultsGroup.style.display = 'none';
                } else {
                    maxResultsGroup.style.display = 'block';
                }
            });
        });
    </script>
</body>
</html>
