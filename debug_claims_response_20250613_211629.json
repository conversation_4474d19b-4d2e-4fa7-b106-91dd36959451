[{"_debug_info": {"total_claims": 4, "filtering_rule": "Only logs with typeOfLog: \"Claim\" will be processed", "alteration_timestamp": "2025-06-13T21:16:29.025181"}}, {"claimNo": 126050392, "logs": [{"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 370670.0, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 0.00"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 0.00"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: "}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 370670.0, "next_bill_amount": 370670.0, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 1, "total_rules_in_sequence": 5, "explanation": "Bill reduced from 370670.0 to 370670.0"}}, {"category": "NonMedicalExpense", "subCategory": "CapAmbulanceChargesByAmountComparision", "subCategoryDisplayName": "Ambulance Limit By Sum Insured & Amount Comparison", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 370670.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 370670.0, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Ambulance Limit By Sum Insured & Amount Comparison - Execution starts"}, {"message": "Comparision between 1% and flat amount 5000 is 5000"}, {"message": "Deduction amount is 5000 as payable amount is less than confiuration value 5000"}, {"message": "Ambulance Limit By Sum Insured & Amount Comparison - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 370670.0, "next_bill_amount": 370670.0, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 2, "total_rules_in_sequence": 5, "explanation": "Bill reduced from 370670.0 to 370670.0"}}, {"category": "NonMedicalExpense", "subCategory": "OverrideNMEParticularsAmount", "subCategoryDisplayName": "NME limit by Subcharge", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 370670.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 370670.0, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Limit applicable subcharge head: 1"}, {"message": "Matching Config Count: 1"}, {"message": "NME limit by Subcharge - Execution starts"}, {"message": "NME limit by Subcharge - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 370670.0, "next_bill_amount": 370670.0, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 3, "total_rules_in_sequence": 5, "explanation": "Bill reduced from 370670.0 to 370670.0"}}, {"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 370670.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 370670.0, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:6750.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:4750.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:8250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:35000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:16000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:8500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:40000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:6750.0"}, {"message": "Tariff ICU Room Rent From Master:6750.0"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 370670.0, "next_bill_amount": 370670.0, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 4, "total_rules_in_sequence": 5, "explanation": "Bill reduced from 370670.0 to 370670.0"}}, {"category": "PropotinateDeduction", "subCategory": "FixedLimitWithAilmentExclusion", "subCategoryDisplayName": "By Room Value With Ailment Exclusion", "preExecutionApprovedAmount": 370670.0, "preExecutionTotalBillAmount": 370670.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 370670.0, "postExecutionTotalBillAmount": 370670.0, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 10000"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Calulating Eligibile Room Category By RoomRent Started*****"}, {"message": "Rule Suggested, Eligible Room Rent 10000"}, {"message": "Rule Suggested, Eligible Room Rent Tolerance 105"}, {"message": "Rule Suggested, Eligible Room Rent ToleranceType Percentage"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Hospital room rent master details"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:6750.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:4750.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:8250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:35000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:16000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:8500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:40000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Hospital room rent master details After filtering ICU"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:6750.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:4750.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:8250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:35000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:16000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:8500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:40000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "No excat room rent details for the eligible room rent :10000"}, {"message": "Now trying to find room rent details by applying tolerance by :Percentage"}, {"message": "Tolerance Amount:10500.00"}, {"message": "Finding room rent details from master , taking max price which is less than tolerance amount 10500.00"}, {"message": "Max room rent price 8250.0"}, {"message": "Room Rent master details which is used to calculate eligible room rent"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:8250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Rule Calculated EligibleRoomCategory : DELUXE"}, {"message": "*****Calulating Eligibile Room Category By RoomRent END*****"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 10000"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Room Value With Ailment Exclusion - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 370670.0, "next_bill_amount": null, "bill_reduction": 0.0, "calculation_method": "final_rule_no_reduction", "position_in_sequence": 5, "total_rules_in_sequence": 5, "explanation": "Bill reduced from 370670.0 to final"}}], "_will_be_processed": true}, {"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 410000.0, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 0.00"}, {"message": "HMOU Discount Calculated: 0.0"}, {"message": "MAX Discount Calculated: 0.00"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: "}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 410000.0, "next_bill_amount": 410000.0, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 1, "total_rules_in_sequence": 5, "explanation": "Bill reduced from 410000.0 to 410000.0"}}, {"category": "NonMedicalExpense", "subCategory": "CapAmbulanceChargesByAmountComparision", "subCategoryDisplayName": "Ambulance Limit By Sum Insured & Amount Comparison", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 410000.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 410000.0, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Ambulance Limit By Sum Insured & Amount Comparison - Execution starts"}, {"message": "Comparision between 1% and flat amount 5000 is 5000"}, {"message": "Deduction amount is 5000 as payable amount is less than confiuration value 5000"}, {"message": "Ambulance Limit By Sum Insured & Amount Comparison - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 410000.0, "next_bill_amount": 410000.0, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 2, "total_rules_in_sequence": 5, "explanation": "Bill reduced from 410000.0 to 410000.0"}}, {"category": "NonMedicalExpense", "subCategory": "OverrideNMEParticularsAmount", "subCategoryDisplayName": "NME limit by Subcharge", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 410000.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 410000.0, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Limit applicable subcharge head: 1"}, {"message": "Matching Config Count: 1"}, {"message": "NME limit by Subcharge - Execution starts"}, {"message": "NME limit by Subcharge - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 410000.0, "next_bill_amount": 410000.0, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 3, "total_rules_in_sequence": 5, "explanation": "Bill reduced from 410000.0 to 410000.0"}}, {"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 410000.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 410000.0, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:6750.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:4750.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:8250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:35000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:16000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:8500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:40000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:6750.0"}, {"message": "Tariff ICU Room Rent From Master:6750.0"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 410000.0, "next_bill_amount": 410000.0, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 4, "total_rules_in_sequence": 5, "explanation": "Bill reduced from 410000.0 to 410000.0"}}, {"category": "PropotinateDeduction", "subCategory": "FixedLimitWithAilmentExclusion", "subCategoryDisplayName": "By Room Value With Ailment Exclusion", "preExecutionApprovedAmount": 410000.0, "preExecutionTotalBillAmount": 410000.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 410000.0, "postExecutionTotalBillAmount": 410000.0, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 10000"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Calulating Eligibile Room Category By RoomRent Started*****"}, {"message": "Rule Suggested, Eligible Room Rent 10000"}, {"message": "Rule Suggested, Eligible Room Rent Tolerance 105"}, {"message": "Rule Suggested, Eligible Room Rent ToleranceType Percentage"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Hospital room rent master details"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:6750.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:4750.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:8250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:35000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:16000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:8500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:40000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Hospital room rent master details After filtering ICU"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:6750.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:4750.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:8250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:35000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:16000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:8500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:40000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "No excat room rent details for the eligible room rent :10000"}, {"message": "Now trying to find room rent details by applying tolerance by :Percentage"}, {"message": "Tolerance Amount:10500.00"}, {"message": "Finding room rent details from master , taking max price which is less than tolerance amount 10500.00"}, {"message": "Max room rent price 8250.0"}, {"message": "Room Rent master details which is used to calculate eligible room rent"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:8250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Rule Calculated EligibleRoomCategory : DELUXE"}, {"message": "*****Calulating Eligibile Room Category By RoomRent END*****"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 10000"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Room Value With Ailment Exclusion - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 410000.0, "next_bill_amount": null, "bill_reduction": 0.0, "calculation_method": "final_rule_no_reduction", "position_in_sequence": 5, "total_rules_in_sequence": 5, "explanation": "Bill reduced from 410000.0 to final"}}], "_will_be_processed": true}], "_cleaning_metadata": {"fields_removed": ["logCreatedDate (from logs)", "data (from logs)", "timeOfOccurrence (from data entries and rule logs)", "loggerType (from data entries and rule logs)", "configRuleLogObject (from rule engine logs)", "matchingConfig (from rule engine logs)"], "fields_renamed": ["logMessages → reasons (in rule engine logs)"], "filtering_applied": ["Only rule engine logs with non-empty matchingConfig arrays are included", "Only rule engine logs with non-zero postExecutionTotalBillAmount are included"], "calculations_applied": ["postExectionTotalDeductionAmount calculated as bill amount reduction (current_bill - next_bill)"], "cleaning_timestamp": "2025-06-13T21:16:29.001530", "purpose": "Remove unnecessary fields, rename for clarity, filter for relevant rule logs, and calculate incremental deductions"}, "_processing_metadata": {"filtered_for_claim_logs_only": true, "processing_timestamp": "2025-06-13T21:16:29.025116", "total_logs": 2, "claim_logs_count": 2}}, {"claimNo": 125870715, "logs": [{"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 410000.0, "postExectionTotalDeductionAmount": 143500.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 0.00"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 0.00"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: "}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 410000.0, "next_bill_amount": 266500.0, "bill_reduction": 143500.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 1, "total_rules_in_sequence": 6, "explanation": "Bill reduced from 410000.0 to 266500.0"}}, {"category": "NonMedicalExpense", "subCategory": "HandelNMEByPreAuthType", "subCategoryDisplayName": "Estimate For Initial", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 410000.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 266500.0, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "*****Calulating CalculateNMEDeduction Started*****"}, {"message": "Matching Config Count: 1"}, {"message": "Estimate For Initial - Execution starts"}, {"message": "CalculateNMEDeduction isInitialPA: TRUE"}, {"message": "NMEDeduction percentage: 35"}, {"message": "*****Calulating CalculateNMEDeduction End*****"}, {"message": "Estimate For Initial - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 266500.0, "next_bill_amount": 266500.0, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 2, "total_rules_in_sequence": 6, "explanation": "Bill reduced from 266500.0 to 266500.0"}}, {"category": "NonMedicalExpense", "subCategory": "CapAmbulanceChargesByAmountComparision", "subCategoryDisplayName": "Ambulance Limit By Sum Insured & Amount Comparison", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 266500.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 266500.0, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Ambulance Limit By Sum Insured & Amount Comparison - Execution starts"}, {"message": "Comparision between 1% and flat amount 5000 is 5000"}, {"message": "Deduction amount is 5000 as payable amount is less than confiuration value 5000"}, {"message": "Ambulance Limit By Sum Insured & Amount Comparison - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 266500.0, "next_bill_amount": 266500.0, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 3, "total_rules_in_sequence": 6, "explanation": "Bill reduced from 266500.0 to 266500.0"}}, {"category": "NonMedicalExpense", "subCategory": "OverrideNMEParticularsAmount", "subCategoryDisplayName": "NME limit by Subcharge", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 266500.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 266500.0, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Limit applicable subcharge head: 1"}, {"message": "Matching Config Count: 1"}, {"message": "NME limit by Subcharge - Execution starts"}, {"message": "NME limit by Subcharge - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 266500.0, "next_bill_amount": 266500.0, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 4, "total_rules_in_sequence": 6, "explanation": "Bill reduced from 266500.0 to 266500.0"}}, {"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 266500.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 266500.0, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:6750.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:4750.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:8250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:35000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:16000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:8500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:40000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:6750.0"}, {"message": "Tariff ICU Room Rent From Master:6750.0"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 266500.0, "next_bill_amount": 266500.0, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 5, "total_rules_in_sequence": 6, "explanation": "Bill reduced from 266500.0 to 266500.0"}}, {"category": "PropotinateDeduction", "subCategory": "FixedLimitWithAilmentExclusion", "subCategoryDisplayName": "By Room Value With Ailment Exclusion", "preExecutionApprovedAmount": 266500.0, "preExecutionTotalBillAmount": 266500.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 266500.0, "postExecutionTotalBillAmount": 266500.0, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 10000"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Calulating Eligibile Room Category By RoomRent Started*****"}, {"message": "Rule Suggested, Eligible Room Rent 10000"}, {"message": "Rule Suggested, Eligible Room Rent Tolerance 105"}, {"message": "Rule Suggested, Eligible Room Rent ToleranceType Percentage"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Hospital room rent master details"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:6750.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:4750.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:8250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:35000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:16000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:8500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:40000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Hospital room rent master details After filtering ICU"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:6750.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:4750.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:8250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:35000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:16000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:8500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:40000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "No excat room rent details for the eligible room rent :10000"}, {"message": "Now trying to find room rent details by applying tolerance by :Percentage"}, {"message": "Tolerance Amount:10500.00"}, {"message": "Finding room rent details from master , taking max price which is less than tolerance amount 10500.00"}, {"message": "Max room rent price 8250.0"}, {"message": "Room Rent master details which is used to calculate eligible room rent"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:8250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Rule Calculated EligibleRoomCategory : DELUXE"}, {"message": "*****Calulating Eligibile Room Category By RoomRent END*****"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 10000"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Due to Initial PA LengthOfStay calculated on DOD - DOA "}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 5"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 5"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 50000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Room Value With Ailment Exclusion - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 266500.0, "next_bill_amount": null, "bill_reduction": 0.0, "calculation_method": "final_rule_no_reduction", "position_in_sequence": 6, "total_rules_in_sequence": 6, "explanation": "Bill reduced from 266500.0 to final"}}], "_will_be_processed": true}], "_cleaning_metadata": {"fields_removed": ["logCreatedDate (from logs)", "data (from logs)", "timeOfOccurrence (from data entries and rule logs)", "loggerType (from data entries and rule logs)", "configRuleLogObject (from rule engine logs)", "matchingConfig (from rule engine logs)"], "fields_renamed": ["logMessages → reasons (in rule engine logs)"], "filtering_applied": ["Only rule engine logs with non-empty matchingConfig arrays are included", "Only rule engine logs with non-zero postExecutionTotalBillAmount are included"], "calculations_applied": ["postExectionTotalDeductionAmount calculated as bill amount reduction (current_bill - next_bill)"], "cleaning_timestamp": "2025-06-13T21:16:29.009800", "purpose": "Remove unnecessary fields, rename for clarity, filter for relevant rule logs, and calculate incremental deductions"}, "_processing_metadata": {"filtered_for_claim_logs_only": true, "processing_timestamp": "2025-06-13T21:16:29.025170", "total_logs": 1, "claim_logs_count": 1}}, {"claimNo": 125870716, "logs": [{"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 458654.0, "postExectionTotalDeductionAmount": 39964.0, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 0.00"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 0.00"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: "}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 458654.0, "next_bill_amount": 418690.0, "bill_reduction": 39964.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 1, "total_rules_in_sequence": 4, "explanation": "Bill reduced from 458654.0 to 418690.0"}}, {"category": "NonMedicalExpense", "subCategory": "OverrideNMEParticularsAmount", "subCategoryDisplayName": "NME limit by Subcharge", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 418690.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 418690.0, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Limit applicable subcharge head: 1"}, {"message": "Matching Config Count: 1"}, {"message": "NME limit by Subcharge - Execution starts"}, {"message": "NME limit by Subcharge - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 418690.0, "next_bill_amount": 418690.0, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 2, "total_rules_in_sequence": 4, "explanation": "Bill reduced from 418690.0 to 418690.0"}}, {"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 418690.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 418690.0, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:6750.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:4750.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:8250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:35000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:16000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:8500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:40000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SemiPrivateRoom"}, {"message": "Tariff Room Rent From Master:4750.0"}, {"message": "Tariff ICU Room Rent From Master:4750.0"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 418690.0, "next_bill_amount": 418690.0, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 3, "total_rules_in_sequence": 4, "explanation": "Bill reduced from 418690.0 to 418690.0"}}, {"category": "PropotinateDeduction", "subCategory": "FixedLimitWithAilmentExclusion", "subCategoryDisplayName": "By Room Value With Ailment Exclusion", "preExecutionApprovedAmount": 418690.0, "preExecutionTotalBillAmount": 418690.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 418690.0, "postExecutionTotalBillAmount": 418690.0, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 10000"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Calulating Eligibile Room Category By RoomRent Started*****"}, {"message": "Rule Suggested, Eligible Room Rent 10000"}, {"message": "Rule Suggested, Eligible Room Rent Tolerance 105"}, {"message": "Rule Suggested, Eligible Room Rent ToleranceType Percentage"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Hospital room rent master details"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:6750.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:4750.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:8250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:35000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:16000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:8500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:40000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Hospital room rent master details After filtering ICU"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:6750.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:4750.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:8250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:35000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:16000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:8500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:40000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "No excat room rent details for the eligible room rent :10000"}, {"message": "Now trying to find room rent details by applying tolerance by :Percentage"}, {"message": "Tolerance Amount:10500.00"}, {"message": "Finding room rent details from master , taking max price which is less than tolerance amount 10500.00"}, {"message": "Max room rent price 8250.0"}, {"message": "Room Rent master details which is used to calculate eligible room rent"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:8250.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Rule Calculated EligibleRoomCategory : DELUXE"}, {"message": "*****Calulating Eligibile Room Category By RoomRent END*****"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 10000"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 5.00"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 4750"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 50000.00"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Room Value With Ailment Exclusion - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 418690.0, "next_bill_amount": null, "bill_reduction": 0.0, "calculation_method": "final_rule_no_reduction", "position_in_sequence": 4, "total_rules_in_sequence": 4, "explanation": "Bill reduced from 418690.0 to final"}}], "_will_be_processed": true}], "_cleaning_metadata": {"fields_removed": ["logCreatedDate (from logs)", "data (from logs)", "timeOfOccurrence (from data entries and rule logs)", "loggerType (from data entries and rule logs)", "configRuleLogObject (from rule engine logs)", "matchingConfig (from rule engine logs)"], "fields_renamed": ["logMessages → reasons (in rule engine logs)"], "filtering_applied": ["Only rule engine logs with non-empty matchingConfig arrays are included", "Only rule engine logs with non-zero postExecutionTotalBillAmount are included"], "calculations_applied": ["postExectionTotalDeductionAmount calculated as bill amount reduction (current_bill - next_bill)"], "cleaning_timestamp": "2025-06-13T21:16:29.018876", "purpose": "Remove unnecessary fields, rename for clarity, filter for relevant rule logs, and calculate incremental deductions"}, "_processing_metadata": {"filtered_for_claim_logs_only": true, "processing_timestamp": "2025-06-13T21:16:29.025173", "total_logs": 1, "claim_logs_count": 1}}, {"claimNo": 126126998, "logs": [], "_cleaning_metadata": {"fields_removed": ["logCreatedDate (from logs)", "data (from logs)", "timeOfOccurrence (from data entries and rule logs)", "loggerType (from data entries and rule logs)", "configRuleLogObject (from rule engine logs)", "matchingConfig (from rule engine logs)"], "fields_renamed": ["logMessages → reasons (in rule engine logs)"], "filtering_applied": ["Only rule engine logs with non-empty matchingConfig arrays are included", "Only rule engine logs with non-zero postExecutionTotalBillAmount are included"], "calculations_applied": ["postExectionTotalDeductionAmount calculated as bill amount reduction (current_bill - next_bill)"], "cleaning_timestamp": "2025-06-13T21:16:29.020346", "purpose": "Remove unnecessary fields, rename for clarity, filter for relevant rule logs, and calculate incremental deductions"}, "_processing_metadata": {"filtered_for_claim_logs_only": true, "processing_timestamp": "2025-06-13T21:16:29.025176", "total_logs": 0, "claim_logs_count": 0}}]