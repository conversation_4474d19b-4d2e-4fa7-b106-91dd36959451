import json
import uuid
import logging
from datetime import datetime
from typing import List, Dict, Any, Union, Optional
from models import ClaimsResponse, ProcessedClaimChunk
import re

logger = logging.getLogger(__name__)

class ClaimsDataProcessor:
    def __init__(self):
        self.date_pattern = re.compile(r'/Date\((\d+)(?:[+-]\d{4})?\)/')

        # Patient-friendly field mappings for better LLM context
        self.patient_friendly_mappings = {
            # Financial terms - patient perspective
            'preExecutionApprovedAmount': 'initial_approved_amount',
            'preExecutionTotalBillAmount': 'original_bill_amount',
            'preExectionTotalDeductionAmount': 'initial_deductions',
            'postExecutionApprovedAmount': 'final_approved_amount',
            'postExecutionTotalBillAmount': 'final_bill_amount',
            'postExectionTotalDeductionAmount': 'total_deductions_applied',

            # Process terms - patient perspective
            'category': 'benefit_category',
            'subCategory': 'specific_benefit_type',
            'subCategoryDisplayName': 'benefit_description',
            'loggerType': 'processing_stage',
            'timeOfOccurrence': 'processing_timestamp',
            'matchingConfig': 'applicable_policy_rules',
            'configRuleLogObject': 'policy_configuration_details',

            # Log types - patient perspective
            'typeOfLog': 'processing_activity_type',
            'logCreatedDate': 'activity_timestamp',
            'message': 'processing_note',
            'claimNo': 'claim_reference_number'
        }

        # Patient-friendly descriptions for categories
        self.category_descriptions = {
            'DEDUCTION': 'Amount Adjustments',
            'APPROVAL': 'Benefit Approvals',
            'VALIDATION': 'Eligibility Verification',
            'CONFIGURATION': 'Policy Rule Application',
            'FINANCIAL': 'Payment Processing',
            'MEDICAL': 'Medical Review',
            'POLICY': 'Coverage Verification',
            'WAITING_PERIOD': 'Waiting Period Check',
            'HOSPITAL': 'Provider Network Verification',
            'REPUDIATION': 'Claim Rejection Review'
        }
    
    def parse_dotnet_date(self, date_str: str) -> str:
        """Convert .NET date format to readable format"""
        match = self.date_pattern.search(date_str)
        if match:
            timestamp = int(match.group(1)) / 1000  # Convert to seconds
            return datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
        return date_str

    def get_patient_friendly_category(self, category: str) -> str:
        """Convert technical category to patient-friendly description"""
        return self.category_descriptions.get(category.upper(), category)

    def transform_to_patient_context(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Transform technical field names to patient-friendly context"""
        transformed = {}
        for key, value in data.items():
            # Use patient-friendly mapping if available
            friendly_key = self.patient_friendly_mappings.get(key, key)

            # Transform category values to be more descriptive
            if key == 'category' and isinstance(value, str):
                transformed[friendly_key] = self.get_patient_friendly_category(value)
            else:
                transformed[friendly_key] = value

        return transformed

    def _is_technical_system_message(self, message: str) -> bool:
        """Filter out technical system messages that aren't relevant to patients"""
        technical_keywords = [
            'execution', 'logger', 'debug', 'trace', 'stack', 'exception',
            'thread', 'process', 'system', 'internal', 'database', 'query',
            'cache', 'memory', 'cpu', 'performance', 'timeout'
        ]
        message_lower = message.lower()
        return any(keyword in message_lower for keyword in technical_keywords)

    def _alter_claims_response(self, claims_data: Union[Dict[str, Any], List[Dict[str, Any]]]) -> Union[Dict[str, Any], List[Dict[str, Any]]]:
        """
        Alter the claims response data before saving to debug file

        This method allows you to modify the claims_data structure before it's saved
        to the debug file, so you can see the processed/altered version.

        Args:
            claims_data: Original claims data

        Returns:
            Altered claims data
        """
        # Make a deep copy to avoid modifying the original data
        import copy
        altered_data = copy.deepcopy(claims_data)

        # Example alterations - customize as needed:

        # 1. Add processing metadata
        if isinstance(altered_data, list):
            for claim in altered_data:
                if isinstance(claim, dict):
                    claim['_processing_metadata'] = {
                        'filtered_for_claim_logs_only': True,
                        'processing_timestamp': datetime.now().isoformat(),
                        'total_logs': len(claim.get('logs', [])),
                        'claim_logs_count': len([log for log in claim.get('logs', []) if log.get('typeOfLog') == 'Claim'])
                    }

        # 2. Mark filtered logs
        if isinstance(altered_data, list):
            for claim in altered_data:
                if isinstance(claim, dict) and 'logs' in claim:
                    for log in claim['logs']:
                        if log.get('typeOfLog') == 'Claim':
                            log['_will_be_processed'] = True
                        else:
                            log['_will_be_processed'] = False
                            log['_filtered_out'] = f"Excluded: typeOfLog is '{log.get('typeOfLog')}', not 'Claim'"

        # 3. Add summary of what will be processed
        if isinstance(altered_data, list):
            processing_summary = {
                '_debug_info': {
                    'total_claims': len(altered_data),
                    'filtering_rule': 'Only logs with typeOfLog: "Claim" will be processed',
                    'alteration_timestamp': datetime.now().isoformat()
                }
            }
            # Add summary as first item if it's a list
            altered_data.insert(0, processing_summary)

        return altered_data
    
    def format_financial_amounts(self, rule_log: Dict[str, Any]) -> str:
        """Format financial information in patient-friendly terms"""
        financial_info = []

        if rule_log.get('preExecutionApprovedAmount', 0) > 0:
            financial_info.append(f"Initial Approved Amount: ₹{rule_log['preExecutionApprovedAmount']:,.2f}")

        if rule_log.get('preExecutionTotalBillAmount', 0) > 0:
            financial_info.append(f"Original Bill Amount: ₹{rule_log['preExecutionTotalBillAmount']:,.2f}")

        if rule_log.get('postExecutionApprovedAmount', 0) > 0:
            financial_info.append(f"Final Approved Amount: ₹{rule_log['postExecutionApprovedAmount']:,.2f}")

        if rule_log.get('postExecutionTotalBillAmount', 0) > 0:
            financial_info.append(f"Final Bill Amount: ₹{rule_log['postExecutionTotalBillAmount']:,.2f}")

        if rule_log.get('postExectionTotalDeductionAmount', 0) > 0:
            financial_info.append(f"Total Deductions Applied: ₹{rule_log['postExectionTotalDeductionAmount']:,.2f}")

        return " | ".join(financial_info) if financial_info else "No financial impact"
    
    def format_config_rules(self, config_rules: List[Dict[str, Any]]) -> str:
        """Format configuration rules in a readable way"""
        if not config_rules:
            return "No configuration rules"
        
        formatted_rules = []
        for rule in config_rules:
            if rule.get('isToBeDisplayed', True):
                display_name = rule.get('displayName', 'Unknown')
                value = rule.get('value', 'N/A')
                claim_value = rule.get('claimValue', '')
                
                rule_text = f"{display_name}: {value}"
                if claim_value:
                    rule_text += f" (Claim Value: {claim_value})"
                formatted_rules.append(rule_text)
        
        return " | ".join(formatted_rules)
    
    def format_matching_config(self, matching_config: List[Dict[str, Any]]) -> str:
        """Format matching configuration in a readable way"""
        if not matching_config:
            return "No matching configuration"
        
        formatted_configs = []
        for config in matching_config:
            config_items = []
            for key, value in config.items():
                if value and str(value).strip():
                    config_items.append(f"{key}: {value}")
            
            if config_items:
                formatted_configs.append(" | ".join(config_items))
        
        return " || ".join(formatted_configs)
    
    def process_rule_engine_log(self, claim_no: int, rule_log: Dict[str, Any], actual_claim_no: Optional[int] = None) -> ProcessedClaimChunk:
        """Process a single rule engine log into a patient-friendly formatted chunk

        Args:
            claim_no: The reference claim number to use for the chunk
            rule_log: The rule engine log data
            actual_claim_no: The actual claim number where this data came from (for metadata)
        """

        # Basic information with patient-friendly terms
        time_occurrence = self.parse_dotnet_date(rule_log.get('timeOfOccurrence', ''))
        category = self.get_patient_friendly_category(rule_log.get('category', 'Unknown'))
        sub_category = rule_log.get('subCategory', 'Unknown')
        display_name = rule_log.get('subCategoryDisplayName', 'Unknown')
        logger_type = rule_log.get('loggerType', 'Information')

        # Format log messages with patient context
        log_messages = rule_log.get('logMessages', [])
        formatted_messages = []
        for msg in log_messages:
            msg_time = self.parse_dotnet_date(msg.get('timeOfOccurrence', ''))
            # Filter out technical system messages, focus on patient-relevant info
            message = msg.get('message', '')
            if not self._is_technical_system_message(message):
                formatted_messages.append(f"[{msg_time}] {message}")

        # Format financial information
        financial_info = self.format_financial_amounts(rule_log)

        # Format configuration rules
        config_rules_text = self.format_config_rules(rule_log.get('configRuleLogObject', []))

        # Format matching configuration
        matching_config_text = self.format_matching_config(rule_log.get('matchingConfig', []))

        # Create patient-focused content
        content_parts = [
            f"BENEFIT PROCESSING: {display_name}",
            f"Benefit Type: {category} > {sub_category}",
            f"Processed On: {time_occurrence}",
            f"Processing Stage: {logger_type}",
            "",
            "FINANCIAL IMPACT ON YOUR CLAIM:",
            financial_info,
            "",
        ]

        if formatted_messages:
            content_parts.extend([
                "PROCESSING NOTES:",
                "\n".join(formatted_messages),
                "",
            ])

        if matching_config_text != "No matching configuration":
            content_parts.extend([
                "APPLICABLE POLICY RULES:",
                matching_config_text,
                "",
            ])

        if config_rules_text != "No configuration rules":
            content_parts.extend([
                "POLICY CONFIGURATION DETAILS:",
                config_rules_text,
                "",
            ])

        content = "\n".join(content_parts)
        
        # Create patient-focused metadata
        metadata = {
            "benefit_category": category,
            "specific_benefit_type": sub_category,
            "benefit_description": display_name,
            "processing_stage": logger_type,
            "processing_timestamp": time_occurrence,
            "has_financial_impact": any([
                rule_log.get('postExecutionApprovedAmount', 0) > 0,
                rule_log.get('postExecutionTotalBillAmount', 0) > 0,
                rule_log.get('postExectionTotalDeductionAmount', 0) > 0
            ]),
            "has_applicable_policy_rules": len(rule_log.get('matchingConfig', [])) > 0,
            "processing_notes_count": len([msg for msg in log_messages if not self._is_technical_system_message(msg.get('message', ''))]),
            "patient_relevant": True,  # Flag to indicate this is patient-facing information
            "reference_claim_no": claim_no,  # The claim number used for referencing
            "actual_claim_no": actual_claim_no if actual_claim_no is not None else claim_no,  # The actual source claim
            "is_parent_claim": actual_claim_no is None or claim_no == actual_claim_no
        }
        
        return ProcessedClaimChunk(
            claim_no=claim_no,
            chunk_id=str(uuid.uuid4()),
            chunk_type="rule_engine_log",
            content=content,
            metadata=metadata,
            timestamp=datetime.now().isoformat()
        )
    
    def process_general_log(self, claim_no: int, log: Dict[str, Any], actual_claim_no: Optional[int] = None) -> ProcessedClaimChunk:
        """Process a general log entry into a patient-friendly formatted chunk

        Args:
            claim_no: The reference claim number to use for the chunk
            log: The general log data
            actual_claim_no: The actual claim number where this data came from (for metadata)
        """

        log_created_date = self.parse_dotnet_date(log.get('logCreatedDate', ''))
        type_of_log = log.get('typeOfLog', 'Unknown')
        log_data = log.get('data', [])

        # Transform log type to patient-friendly description
        friendly_log_type = self.get_patient_friendly_category(type_of_log)

        content_parts = [
            f"CLAIM ACTIVITY: {friendly_log_type}",
            f"Activity Date: {log_created_date}",
            "",
        ]

        if log_data:
            content_parts.append("ACTIVITY DETAILS:")
            for entry in log_data:
                entry_time = self.parse_dotnet_date(entry.get('timeOfOccurrence', ''))
                message = entry.get('message', '')
                logger_type = entry.get('loggerType', '0')

                # Filter out technical messages for patient view
                if not self._is_technical_system_message(message):
                    content_parts.append(f"[{entry_time}] [{logger_type}] {message}")
            content_parts.append("")

        content = "\n".join(content_parts)

        metadata = {
            "processing_activity_type": friendly_log_type,
            "activity_timestamp": log_created_date,
            "relevant_entries_count": len([entry for entry in log_data if not self._is_technical_system_message(entry.get('message', ''))]),
            "patient_relevant": True,
            "reference_claim_no": claim_no,  # The claim number used for referencing
            "actual_claim_no": actual_claim_no if actual_claim_no is not None else claim_no,  # The actual source claim
            "is_parent_claim": actual_claim_no is None or claim_no == actual_claim_no
        }
        
        return ProcessedClaimChunk(
            claim_no=claim_no,
            chunk_id=str(uuid.uuid4()),
            chunk_type="general_log",
            content=content,
            metadata=metadata,
            timestamp=datetime.now().isoformat()
        )
    
    def process_claims_data(self, claims_data: Union[Dict[str, Any], List[Dict[str, Any]]],
                           parent_claim_no: Optional[int] = None) -> List[ProcessedClaimChunk]:
        """Process the entire claims data into formatted chunks

        Args:
            claims_data: The claims data to process
            parent_claim_no: Optional parent claim number to use for all chunks.
                           If provided, all chunks will reference this parent claim number
                           instead of their individual claim numbers.
        """

        processed_chunks = []

        # Parse the claims response using the flexible method
        claims_response = ClaimsResponse.from_response(claims_data)

        # Filter claims_response to only include logs with typeOfLog: "Claim"
        for claim_data in claims_response.get_claims():
            # Filter logs to only keep "Claim" type logs
            claim_data.logs = [log for log in claim_data.logs if log.typeOfLog == "Claim"]

        logger.info(f"Filtered claims_response to only include 'Claim' logs: {claims_response}")

        # Convert filtered claims_response back to dict format for debug saving
        filtered_claims_data = []
        for claim_data in claims_response.get_claims():
            claim_dict = claim_data.model_dump()
            filtered_claims_data.append(claim_dict)

        # Alter the filtered claims response and save to debug file
        altered_claims_data = self._alter_claims_response(filtered_claims_data)

        # Save altered claims response to file for debugging
        try:
            import json
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            debug_filename = f"debug_claims_response_{timestamp}.json"

            # Convert altered_claims_data to JSON serializable format if needed
            if hasattr(altered_claims_data, 'model_dump'):
                debug_data = altered_claims_data.model_dump()
            elif hasattr(altered_claims_data, 'dict'):
                debug_data = altered_claims_data.dict()
            else:
                debug_data = altered_claims_data

            with open(debug_filename, 'w', encoding='utf-8') as f:
                json.dump(debug_data, f, indent=2, ensure_ascii=False, default=str)

            logger.info(f"Altered claims response saved to {debug_filename} for debugging")
        except Exception as e:
            logger.warning(f"Failed to save altered claims response for debugging: {e}")

        for claim_data in claims_response.get_claims():
            actual_claim_no = claim_data.claimNo
            # Use parent claim number if provided, otherwise use the actual claim number
            reference_claim_no = parent_claim_no if parent_claim_no is not None else actual_claim_no

            # claim_data.logs now only contains "Claim" type logs due to filtering above
            claim_logs = claim_data.logs

            # Create a summary chunk for the claim
            summary_content = f"""CLAIM SUMMARY
Reference Claim Number: {reference_claim_no}
Actual Data Source: {actual_claim_no}
Claim Logs (after filtering): {len(claim_logs)}

LOG TYPES PRESENT (after filtering):
{chr(10).join([f"- {log.typeOfLog}" for log in claim_data.logs])}

FILTERING APPLIED:
claims_response was filtered to only include logs with typeOfLog: "Claim"
All other log types were removed from the response object
"""

            summary_chunk = ProcessedClaimChunk(
                claim_no=reference_claim_no,  # Use reference claim number
                chunk_id=str(uuid.uuid4()),
                chunk_type="claim_summary",
                content=summary_content,
                metadata={
                    "reference_claim_no": reference_claim_no,
                    "actual_claim_no": actual_claim_no,
                    "claim_logs_after_filtering": len(claim_logs),
                    "log_types_after_filtering": [log.typeOfLog for log in claim_data.logs],
                    "filtering_method": "claims_response object filtered to only include 'Claim' logs",
                    "is_parent_claim": reference_claim_no == actual_claim_no
                },
                timestamp=datetime.now().isoformat()
            )
            processed_chunks.append(summary_chunk)

            # Process only the filtered claim logs
            for log in claim_logs:
                # Process general log data
                if log.data:
                    general_chunk = self.process_general_log(reference_claim_no, log.model_dump(), actual_claim_no)
                    processed_chunks.append(general_chunk)

                # Process rule engine logs
                for rule_log in log.ruleEngineLogs:
                    rule_chunk = self.process_rule_engine_log(reference_claim_no, rule_log.model_dump(), actual_claim_no)
                    processed_chunks.append(rule_chunk)

        return processed_chunks
