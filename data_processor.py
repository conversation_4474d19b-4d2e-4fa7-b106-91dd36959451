import json
import uuid
from datetime import datetime
from typing import List, Dict, Any
from models import ClaimsResponse, ProcessedClaimChunk
import re

class ClaimsDataProcessor:
    def __init__(self):
        self.date_pattern = re.compile(r'/Date\((\d+)(?:[+-]\d{4})?\)/')

        # Patient-friendly field mappings for better LLM context
        self.patient_friendly_mappings = {
            # Financial terms - patient perspective
            'preExecutionApprovedAmount': 'initial_approved_amount',
            'preExecutionTotalBillAmount': 'original_bill_amount',
            'preExectionTotalDeductionAmount': 'initial_deductions',
            'postExecutionApprovedAmount': 'final_approved_amount',
            'postExecutionTotalBillAmount': 'final_bill_amount',
            'postExectionTotalDeductionAmount': 'total_deductions_applied',

            # Process terms - patient perspective
            'category': 'benefit_category',
            'subCategory': 'specific_benefit_type',
            'subCategoryDisplayName': 'benefit_description',
            'loggerType': 'processing_stage',
            'timeOfOccurrence': 'processing_timestamp',
            'matchingConfig': 'applicable_policy_rules',
            'configRuleLogObject': 'policy_configuration_details',

            # Log types - patient perspective
            'typeOfLog': 'processing_activity_type',
            'logCreatedDate': 'activity_timestamp',
            'message': 'processing_note',
            'claimNo': 'claim_reference_number'
        }

        # Patient-friendly descriptions for categories
        self.category_descriptions = {
            'DEDUCTION': 'Amount Adjustments',
            'APPROVAL': 'Benefit Approvals',
            'VALIDATION': 'Eligibility Verification',
            'CONFIGURATION': 'Policy Rule Application',
            'FINANCIAL': 'Payment Processing',
            'MEDICAL': 'Medical Review',
            'POLICY': 'Coverage Verification',
            'WAITING_PERIOD': 'Waiting Period Check',
            'HOSPITAL': 'Provider Network Verification',
            'REPUDIATION': 'Claim Rejection Review'
        }
    
    def parse_dotnet_date(self, date_str: str) -> str:
        """Convert .NET date format to readable format"""
        match = self.date_pattern.search(date_str)
        if match:
            timestamp = int(match.group(1)) / 1000  # Convert to seconds
            return datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
        return date_str

    def get_patient_friendly_category(self, category: str) -> str:
        """Convert technical category to patient-friendly description"""
        return self.category_descriptions.get(category.upper(), category)

    def transform_to_patient_context(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Transform technical field names to patient-friendly context"""
        transformed = {}
        for key, value in data.items():
            # Use patient-friendly mapping if available
            friendly_key = self.patient_friendly_mappings.get(key, key)

            # Transform category values to be more descriptive
            if key == 'category' and isinstance(value, str):
                transformed[friendly_key] = self.get_patient_friendly_category(value)
            else:
                transformed[friendly_key] = value

        return transformed

    def _is_technical_system_message(self, message: str) -> bool:
        """Filter out technical system messages that aren't relevant to patients"""
        technical_keywords = [
            'execution', 'logger', 'debug', 'trace', 'stack', 'exception',
            'thread', 'process', 'system', 'internal', 'database', 'query',
            'cache', 'memory', 'cpu', 'performance', 'timeout'
        ]
        message_lower = message.lower()
        return any(keyword in message_lower for keyword in technical_keywords)
    
    def format_financial_amounts(self, rule_log: Dict[str, Any]) -> str:
        """Format financial information in patient-friendly terms"""
        financial_info = []

        if rule_log.get('preExecutionApprovedAmount', 0) > 0:
            financial_info.append(f"Initial Approved Amount: ₹{rule_log['preExecutionApprovedAmount']:,.2f}")

        if rule_log.get('preExecutionTotalBillAmount', 0) > 0:
            financial_info.append(f"Original Bill Amount: ₹{rule_log['preExecutionTotalBillAmount']:,.2f}")

        if rule_log.get('postExecutionApprovedAmount', 0) > 0:
            financial_info.append(f"Final Approved Amount: ₹{rule_log['postExecutionApprovedAmount']:,.2f}")

        if rule_log.get('postExecutionTotalBillAmount', 0) > 0:
            financial_info.append(f"Final Bill Amount: ₹{rule_log['postExecutionTotalBillAmount']:,.2f}")

        if rule_log.get('postExectionTotalDeductionAmount', 0) > 0:
            financial_info.append(f"Total Deductions Applied: ₹{rule_log['postExectionTotalDeductionAmount']:,.2f}")

        return " | ".join(financial_info) if financial_info else "No financial impact"
    
    def format_config_rules(self, config_rules: List[Dict[str, Any]]) -> str:
        """Format configuration rules in a readable way"""
        if not config_rules:
            return "No configuration rules"
        
        formatted_rules = []
        for rule in config_rules:
            if rule.get('isToBeDisplayed', True):
                display_name = rule.get('displayName', 'Unknown')
                value = rule.get('value', 'N/A')
                claim_value = rule.get('claimValue', '')
                
                rule_text = f"{display_name}: {value}"
                if claim_value:
                    rule_text += f" (Claim Value: {claim_value})"
                formatted_rules.append(rule_text)
        
        return " | ".join(formatted_rules)
    
    def format_matching_config(self, matching_config: List[Dict[str, Any]]) -> str:
        """Format matching configuration in a readable way"""
        if not matching_config:
            return "No matching configuration"
        
        formatted_configs = []
        for config in matching_config:
            config_items = []
            for key, value in config.items():
                if value and str(value).strip():
                    config_items.append(f"{key}: {value}")
            
            if config_items:
                formatted_configs.append(" | ".join(config_items))
        
        return " || ".join(formatted_configs)
    
    def process_rule_engine_log(self, claim_no: int, rule_log: Dict[str, Any]) -> ProcessedClaimChunk:
        """Process a single rule engine log into a patient-friendly formatted chunk"""

        # Basic information with patient-friendly terms
        time_occurrence = self.parse_dotnet_date(rule_log.get('timeOfOccurrence', ''))
        category = self.get_patient_friendly_category(rule_log.get('category', 'Unknown'))
        sub_category = rule_log.get('subCategory', 'Unknown')
        display_name = rule_log.get('subCategoryDisplayName', 'Unknown')
        logger_type = rule_log.get('loggerType', 'Information')

        # Format log messages with patient context
        log_messages = rule_log.get('logMessages', [])
        formatted_messages = []
        for msg in log_messages:
            msg_time = self.parse_dotnet_date(msg.get('timeOfOccurrence', ''))
            # Filter out technical system messages, focus on patient-relevant info
            message = msg.get('message', '')
            if not self._is_technical_system_message(message):
                formatted_messages.append(f"[{msg_time}] {message}")

        # Format financial information
        financial_info = self.format_financial_amounts(rule_log)

        # Format configuration rules
        config_rules_text = self.format_config_rules(rule_log.get('configRuleLogObject', []))

        # Format matching configuration
        matching_config_text = self.format_matching_config(rule_log.get('matchingConfig', []))

        # Create patient-focused content
        content_parts = [
            f"BENEFIT PROCESSING: {display_name}",
            f"Benefit Type: {category} > {sub_category}",
            f"Processed On: {time_occurrence}",
            f"Processing Stage: {logger_type}",
            "",
            "FINANCIAL IMPACT ON YOUR CLAIM:",
            financial_info,
            "",
        ]

        if formatted_messages:
            content_parts.extend([
                "PROCESSING NOTES:",
                "\n".join(formatted_messages),
                "",
            ])

        if matching_config_text != "No matching configuration":
            content_parts.extend([
                "APPLICABLE POLICY RULES:",
                matching_config_text,
                "",
            ])

        if config_rules_text != "No configuration rules":
            content_parts.extend([
                "POLICY CONFIGURATION DETAILS:",
                config_rules_text,
                "",
            ])

        content = "\n".join(content_parts)
        
        # Create patient-focused metadata
        metadata = {
            "benefit_category": category,
            "specific_benefit_type": sub_category,
            "benefit_description": display_name,
            "processing_stage": logger_type,
            "processing_timestamp": time_occurrence,
            "has_financial_impact": any([
                rule_log.get('postExecutionApprovedAmount', 0) > 0,
                rule_log.get('postExecutionTotalBillAmount', 0) > 0,
                rule_log.get('postExectionTotalDeductionAmount', 0) > 0
            ]),
            "has_applicable_policy_rules": len(rule_log.get('matchingConfig', [])) > 0,
            "processing_notes_count": len([msg for msg in log_messages if not self._is_technical_system_message(msg.get('message', ''))]),
            "patient_relevant": True  # Flag to indicate this is patient-facing information
        }
        
        return ProcessedClaimChunk(
            claim_no=claim_no,
            chunk_id=str(uuid.uuid4()),
            chunk_type="rule_engine_log",
            content=content,
            metadata=metadata,
            timestamp=datetime.now().isoformat()
        )
    
    def process_general_log(self, claim_no: int, log: Dict[str, Any]) -> ProcessedClaimChunk:
        """Process a general log entry into a patient-friendly formatted chunk"""

        log_created_date = self.parse_dotnet_date(log.get('logCreatedDate', ''))
        type_of_log = log.get('typeOfLog', 'Unknown')
        log_data = log.get('data', [])

        # Transform log type to patient-friendly description
        friendly_log_type = self.get_patient_friendly_category(type_of_log)

        content_parts = [
            f"CLAIM ACTIVITY: {friendly_log_type}",
            f"Activity Date: {log_created_date}",
            "",
        ]

        if log_data:
            content_parts.append("ACTIVITY DETAILS:")
            for entry in log_data:
                entry_time = self.parse_dotnet_date(entry.get('timeOfOccurrence', ''))
                message = entry.get('message', '')
                logger_type = entry.get('loggerType', '0')

                # Filter out technical messages for patient view
                if not self._is_technical_system_message(message):
                    content_parts.append(f"[{entry_time}] [{logger_type}] {message}")
            content_parts.append("")

        content = "\n".join(content_parts)

        metadata = {
            "processing_activity_type": friendly_log_type,
            "activity_timestamp": log_created_date,
            "relevant_entries_count": len([entry for entry in log_data if not self._is_technical_system_message(entry.get('message', ''))]),
            "patient_relevant": True
        }
        
        return ProcessedClaimChunk(
            claim_no=claim_no,
            chunk_id=str(uuid.uuid4()),
            chunk_type="general_log",
            content=content,
            metadata=metadata,
            timestamp=datetime.now().isoformat()
        )
    
    def process_claims_data(self, claims_data: Dict[str, Any]) -> List[ProcessedClaimChunk]:
        """Process the entire claims data into formatted chunks"""
        
        processed_chunks = []
        
        # Parse the claims response
        claims_response = ClaimsResponse(**claims_data)
        
        for claim_data in claims_response.data:
            claim_no = claim_data.claimNo
            
            # Create a summary chunk for the claim
            summary_content = f"""CLAIM SUMMARY
Claim Number: {claim_no}
Total Logs: {len(claim_data.logs)}

LOG TYPES PRESENT:
{chr(10).join([f"- {log.typeOfLog}" for log in claim_data.logs])}
"""
            
            summary_chunk = ProcessedClaimChunk(
                claim_no=claim_no,
                chunk_id=str(uuid.uuid4()),
                chunk_type="claim_summary",
                content=summary_content,
                metadata={
                    "claim_no": claim_no,
                    "total_logs": len(claim_data.logs),
                    "log_types": [log.typeOfLog for log in claim_data.logs]
                },
                timestamp=datetime.now().isoformat()
            )
            processed_chunks.append(summary_chunk)
            
            # Process each log
            for log in claim_data.logs:
                # Process general log data
                if log.data:
                    general_chunk = self.process_general_log(claim_no, log.model_dump())
                    processed_chunks.append(general_chunk)

                # Process rule engine logs
                for rule_log in log.ruleEngineLogs:
                    rule_chunk = self.process_rule_engine_log(claim_no, rule_log.model_dump())
                    processed_chunks.append(rule_chunk)
        
        return processed_chunks
