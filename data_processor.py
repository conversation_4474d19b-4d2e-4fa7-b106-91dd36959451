import json
import uuid
from datetime import datetime
from typing import List, Dict, Any
from models import ClaimsResponse, ProcessedClaimChunk
import re

class ClaimsDataProcessor:
    def __init__(self):
        self.date_pattern = re.compile(r'/Date\((\d+)(?:[+-]\d{4})?\)/')
    
    def parse_dotnet_date(self, date_str: str) -> str:
        """Convert .NET date format to readable format"""
        match = self.date_pattern.search(date_str)
        if match:
            timestamp = int(match.group(1)) / 1000  # Convert to seconds
            return datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
        return date_str
    
    def format_financial_amounts(self, rule_log: Dict[str, Any]) -> str:
        """Format financial information in a readable way"""
        financial_info = []
        
        if rule_log.get('preExecutionApprovedAmount', 0) > 0:
            financial_info.append(f"Pre-execution Approved: ₹{rule_log['preExecutionApprovedAmount']:,.2f}")
        
        if rule_log.get('preExecutionTotalBillAmount', 0) > 0:
            financial_info.append(f"Pre-execution Bill: ₹{rule_log['preExecutionTotalBillAmount']:,.2f}")
        
        if rule_log.get('postExecutionApprovedAmount', 0) > 0:
            financial_info.append(f"Post-execution Approved: ₹{rule_log['postExecutionApprovedAmount']:,.2f}")
        
        if rule_log.get('postExecutionTotalBillAmount', 0) > 0:
            financial_info.append(f"Post-execution Bill: ₹{rule_log['postExecutionTotalBillAmount']:,.2f}")
        
        if rule_log.get('postExectionTotalDeductionAmount', 0) > 0:
            financial_info.append(f"Total Deductions: ₹{rule_log['postExectionTotalDeductionAmount']:,.2f}")
        
        return " | ".join(financial_info) if financial_info else "No financial data"
    
    def format_config_rules(self, config_rules: List[Dict[str, Any]]) -> str:
        """Format configuration rules in a readable way"""
        if not config_rules:
            return "No configuration rules"
        
        formatted_rules = []
        for rule in config_rules:
            if rule.get('isToBeDisplayed', True):
                display_name = rule.get('displayName', 'Unknown')
                value = rule.get('value', 'N/A')
                claim_value = rule.get('claimValue', '')
                
                rule_text = f"{display_name}: {value}"
                if claim_value:
                    rule_text += f" (Claim Value: {claim_value})"
                formatted_rules.append(rule_text)
        
        return " | ".join(formatted_rules)
    
    def format_matching_config(self, matching_config: List[Dict[str, Any]]) -> str:
        """Format matching configuration in a readable way"""
        if not matching_config:
            return "No matching configuration"
        
        formatted_configs = []
        for config in matching_config:
            config_items = []
            for key, value in config.items():
                if value and str(value).strip():
                    config_items.append(f"{key}: {value}")
            
            if config_items:
                formatted_configs.append(" | ".join(config_items))
        
        return " || ".join(formatted_configs)
    
    def process_rule_engine_log(self, claim_no: int, rule_log: Dict[str, Any]) -> ProcessedClaimChunk:
        """Process a single rule engine log into a formatted chunk"""
        
        # Basic information
        time_occurrence = self.parse_dotnet_date(rule_log.get('timeOfOccurrence', ''))
        category = rule_log.get('category', 'Unknown')
        sub_category = rule_log.get('subCategory', 'Unknown')
        display_name = rule_log.get('subCategoryDisplayName', 'Unknown')
        logger_type = rule_log.get('loggerType', 'Information')
        
        # Format log messages
        log_messages = rule_log.get('logMessages', [])
        formatted_messages = []
        for msg in log_messages:
            msg_time = self.parse_dotnet_date(msg.get('timeOfOccurrence', ''))
            formatted_messages.append(f"[{msg_time}] {msg.get('message', '')}")
        
        # Format financial information
        financial_info = self.format_financial_amounts(rule_log)
        
        # Format configuration rules
        config_rules_text = self.format_config_rules(rule_log.get('configRuleLogObject', []))
        
        # Format matching configuration
        matching_config_text = self.format_matching_config(rule_log.get('matchingConfig', []))
        
        # Create comprehensive content
        content_parts = [
            f"CLAIM PROCESSING RULE: {display_name}",
            f"Category: {category} > {sub_category}",
            f"Execution Time: {time_occurrence}",
            f"Logger Type: {logger_type}",
            "",
            "FINANCIAL IMPACT:",
            financial_info,
            "",
        ]
        
        if formatted_messages:
            content_parts.extend([
                "EXECUTION MESSAGES:",
                "\n".join(formatted_messages),
                "",
            ])
        
        if matching_config_text != "No matching configuration":
            content_parts.extend([
                "MATCHING CONFIGURATION:",
                matching_config_text,
                "",
            ])
        
        if config_rules_text != "No configuration rules":
            content_parts.extend([
                "CONFIGURATION RULES:",
                config_rules_text,
                "",
            ])
        
        content = "\n".join(content_parts)
        
        # Create metadata
        metadata = {
            "category": category,
            "sub_category": sub_category,
            "display_name": display_name,
            "logger_type": logger_type,
            "execution_time": time_occurrence,
            "has_financial_impact": any([
                rule_log.get('postExecutionApprovedAmount', 0) > 0,
                rule_log.get('postExecutionTotalBillAmount', 0) > 0,
                rule_log.get('postExectionTotalDeductionAmount', 0) > 0
            ]),
            "has_matching_config": len(rule_log.get('matchingConfig', [])) > 0,
            "message_count": len(log_messages)
        }
        
        return ProcessedClaimChunk(
            claim_no=claim_no,
            chunk_id=str(uuid.uuid4()),
            chunk_type="rule_engine_log",
            content=content,
            metadata=metadata,
            timestamp=datetime.now().isoformat()
        )
    
    def process_general_log(self, claim_no: int, log: Dict[str, Any]) -> ProcessedClaimChunk:
        """Process a general log entry into a formatted chunk"""
        
        log_created_date = self.parse_dotnet_date(log.get('logCreatedDate', ''))
        type_of_log = log.get('typeOfLog', 'Unknown')
        log_data = log.get('data', [])
        
        content_parts = [
            f"GENERAL LOG: {type_of_log}",
            f"Created: {log_created_date}",
            "",
        ]
        
        if log_data:
            content_parts.append("LOG ENTRIES:")
            for entry in log_data:
                entry_time = self.parse_dotnet_date(entry.get('timeOfOccurrence', ''))
                message = entry.get('message', '')
                logger_type = entry.get('loggerType', '0')
                content_parts.append(f"[{entry_time}] [{logger_type}] {message}")
            content_parts.append("")
        
        content = "\n".join(content_parts)
        
        metadata = {
            "type_of_log": type_of_log,
            "created_date": log_created_date,
            "entry_count": len(log_data)
        }
        
        return ProcessedClaimChunk(
            claim_no=claim_no,
            chunk_id=str(uuid.uuid4()),
            chunk_type="general_log",
            content=content,
            metadata=metadata,
            timestamp=datetime.now().isoformat()
        )
    
    def process_claims_data(self, claims_data: Dict[str, Any]) -> List[ProcessedClaimChunk]:
        """Process the entire claims data into formatted chunks"""
        
        processed_chunks = []
        
        # Parse the claims response
        claims_response = ClaimsResponse(**claims_data)
        
        for claim_data in claims_response.data:
            claim_no = claim_data.claimNo
            
            # Create a summary chunk for the claim
            summary_content = f"""CLAIM SUMMARY
Claim Number: {claim_no}
Total Logs: {len(claim_data.logs)}

LOG TYPES PRESENT:
{chr(10).join([f"- {log.typeOfLog}" for log in claim_data.logs])}
"""
            
            summary_chunk = ProcessedClaimChunk(
                claim_no=claim_no,
                chunk_id=str(uuid.uuid4()),
                chunk_type="claim_summary",
                content=summary_content,
                metadata={
                    "claim_no": claim_no,
                    "total_logs": len(claim_data.logs),
                    "log_types": [log.typeOfLog for log in claim_data.logs]
                },
                timestamp=datetime.now().isoformat()
            )
            processed_chunks.append(summary_chunk)
            
            # Process each log
            for log in claim_data.logs:
                # Process general log data
                if log.data:
                    general_chunk = self.process_general_log(claim_no, log.dict())
                    processed_chunks.append(general_chunk)
                
                # Process rule engine logs
                for rule_log in log.ruleEngineLogs:
                    rule_chunk = self.process_rule_engine_log(claim_no, rule_log.dict())
                    processed_chunks.append(rule_chunk)
        
        return processed_chunks
