#!/usr/bin/env python3
"""
Test script to verify the new data structure processing works correctly
"""

import json
from data_processor import ClaimsDataProcessor
from models import ClaimsResponse

def test_new_structure():
    """Test processing with the new direct array structure"""
    
    # Load the new response.json structure
    with open('response.json', 'r') as f:
        new_structure_data = json.load(f)
    
    print("Testing new structure processing...")
    print(f"Data type: {type(new_structure_data)}")
    print(f"Number of claims: {len(new_structure_data) if isinstance(new_structure_data, list) else 'N/A'}")
    
    # Test ClaimsResponse.from_response method
    try:
        claims_response = ClaimsResponse.from_response(new_structure_data)
        claims = claims_response.get_claims()
        print(f"Successfully parsed {len(claims)} claims")
        
        # Test data processor
        processor = ClaimsDataProcessor()
        processed_chunks = processor.process_claims_data(new_structure_data)
        print(f"Successfully processed {len(processed_chunks)} chunks")
        
        # Show sample chunk
        if processed_chunks:
            sample_chunk = processed_chunks[0]
            print(f"\nSample chunk:")
            print(f"Claim No: {sample_chunk.claim_no}")
            print(f"Chunk Type: {sample_chunk.chunk_type}")
            print(f"Content Preview: {sample_chunk.content[:200]}...")
            print(f"Metadata: {sample_chunk.metadata}")
        
        return True
        
    except Exception as e:
        print(f"Error processing new structure: {e}")
        return False

def test_old_structure():
    """Test processing with the old wrapped structure"""
    
    # Load the new structure and wrap it in the old format
    with open('response.json', 'r') as f:
        new_structure_data = json.load(f)
    
    old_structure_data = {"data": new_structure_data}
    
    print("\nTesting old structure processing...")
    print(f"Data type: {type(old_structure_data)}")
    
    try:
        claims_response = ClaimsResponse.from_response(old_structure_data)
        claims = claims_response.get_claims()
        print(f"Successfully parsed {len(claims)} claims from old format")
        
        # Test data processor
        processor = ClaimsDataProcessor()
        processed_chunks = processor.process_claims_data(old_structure_data)
        print(f"Successfully processed {len(processed_chunks)} chunks from old format")
        
        return True
        
    except Exception as e:
        print(f"Error processing old structure: {e}")
        return False

if __name__ == "__main__":
    print("Testing Claims Data Structure Processing")
    print("=" * 50)
    
    new_success = test_new_structure()
    old_success = test_old_structure()
    
    print("\n" + "=" * 50)
    print("Test Results:")
    print(f"New structure processing: {'✅ PASS' if new_success else '❌ FAIL'}")
    print(f"Old structure processing: {'✅ PASS' if old_success else '❌ FAIL'}")
    
    if new_success and old_success:
        print("\n🎉 All tests passed! The data processor supports both old and new structures.")
    else:
        print("\n⚠️  Some tests failed. Please check the implementation.")
