[{"_debug_info": {"total_claims": 1, "filtering_rule": "Only logs with typeOfLog: \"Claim\" will be processed", "alteration_timestamp": "2025-06-13T19:52:44.272179"}}, {"claimNo": 123456, "logs": [{"typeOfLog": "<PERSON><PERSON><PERSON>", "logCreatedDate": "/Date(1640995200000)/", "data": [{"message": "Claim log", "timeOfOccurrence": "/Date(1640995200000)/", "loggerType": "1"}], "ruleEngineLogs": [], "_will_be_processed": true}, {"typeOfLog": "Decision", "logCreatedDate": "/Date(1640995200000)/", "data": [{"message": "Decision log", "timeOfOccurrence": "/Date(1640995200000)/", "loggerType": "1"}], "ruleEngineLogs": [], "_will_be_processed": false, "_filtered_out": "Excluded: typeOfLog is 'Decision', not 'Claim'"}], "_processing_metadata": {"filtered_for_claim_logs_only": true, "processing_timestamp": "2025-06-13T19:52:44.272169", "total_logs": 2, "claim_logs_count": 1}}]