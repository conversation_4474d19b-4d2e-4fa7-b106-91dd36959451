[{"_debug_info": {"total_claims": 1, "filtering_rule": "Only logs with typeOfLog: \"Claim\" will be processed", "alteration_timestamp": "2025-06-13T20:13:17.732945"}}, {"claimNo": 131420765, "logs": [{"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": [{"category": "BaseConfiguration", "subCategory": "DefautChecks", "subCategoryDisplayName": "Default Configuration", "logMessages": [{"message": "Claim types considered for discount override: PreHosp,PostHosp,HospBenefit,HealthCheckUp,RI,ReOpenClaim"}, {"message": "Override Discount By <PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON> not executed as no matching rule found."}, {"message": "Default Configuration - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 0.0, "postExectionTotalDeductionAmount": 0.0, "configRuleLogObject": []}, {"category": "BaseConfiguration", "subCategory": "PolicyConfigIncaseOfDeath", "subCategoryDisplayName": "Exception On Death Case", "logMessages": [{"message": "Exception On Death Case - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 0.0, "postExectionTotalDeductionAmount": 0.0, "configRuleLogObject": []}, {"category": "BaseConfiguration", "subCategory": "OverideDiscount", "subCategoryDisplayName": "Override Discount By Claim Type", "logMessages": [{"message": "Override Discount By <PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON> not executed as no matching rule found."}, {"message": "Override Discount By Claim Type - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 0.0, "postExectionTotalDeductionAmount": 0.0, "configRuleLogObject": []}, {"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "logMessages": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 8905.18"}, {"message": "HMOU Discount Calculated: 9402.08"}, {"message": "MAX Discount Calculated: 9402.08"}, {"message": "Discount Calculation Is Goving with HMOU"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Pharmacy & Medicine Charges: 2760.22,Hospital Charges: 1827.11,Consultant Charges: 1299.90,Surgery Charges: 94.74,Investigation & Lab Charges: 2460.50,Miscellaneous Charges: 959.57"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}], "matchingConfig": [{"claimType": "None"}, {"claimType": "PreAuth"}, {"claimType": "Cashless"}], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 145307.6, "postExectionTotalDeductionAmount": 0.0, "configRuleLogObject": [{"displayName": "ClaimType", "value": "None", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "ClaimType", "value": "PreAuth", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "ClaimType", "value": "Cashless", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}]}, {"category": "NonMedicalExpense", "subCategory": "ManualOverRideBills", "subCategoryDisplayName": "Manual OverRide Bills", "logMessages": [{"message": "Manual OverRide Bills - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 145307.6, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"category": "NonMedicalExpense", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "logMessages": [{"message": "Standard (IRDAI) - Execution starts"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:IV Cannula, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Accu Check ( Glucometery/ Strips), Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Face Mask, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Band Aids, Bandages, Sterile Injections, Needles, Syringes, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Face Mask, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Band Aids, Bandages, Sterile Injections, Needles, Syringes, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Band Aids, Bandages, Sterile Injections, Needles, Syringes, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Miscellaneous Charges, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Miscellaneous Charges, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Nutrition Planning Charges - Dietician Charges- Diet Charges, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Miscellaneous Charges, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Accu Check ( Glucometery/ Strips), Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Miscellaneous Charges, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Accu Check ( Glucometery/ Strips), Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Accu Check ( Glucometery/ Strips), Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Band Aids, Bandages, Sterile Injections, Needles, Syringes, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Medical Records, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Band Aids, Bandages, Sterile Injections, Needles, Syringes, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON>, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Accu Check ( Glucometery/ Strips), Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Accu Check ( Glucometery/ Strips), Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Accu Check ( Glucometery/ Strips), Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Face Mask, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Face Mask, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Face Mask, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Bed Under Pad Charges, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Band Aids, Bandages, Sterile Injections, Needles, Syringes, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Accu Check ( Glucometery/ Strips), Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON>, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Face Mask, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Face Mask, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Bed Under Pad Charges, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:ECG Electrodes, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Flexi Mask, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Caps, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Band Aids, Bandages, Sterile Injections, Needles, Syringes, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Band Aids, Bandages, Sterile Injections, Needles, Syringes, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Accu Check ( Glucometery/ Strips), Marking Bill Item Non Payable"}, {"message": "Standard (IRDAI) - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 145307.6, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"category": "NonMedicalExpense", "subCategory": "HandelNMEByPreAuthType", "subCategoryDisplayName": "Estimate For Initial", "logMessages": [{"message": "*****Calulating CalculateNMEDeduction Started*****"}, {"message": "Estimate For Initial - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"category": "NonMedicalExpense", "subCategory": "CapAmbulanceCharges", "subCategoryDisplayName": "Ambulance Limit By Amount", "logMessages": [{"message": "No deduction found in the historic claims"}, {"message": "Max Payable Ambulance Charge: 2500"}, {"message": "Current Payable Ambulance Charge if Any applicable: 2500"}, {"message": "Ambulance Limit By Amount - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"category": "NonMedicalExpense", "subCategory": "MarkAllNMEsAsPayable", "subCategoryDisplayName": "Pay All NMEs", "logMessages": [{"message": "Rules not applied as no matching data found"}, {"message": "Pay All NMEs - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"category": "NonMedicalExpense", "subCategory": "RIPrePostNMEDeductions", "subCategoryDisplayName": "Reimbursement Pre-Post Deductions", "logMessages": [{"message": "Reimbursement Pre-Post Deductions - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"category": "NonMedicalExpense", "subCategory": "VariantNotConfigured", "subCategoryDisplayName": "Condition To Be Reviewed", "logMessages": [{"message": "Condition To Be Reviewed - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"category": "NonMedicalExpense", "subCategory": "OverrideNMEParticularsAmount", "subCategoryDisplayName": "NME limit by Subcharge", "logMessages": [{"message": "Limit applicable subcharge head: 1"}, {"message": "Matching Config Count: 1"}, {"message": "Matching Config Count: 1"}, {"message": "NME limit by Subcharge - Execution starts"}, {"message": "NME limit by Subcharge - Execution ends"}], "matchingConfig": [{"nmeConfigId": "0", "schemeId": "0", "policyId": "0", "payableType": "<PERSON>", "payableSubType": "Air Ambulance Charges", "isPayable": "True", "payableLimit": "100000", "quatityLimit": "-1", "quantityPriceLimit": "-1", "payablePercentageLimit": "0", "isLower": "False", "deductionLevel": "<PERSON><PERSON><PERSON>", "isActive": "True"}], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 119565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "NMEConfigId", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}, {"displayName": "SchemeId", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}, {"displayName": "PolicyId", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}, {"displayName": "PayableType", "value": "<PERSON>", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "PayableSubType", "value": "Air Ambulance Charges", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "IsPayable", "value": "Applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "PayableLimit", "value": "100000", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "QuatityLimit", "value": "-1", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "QuantityPriceLimit", "value": "-1", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "PayablePercentageLimit", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "DeductionLevel", "value": "<PERSON><PERSON><PERSON>", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "IsActive", "value": "Applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}]}, {"category": "NonMedicalExpense", "subCategory": "ManualOverRideBills", "subCategoryDisplayName": "Manual OverRide Bills", "logMessages": [{"message": "Manual OverRide Bills - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"category": "Repudiation", "subCategory": "ExclusionByGenderAilment", "subCategoryDisplayName": "Gender and ailment", "logMessages": [{"message": "Gender and ailment - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"category": "Repudiation", "subCategory": "OnNumberOfChildren", "subCategoryDisplayName": "Number of living children allowed for maternity coverage", "logMessages": [{"message": "Number of living children allowed for maternity coverage - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"category": "Repudiation", "subCategory": "ExclusionByAilment", "subCategoryDisplayName": "Repudiation By Ailment Category", "logMessages": [{"message": "Repudiation By Ailment Category - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"category": "Repudiation", "subCategory": "RepudiationByRefractiveError", "subCategoryDisplayName": "Refractive Error", "logMessages": [{"message": "Refractive Error - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "logMessages": [{"message": "Matching Config Count: 1"}, {"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}], "matchingConfig": [{"relation to Primary beneficiary": "Father, <PERSON><PERSON><PERSON>, Mother, <PERSON><PERSON><PERSON>", "grade": "any", "check PED Applicable For Beneficiary": "True", "skip For Demise": "False", "skip For Emergency Admission": "False", "number of days for waiting period": "365", "benefitScope": "Policy"}], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 119565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "Relation to Primary beneficiary", "value": "Father,<PERSON><PERSON><PERSON>,Mother,<PERSON><PERSON><PERSON>", "isFilterApplicable": true, "defaultValue": "ALL", "isToBeDisplayed": true, "claimValue": "<PERSON><PERSON><PERSON>"}, {"displayName": "Grade", "value": "any", "isFilterApplicable": true, "defaultValue": "ANY", "isToBeDisplayed": true, "claimValue": "Gold Plus"}, {"displayName": "Check PED Applicable For Beneficiary", "value": "Applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Skip For Demise", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Skip For Emergency Admission", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Number of days for waiting period", "value": "365", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "BenefitScope", "value": "Policy", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}]}, {"category": "Repudiation", "subCategory": "DenyObesity", "subCategoryDisplayName": "Obesity", "logMessages": [{"message": "Obesity - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"category": "Repudiation", "subCategory": "WaitingPeriodByAilmentsCategory", "subCategoryDisplayName": "Waiting Period by Ailment category", "logMessages": [{"message": "Waiting Period by Ailment category - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"category": "Repudiation", "subCategory": "ExclusionByProcedures", "subCategoryDisplayName": "Repudiation By Procedure", "logMessages": [{"message": "Repudiation By Procedure - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"category": "Repudiation", "subCategory": "WaitingPeriodByProcedures", "subCategoryDisplayName": "Waiting Period by Procedure", "logMessages": [{"message": "Waiting Period by Procedure - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"category": "Repudiation", "subCategory": "RepudiationByHospital", "subCategoryDisplayName": "Repudiation By Hospital And Claim Type", "logMessages": [{"message": "Matching Config Count: 1"}, {"message": "Matching Config Count: 1"}, {"message": "Repudiation By Hospital And Claim Type - Execution starts"}, {"message": "Repudiation By Hospital And Claim Type - Execution ends"}], "matchingConfig": [{"relation To Primary Beneficiary": "ALL", "grade": "gold, gold plus, gold plus_r, gold_r, silver, silver_r, platinum, platinum_r", "claim type": "Cashless, PreAuth", "skip For Network Hospital": "False", "skip For PPN Hospital": "False", "hospital": "287585", "skip for Demise": "False", "benefitScope": "Policy"}], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 119565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "Relation To Primary Beneficiary", "value": "ALL", "isFilterApplicable": true, "defaultValue": "ALL", "isToBeDisplayed": true, "claimValue": "<PERSON><PERSON><PERSON>"}, {"displayName": "Grade", "value": "gold,gold plus,gold plus_r,gold_r,silver,silver_r,platinum,platinum_r", "isFilterApplicable": true, "defaultValue": "ANY", "isToBeDisplayed": true, "claimValue": "Gold Plus"}, {"displayName": "Claim type", "value": "Cashless,PreAuth", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "<PERSON><PERSON> For Network Hospital", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Skip For PPN Hospital", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Hospital", "value": "287585", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Skip for Demise", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "BenefitScope", "value": "Policy", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}]}, {"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "logMessages": [{"message": "Matching Config Count: 2"}, {"message": "Matching Config Count: 2"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:11100.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:6500.0"}, {"message": "Tariff ICU Room Rent From Master:6500.0"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}], "matchingConfig": [{"claimType": "PreAuth"}, {"claimType": "Cashless"}], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 119565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "ClaimType", "value": "PreAuth", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "ClaimType", "value": "Cashless", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "logMessages": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category SemiPvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:11100.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category Found Under Sharing Category"}, {"message": "Found room rent details under SHARING Category"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category not found Under PRIVATE Category"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer nursing,room,room rent,room rent (per day),associated room charges,duty doctor,injection / iv administration charge,gst on room rent"}, {"message": "ICU : Room Rent based on insurer icu charges,icu nursing charges,icu room rent,icu injection / iv administration charge,intencivist charges,icu monitoring charges"}, {"message": "Normal Room Rent Lenght of Stay : 4.00"}, {"message": "ICU Room Rent Lenght of Stay : 0"}, {"message": "Non-Tariff bill as insurer Perday ChargeType :gst on room rent , Units : 4.00, PerDay Price : 604"}, {"message": "Non-Tariff bill as insurer Perday ChargeType :gst on room rent , Units : 4.00, PerDay Price : 604"}, {"message": "Tariff Bill Price as per insurer :5500.0"}, {"message": "Non-Tariff bill Price as per insurer :302"}, {"message": "Total Room Price as per insurer :5802.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 5802.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 5802.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 5802.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 4.00"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 6802.26"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 23208.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 85.30%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 85.30%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}], "matchingConfig": [{"type of  Room": "Normal", "provider Type": "False", "propotionateApplicable": "True", "skip Proportionate On DeathCase": "False", "skip Proportionate On AccidentCase": "False", "topUp Configured": "False", "relation to Primary beneficiary": "Father, <PERSON><PERSON><PERSON>, Mother, <PERSON><PERSON><PERSON>", "grade": "gold plus, gold", "min Age": "0", "max Age": "0", "room Value Limit Defined By": "Amount", "room Value Type": "5802.0", "room Capped Limit": "0", "proportionate Limit Defined By": "Amount", "proportionate Limit Defined By Value": "0", "min SI": "0", "max SI": "0", "benefitScope": "Policy"}], "preExecutionApprovedAmount": 110163.96, "preExecutionTotalBillAmount": 119565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 110163.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "Type of  Room", "value": "Normal", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Provider Type", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "PropotionateApplicable", "value": "Applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Skip Proportionate On DeathCase", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Skip Proportionate On AccidentCase", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "TopUp Configured", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Relation to Primary beneficiary", "value": "Father,<PERSON><PERSON><PERSON>,Mother,<PERSON><PERSON><PERSON>", "isFilterApplicable": true, "defaultValue": "ALL", "isToBeDisplayed": true, "claimValue": "<PERSON><PERSON><PERSON>"}, {"displayName": "Grade", "value": "gold plus,gold", "isFilterApplicable": true, "defaultValue": "ANY", "isToBeDisplayed": true, "claimValue": "Gold Plus"}, {"displayName": "Min Age", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "61"}, {"displayName": "Max Age", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "61"}, {"displayName": "Room Value Limit Defined By", "value": "Amount", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Room Value Type", "value": "5802.0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Room Capped Limit", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}, {"displayName": "Proportionate Limit Defined By", "value": "Amount", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Proportionate Limit Defined By Value", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}, {"displayName": "Min SI", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "1600000.0"}, {"displayName": "Max SI", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "1600000.0"}, {"displayName": "BenefitScope", "value": "Policy", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}]}, {"category": "PropotinateDeduction", "subCategory": "VariantNotConfigured", "subCategoryDisplayName": "Condition To Be Reviewed", "logMessages": [{"message": "Condition To Be Reviewed - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 103439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "logMessages": [{"message": "Matching Config Count: 1"}, {"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}], "matchingConfig": [{"relation To Primary Beneficiary": "ALL", "grade": "gold, gold plus, platinum, silver", "claim type": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>", "intimation Days": "3", "copay Defined By": "PecentageOfAdmissibleAmount", "copay Defined Value(Percentage/Amount)": "10", "upper/Lower Copay Limit For Comparison": "0", "skip For Emergency Admission": "True", "is Min/Max Copay Applicable Per Person": "False", "is Copay Applicable Per Admission Basis": "False", "copay Limit Applicable Per Family": "False", "dedution Remarks": "INTIMATION_COPAY", "benefitScope": "Policy"}], "preExecutionApprovedAmount": 103439.96, "preExecutionTotalBillAmount": 112842.58, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 103439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "Relation To Primary Beneficiary", "value": "ALL", "isFilterApplicable": true, "defaultValue": "ALL", "isToBeDisplayed": true, "claimValue": "<PERSON><PERSON><PERSON>"}, {"displayName": "Grade", "value": "gold,gold plus,platinum,silver", "isFilterApplicable": true, "defaultValue": "ANY", "isToBeDisplayed": true, "claimValue": "Gold Plus"}, {"displayName": "Claim type", "value": "<PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>", "isFilterApplicable": true, "defaultValue": "None", "isToBeDisplayed": true, "claimValue": "PreAuth"}, {"displayName": "Intimation Days", "value": "3", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Copay Defined By", "value": "PecentageOfAdmissibleAmount", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Copay Defined Value(Percentage/Amount)", "value": "10", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Upper/Lower Copay Limit For Comparison", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}, {"displayName": "Skip For Emergency Admission", "value": "Applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Is Min/Max Copay Applicable Per Person", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Is Copay Applicable Per Admission Basis", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Copay Limit Applicable Per Family", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Dedution Remarks", "value": "INTIMATION_COPAY", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "BenefitScope", "value": "Policy", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}]}, {"category": "Copay", "subCategory": "CopayOnNonNetwork", "subCategoryDisplayName": "Non-Network Hospital", "logMessages": [{"message": "Non-Network Hospital - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 103439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "logMessages": [{"message": "Matching Config Count: 1"}, {"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 10%  of admissible amount 103439.96 and flat amount 10000 is 10000"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 10000"}, {"message": "Copay deduction amount is 10000"}, {"message": "Copay deduction amount is: 10000"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 10%  of admissible amount 103439.96 and flat amount 10000 is 10000 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}], "matchingConfig": [{"relationship": "ALL", "grade": "gold plus, gold plus_r", "minimum Age": "0", "maximum Age": "0", "min SI": "0", "max SI": "0", "min Claim amount": "0", "max Claim amount": "0", "min Admissible amount": "0", "max Admissible amount": "0", "exculding Procedures": "3119, 3163, 3165, 3467, 3861, 4121, 4430, 4436, 4513, 4560, 4591, 4599, 4671", "hospital Network/Non-Network": "False", "copay Type": "MinOfPercentageOfAdmissibleAndCappedAmount", "copay Percentage": "10", "capped Value": "10000", "skip For Emergency Admission": "False", "applicable on claim event basis": "True", "is Family Floater": "False", "applicable on claim Member basis": "False", "no. of Days": "0", "check PED Applicable For Beneficiary": "False", "isCopayDueToAnyoneIllness": "False", "exculdingAilmentsCategoryOrAilment": "IVF_FOR_INFERTILITY,INFERTILITY,PSYCHIATRIC,GENETICDISORDER,M<PERSON><PERSON><PERSON>LE_SCLEROSIS,MATERNITY_COMPLICATIONS,MATERNITY_MULTIPLE_DELIVERY,MATER<PERSON><PERSON>_NORMAL,MATE<PERSON><PERSON>TY_LSCS,TWIN_DELIVERY,TRIPLET_DELIVERY,MORE_THAN_TRIPLET_DELIVERY,PRE_AND_POST_NATAL,Mental_disorder", "dedution Remarks": "MANDATORY_COPAY", "sum Insured Utilization": "0", "benefitScope": "Policy"}], "preExecutionApprovedAmount": 103439.96, "preExecutionTotalBillAmount": 112842.58, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 103439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "Relationship", "value": "ALL", "isFilterApplicable": true, "defaultValue": "ALL", "isToBeDisplayed": true, "claimValue": "<PERSON><PERSON><PERSON>"}, {"displayName": "Grade", "value": "gold plus,gold plus_r", "isFilterApplicable": true, "defaultValue": "ANY", "isToBeDisplayed": true, "claimValue": "Gold Plus"}, {"displayName": "Minimum Age", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "61"}, {"displayName": "Maximum Age", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "61"}, {"displayName": "Min SI", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "1600000.0"}, {"displayName": "Max SI", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "1600000.0"}, {"displayName": "Min Claim amount", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "145307.0"}, {"displayName": "Max Claim amount", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "145307.0"}, {"displayName": "Min Admissible amount", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "112842"}, {"displayName": "Max Admissible amount", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "112842"}, {"displayName": "Exculding Procedures", "value": "3119,3163,3165,3467,3861,4121,4430,4436,4513,4560,4591,4599,4671", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": true, "claimValue": "4564"}, {"displayName": "Hospital Network/Non-Network", "value": "Not applicable", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": true, "claimValue": "True"}, {"displayName": "Copay Type", "value": "MinOfPercentageOfAdmissibleAndCappedAmount", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Copay Per<PERSON>age", "value": "10", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Capped Value", "value": "10000", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Skip For Emergency Admission", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Applicable on claim event basis", "value": "Applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Is Family Floater", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Applicable on claim Member basis", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "No. of Days", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}, {"displayName": "Check PED Applicable For Beneficiary", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "isCopayDueToAnyoneIllness", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "ExculdingAilmentsCategoryOrAilment", "value": "IVF_FOR_INFERTILITY,INFERTILITY,PSYCHIATRIC,GENETICDISORDER,M<PERSON><PERSON><PERSON>LE_SCLEROSIS,MATERNITY_COMPLICATIONS,MATERNITY_MULTIPLE_DELIVERY,MATER<PERSON><PERSON>_NORMAL,MATE<PERSON><PERSON>TY_LSCS,TWIN_DELIVERY,TRIPLET_DELIVERY,MORE_THAN_TRIPLET_DELIVERY,PRE_AND_POST_NATAL,Mental_disorder", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": true, "claimValue": "INFECTIOUS_BACTERIAL_VIRAL_MEDICAL_MGMT"}, {"displayName": "Dedution Remarks", "value": "MANDATORY_COPAY", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Sum Insured Utilization", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}, {"displayName": "BenefitScope", "value": "Policy", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}]}, {"category": "Copay", "subCategory": "RelationGradeAdmissibleAmount", "subCategoryDisplayName": "Copay By Admissible Amount", "logMessages": [{"message": "Copay By Admissible Amount - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"category": "Copay", "subCategory": "VariantNotConfigured", "subCategoryDisplayName": "Condition To Be Reviewed", "logMessages": [{"message": "Condition To Be Reviewed - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"category": "Copay", "subCategory": "RelationGradeAilment", "subCategoryDisplayName": "Ailment Category", "logMessages": [{"message": "Ailment Category - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"category": "AilmentCapping", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "logMessages": [{"message": "Standard (IRDAI) - Execution starts"}, {"message": "Standard (IRDAI) - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 93439.96, "preExecutionTotalBillAmount": 112842.58, "preExectionTotalDeductionAmount": 19402.04, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"category": "AilmentCapping", "subCategory": "AilmentCappingByGrade", "subCategoryDisplayName": "Ailment Category", "logMessages": [{"message": "Ailment Category - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"category": "AilmentCapping", "subCategory": "ProcedureCappingByApproachAndPostion", "subCategoryDisplayName": "Position and Approach", "logMessages": [{"message": "Position and Approach - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"category": "AilmentCapping", "subCategory": "AilmentProcedureCappingBySIType", "subCategoryDisplayName": "Ailment Category and Procedure Combination", "logMessages": [{"message": "Ailment Category and Procedure Combination - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"category": "AilmentCapping", "subCategory": "ProcedureCappingByGrade", "subCategoryDisplayName": "Procedure", "logMessages": [{"message": "Procedure - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"category": "AilmentCapping", "subCategory": "AilmentCappingByClaimType", "subCategoryDisplayName": "Ailment Capping by <PERSON><PERSON><PERSON>", "logMessages": [{"message": "Ailment Capping by <PERSON><PERSON>m Type - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"category": "AilmentCapping", "subCategory": "MaternityWellnessBabyCapping", "subCategoryDisplayName": "Maternity, wellness baby care & Pre-Post natal", "logMessages": [{"message": "Maternity, wellness baby care & Pre-Post natal - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"category": "AilmentCapping", "subCategory": "VariantNotConfigured", "subCategoryDisplayName": "Condition To Be Reviewed", "logMessages": [{"message": "Condition To Be Reviewed - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"category": "SIRestriction", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "logMessages": [{"message": "Standard - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"category": "SIRestriction", "subCategory": "OtherHealthCareSIRestriction", "subCategoryDisplayName": "SI Restriction By Health Care Types", "logMessages": [{"message": "SI Restriction By Health Care Types - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"category": "PolicyBenefits", "subCategory": "NotApplicable", "subCategoryDisplayName": "Not Applicable", "logMessages": [{"message": "Not Applicable - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"category": "UCRDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "logMessages": [{"message": "*****Calculation On SOC Started*****"}, {"message": "NO SOC Details Found"}, {"message": "*****Calculation On SOC Ends*****"}, {"message": "Standard - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 93439.96, "postExecutionTotalBillAmount": 112842.58, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}], "_will_be_processed": true}, {"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": [{"category": "BaseConfiguration", "subCategory": "DefautChecks", "subCategoryDisplayName": "Default Configuration", "logMessages": [{"message": "Claim types considered for discount override: PreHosp,PostHosp,HospBenefit,HealthCheckUp,RI,ReOpenClaim"}, {"message": "Override Discount By <PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON> not executed as no matching rule found."}, {"message": "Default Configuration - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 0.0, "postExectionTotalDeductionAmount": 0.0, "configRuleLogObject": []}, {"category": "BaseConfiguration", "subCategory": "PolicyConfigIncaseOfDeath", "subCategoryDisplayName": "Exception On Death Case", "logMessages": [{"message": "Exception On Death Case - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 0.0, "postExectionTotalDeductionAmount": 0.0, "configRuleLogObject": []}, {"category": "BaseConfiguration", "subCategory": "OverideDiscount", "subCategoryDisplayName": "Override Discount By Claim Type", "logMessages": [{"message": "Override Discount By <PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON> not executed as no matching rule found."}, {"message": "Override Discount By Claim Type - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 0.0, "postExectionTotalDeductionAmount": 0.0, "configRuleLogObject": []}, {"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "logMessages": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 8905.18"}, {"message": "HMOU Discount Calculated: 9402.08"}, {"message": "MAX Discount Calculated: 9402.08"}, {"message": "Discount Calculation Is Goving with HMOU"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Pharmacy & Medicine Charges: 2760.22,Hospital Charges: 1827.11,Consultant Charges: 1299.90,Surgery Charges: 94.74,Investigation & Lab Charges: 2460.50,Miscellaneous Charges: 959.57"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}], "matchingConfig": [{"claimType": "None"}, {"claimType": "PreAuth"}, {"claimType": "Cashless"}], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 145307.6, "postExectionTotalDeductionAmount": 0.0, "configRuleLogObject": [{"displayName": "ClaimType", "value": "None", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "ClaimType", "value": "PreAuth", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "ClaimType", "value": "Cashless", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}]}, {"category": "NonMedicalExpense", "subCategory": "ManualOverRideBills", "subCategoryDisplayName": "Manual OverRide Bills", "logMessages": [{"message": "Manual OverRide Bills - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 145307.6, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"category": "NonMedicalExpense", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "logMessages": [{"message": "Standard (IRDAI) - Execution starts"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:IV Cannula, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Accu Check ( Glucometery/ Strips), Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Face Mask, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Band Aids, Bandages, Sterile Injections, Needles, Syringes, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Face Mask, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Band Aids, Bandages, Sterile Injections, Needles, Syringes, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Band Aids, Bandages, Sterile Injections, Needles, Syringes, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Miscellaneous Charges, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Miscellaneous Charges, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Nutrition Planning Charges - Dietician Charges- Diet Charges, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Miscellaneous Charges, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Accu Check ( Glucometery/ Strips), Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Miscellaneous Charges, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Accu Check ( Glucometery/ Strips), Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Accu Check ( Glucometery/ Strips), Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Band Aids, Bandages, Sterile Injections, Needles, Syringes, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Medical Records, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Band Aids, Bandages, Sterile Injections, Needles, Syringes, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON>, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Accu Check ( Glucometery/ Strips), Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Accu Check ( Glucometery/ Strips), Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Accu Check ( Glucometery/ Strips), Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Face Mask, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Face Mask, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Face Mask, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Bed Under Pad Charges, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Band Aids, Bandages, Sterile Injections, Needles, Syringes, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Accu Check ( Glucometery/ Strips), Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON>, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Face Mask, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Face Mask, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Bed Under Pad Charges, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:ECG Electrodes, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Flexi Mask, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Caps, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Band Aids, Bandages, Sterile Injections, Needles, Syringes, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:<PERSON><PERSON><PERSON>, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Band Aids, Bandages, Sterile Injections, Needles, Syringes, Marking Bill Item Non Payable"}, {"message": "Overide Bills Based On ConfigType:NME_Category PayableSubType:Accu Check ( Glucometery/ Strips), Marking Bill Item Non Payable"}, {"message": "Standard (IRDAI) - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 145307.6, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"category": "NonMedicalExpense", "subCategory": "HandelNMEByPreAuthType", "subCategoryDisplayName": "Estimate For Initial", "logMessages": [{"message": "*****Calulating CalculateNMEDeduction Started*****"}, {"message": "Estimate For Initial - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"category": "NonMedicalExpense", "subCategory": "CapAmbulanceCharges", "subCategoryDisplayName": "Ambulance Limit By Amount", "logMessages": [{"message": "No deduction found in the historic claims"}, {"message": "Max Payable Ambulance Charge: 2500"}, {"message": "Current Payable Ambulance Charge if Any applicable: 2500"}, {"message": "Ambulance Limit By Amount - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"category": "NonMedicalExpense", "subCategory": "MarkAllNMEsAsPayable", "subCategoryDisplayName": "Pay All NMEs", "logMessages": [{"message": "Rules not applied as no matching data found"}, {"message": "Pay All NMEs - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"category": "NonMedicalExpense", "subCategory": "RIPrePostNMEDeductions", "subCategoryDisplayName": "Reimbursement Pre-Post Deductions", "logMessages": [{"message": "Reimbursement Pre-Post Deductions - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"category": "NonMedicalExpense", "subCategory": "VariantNotConfigured", "subCategoryDisplayName": "Condition To Be Reviewed", "logMessages": [{"message": "Condition To Be Reviewed - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"category": "NonMedicalExpense", "subCategory": "OverrideNMEParticularsAmount", "subCategoryDisplayName": "NME limit by Subcharge", "logMessages": [{"message": "Limit applicable subcharge head: 1"}, {"message": "Matching Config Count: 1"}, {"message": "Matching Config Count: 1"}, {"message": "NME limit by Subcharge - Execution starts"}, {"message": "NME limit by Subcharge - Execution ends"}], "matchingConfig": [{"nmeConfigId": "0", "schemeId": "0", "policyId": "0", "payableType": "<PERSON>", "payableSubType": "Air Ambulance Charges", "isPayable": "True", "payableLimit": "100000", "quatityLimit": "-1", "quantityPriceLimit": "-1", "payablePercentageLimit": "0", "isLower": "False", "deductionLevel": "<PERSON><PERSON><PERSON>", "isActive": "True"}], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 119565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "NMEConfigId", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}, {"displayName": "SchemeId", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}, {"displayName": "PolicyId", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}, {"displayName": "PayableType", "value": "<PERSON>", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "PayableSubType", "value": "Air Ambulance Charges", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "IsPayable", "value": "Applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "PayableLimit", "value": "100000", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "QuatityLimit", "value": "-1", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "QuantityPriceLimit", "value": "-1", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "PayablePercentageLimit", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}, {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "DeductionLevel", "value": "<PERSON><PERSON><PERSON>", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "IsActive", "value": "Applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}]}, {"category": "NonMedicalExpense", "subCategory": "ManualOverRideBills", "subCategoryDisplayName": "Manual OverRide Bills", "logMessages": [{"message": "Manual OverRide Bills - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"category": "Repudiation", "subCategory": "ExclusionByGenderAilment", "subCategoryDisplayName": "Gender and ailment", "logMessages": [{"message": "Gender and ailment - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"category": "Repudiation", "subCategory": "OnNumberOfChildren", "subCategoryDisplayName": "Number of living children allowed for maternity coverage", "logMessages": [{"message": "Number of living children allowed for maternity coverage - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"category": "Repudiation", "subCategory": "ExclusionByAilment", "subCategoryDisplayName": "Repudiation By Ailment Category", "logMessages": [{"message": "Repudiation By Ailment Category - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"category": "Repudiation", "subCategory": "RepudiationByRefractiveError", "subCategoryDisplayName": "Refractive Error", "logMessages": [{"message": "Refractive Error - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "logMessages": [{"message": "Matching Config Count: 1"}, {"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}], "matchingConfig": [{"relation to Primary beneficiary": "Father, <PERSON><PERSON><PERSON>, Mother, <PERSON><PERSON><PERSON>", "grade": "any", "check PED Applicable For Beneficiary": "True", "skip For Demise": "False", "skip For Emergency Admission": "False", "number of days for waiting period": "365", "benefitScope": "Policy"}], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 119565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "Relation to Primary beneficiary", "value": "Father,<PERSON><PERSON><PERSON>,Mother,<PERSON><PERSON><PERSON>", "isFilterApplicable": true, "defaultValue": "ALL", "isToBeDisplayed": true, "claimValue": "<PERSON><PERSON><PERSON>"}, {"displayName": "Grade", "value": "any", "isFilterApplicable": true, "defaultValue": "ANY", "isToBeDisplayed": true, "claimValue": "Gold Plus"}, {"displayName": "Check PED Applicable For Beneficiary", "value": "Applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Skip For Demise", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Skip For Emergency Admission", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Number of days for waiting period", "value": "365", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "BenefitScope", "value": "Policy", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}]}, {"category": "Repudiation", "subCategory": "DenyObesity", "subCategoryDisplayName": "Obesity", "logMessages": [{"message": "Obesity - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"category": "Repudiation", "subCategory": "WaitingPeriodByAilmentsCategory", "subCategoryDisplayName": "Waiting Period by Ailment category", "logMessages": [{"message": "Waiting Period by Ailment category - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"category": "Repudiation", "subCategory": "ExclusionByProcedures", "subCategoryDisplayName": "Repudiation By Procedure", "logMessages": [{"message": "Repudiation By Procedure - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"category": "Repudiation", "subCategory": "WaitingPeriodByProcedures", "subCategoryDisplayName": "Waiting Period by Procedure", "logMessages": [{"message": "Waiting Period by Procedure - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"category": "Repudiation", "subCategory": "RepudiationByHospital", "subCategoryDisplayName": "Repudiation By Hospital And Claim Type", "logMessages": [{"message": "Matching Config Count: 1"}, {"message": "Matching Config Count: 1"}, {"message": "Repudiation By Hospital And Claim Type - Execution starts"}, {"message": "Repudiation By Hospital And Claim Type - Execution ends"}], "matchingConfig": [{"relation To Primary Beneficiary": "ALL", "grade": "gold, gold plus, gold plus_r, gold_r, silver, silver_r, platinum, platinum_r", "claim type": "Cashless, PreAuth", "skip For Network Hospital": "False", "skip For PPN Hospital": "False", "hospital": "287585", "skip for Demise": "False", "benefitScope": "Policy"}], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 119565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 119565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "Relation To Primary Beneficiary", "value": "ALL", "isFilterApplicable": true, "defaultValue": "ALL", "isToBeDisplayed": true, "claimValue": "<PERSON><PERSON><PERSON>"}, {"displayName": "Grade", "value": "gold,gold plus,gold plus_r,gold_r,silver,silver_r,platinum,platinum_r", "isFilterApplicable": true, "defaultValue": "ANY", "isToBeDisplayed": true, "claimValue": "Gold Plus"}, {"displayName": "Claim type", "value": "Cashless,PreAuth", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "<PERSON><PERSON> For Network Hospital", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Skip For PPN Hospital", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Hospital", "value": "287585", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Skip for Demise", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "BenefitScope", "value": "Policy", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}]}, {"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "logMessages": [{"message": "Matching Config Count: 2"}, {"message": "Matching Config Count: 2"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:11100.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SemiPrivateRoom"}, {"message": "Tariff Room Rent From Master:5500.0"}, {"message": "Tariff ICU Room Rent From Master:5500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 4000.000"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}], "matchingConfig": [{"claimType": "PreAuth"}, {"claimType": "Cashless"}], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 119565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "ClaimType", "value": "PreAuth", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "ClaimType", "value": "Cashless", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "logMessages": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category SemiPvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SemiPrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:11100.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category Found Under Sharing Category"}, {"message": "Found room rent details under SHARING Category"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category not found Under PRIVATE Category"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer nursing,room,room rent,room rent (per day),associated room charges,duty doctor,injection / iv administration charge,gst on room rent"}, {"message": "ICU : Room Rent based on insurer icu charges,icu nursing charges,icu room rent,icu injection / iv administration charge,intencivist charges,icu monitoring charges"}, {"message": "Normal Room Rent Lenght of Stay : 4.00"}, {"message": "ICU Room Rent Lenght of Stay : 0"}, {"message": "Non-Tariff bill as insurer Perday ChargeType :gst on room rent , Units : 4.00, PerDay Price : 604"}, {"message": "Non-Tariff bill as insurer Perday ChargeType :gst on room rent , Units : 4.00, PerDay Price : 604"}, {"message": "Tariff Bill Price as per insurer :5500.0"}, {"message": "Non-Tariff bill Price as per insurer :302"}, {"message": "Total Room Price as per insurer :5802.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 5802.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 5802.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 5802.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 4.00"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 5802.26"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 23208.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}], "matchingConfig": [{"type of  Room": "Normal", "provider Type": "False", "propotionateApplicable": "True", "skip Proportionate On DeathCase": "False", "skip Proportionate On AccidentCase": "False", "topUp Configured": "False", "relation to Primary beneficiary": "Father, <PERSON><PERSON><PERSON>, Mother, <PERSON><PERSON><PERSON>", "grade": "gold plus, gold", "min Age": "0", "max Age": "0", "room Value Limit Defined By": "Amount", "room Value Type": "5802.0", "room Capped Limit": "0", "proportionate Limit Defined By": "Amount", "proportionate Limit Defined By Value": "0", "min SI": "0", "max SI": "0", "benefitScope": "Policy"}], "preExecutionApprovedAmount": 106163.96, "preExecutionTotalBillAmount": 115565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 106163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "Type of  Room", "value": "Normal", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Provider Type", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "PropotionateApplicable", "value": "Applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Skip Proportionate On DeathCase", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Skip Proportionate On AccidentCase", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "TopUp Configured", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Relation to Primary beneficiary", "value": "Father,<PERSON><PERSON><PERSON>,Mother,<PERSON><PERSON><PERSON>", "isFilterApplicable": true, "defaultValue": "ALL", "isToBeDisplayed": true, "claimValue": "<PERSON><PERSON><PERSON>"}, {"displayName": "Grade", "value": "gold plus,gold", "isFilterApplicable": true, "defaultValue": "ANY", "isToBeDisplayed": true, "claimValue": "Gold Plus"}, {"displayName": "Min Age", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "61"}, {"displayName": "Max Age", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "61"}, {"displayName": "Room Value Limit Defined By", "value": "Amount", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Room Value Type", "value": "5802.0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Room Capped Limit", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}, {"displayName": "Proportionate Limit Defined By", "value": "Amount", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Proportionate Limit Defined By Value", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}, {"displayName": "Min SI", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "1600000.0"}, {"displayName": "Max SI", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "1600000.0"}, {"displayName": "BenefitScope", "value": "Policy", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}]}, {"category": "PropotinateDeduction", "subCategory": "VariantNotConfigured", "subCategoryDisplayName": "Condition To Be Reviewed", "logMessages": [{"message": "Condition To Be Reviewed - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 106163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "logMessages": [{"message": "Matching Config Count: 1"}, {"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}], "matchingConfig": [{"relation To Primary Beneficiary": "ALL", "grade": "gold, gold plus, platinum, silver", "claim type": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>", "intimation Days": "3", "copay Defined By": "PecentageOfAdmissibleAmount", "copay Defined Value(Percentage/Amount)": "10", "upper/Lower Copay Limit For Comparison": "0", "skip For Emergency Admission": "True", "is Min/Max Copay Applicable Per Person": "False", "is Copay Applicable Per Admission Basis": "False", "copay Limit Applicable Per Family": "False", "dedution Remarks": "INTIMATION_COPAY", "benefitScope": "Policy"}], "preExecutionApprovedAmount": 106163.96, "preExecutionTotalBillAmount": 115565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 106163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "Relation To Primary Beneficiary", "value": "ALL", "isFilterApplicable": true, "defaultValue": "ALL", "isToBeDisplayed": true, "claimValue": "<PERSON><PERSON><PERSON>"}, {"displayName": "Grade", "value": "gold,gold plus,platinum,silver", "isFilterApplicable": true, "defaultValue": "ANY", "isToBeDisplayed": true, "claimValue": "Gold Plus"}, {"displayName": "Claim type", "value": "<PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>", "isFilterApplicable": true, "defaultValue": "None", "isToBeDisplayed": true, "claimValue": "PreAuth"}, {"displayName": "Intimation Days", "value": "3", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Copay Defined By", "value": "PecentageOfAdmissibleAmount", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Copay Defined Value(Percentage/Amount)", "value": "10", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Upper/Lower Copay Limit For Comparison", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}, {"displayName": "Skip For Emergency Admission", "value": "Applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Is Min/Max Copay Applicable Per Person", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Is Copay Applicable Per Admission Basis", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Copay Limit Applicable Per Family", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Dedution Remarks", "value": "INTIMATION_COPAY", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "BenefitScope", "value": "Policy", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}]}, {"category": "Copay", "subCategory": "CopayOnNonNetwork", "subCategoryDisplayName": "Non-Network Hospital", "logMessages": [{"message": "Non-Network Hospital - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 106163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": []}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "logMessages": [{"message": "Matching Config Count: 1"}, {"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 10%  of admissible amount 106163.96 and flat amount 10000 is 10000"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 10000"}, {"message": "Copay deduction amount is 10000"}, {"message": "Copay deduction amount is: 10000"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 10%  of admissible amount 106163.96 and flat amount 10000 is 10000 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}], "matchingConfig": [{"relationship": "ALL", "grade": "gold plus, gold plus_r", "minimum Age": "0", "maximum Age": "0", "min SI": "0", "max SI": "0", "min Claim amount": "0", "max Claim amount": "0", "min Admissible amount": "0", "max Admissible amount": "0", "exculding Procedures": "3119, 3163, 3165, 3467, 3861, 4121, 4430, 4436, 4513, 4560, 4591, 4599, 4671", "hospital Network/Non-Network": "False", "copay Type": "MinOfPercentageOfAdmissibleAndCappedAmount", "copay Percentage": "10", "capped Value": "10000", "skip For Emergency Admission": "False", "applicable on claim event basis": "True", "is Family Floater": "False", "applicable on claim Member basis": "False", "no. of Days": "0", "check PED Applicable For Beneficiary": "False", "isCopayDueToAnyoneIllness": "False", "exculdingAilmentsCategoryOrAilment": "IVF_FOR_INFERTILITY,INFERTILITY,PSYCHIATRIC,GENETICDISORDER,M<PERSON><PERSON><PERSON>LE_SCLEROSIS,MATERNITY_COMPLICATIONS,MATERNITY_MULTIPLE_DELIVERY,MATER<PERSON><PERSON>_NORMAL,MATE<PERSON><PERSON>TY_LSCS,TWIN_DELIVERY,TRIPLET_DELIVERY,MORE_THAN_TRIPLET_DELIVERY,PRE_AND_POST_NATAL,Mental_disorder", "dedution Remarks": "MANDATORY_COPAY", "sum Insured Utilization": "0", "benefitScope": "Policy"}], "preExecutionApprovedAmount": 106163.96, "preExecutionTotalBillAmount": 115565.9, "preExectionTotalDeductionAmount": 9402.04, "postExecutionApprovedAmount": 106163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 9402.04, "configRuleLogObject": [{"displayName": "Relationship", "value": "ALL", "isFilterApplicable": true, "defaultValue": "ALL", "isToBeDisplayed": true, "claimValue": "<PERSON><PERSON><PERSON>"}, {"displayName": "Grade", "value": "gold plus,gold plus_r", "isFilterApplicable": true, "defaultValue": "ANY", "isToBeDisplayed": true, "claimValue": "Gold Plus"}, {"displayName": "Minimum Age", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "61"}, {"displayName": "Maximum Age", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "61"}, {"displayName": "Min SI", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "1600000.0"}, {"displayName": "Max SI", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "1600000.0"}, {"displayName": "Min Claim amount", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "145307.0"}, {"displayName": "Max Claim amount", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "145307.0"}, {"displayName": "Min Admissible amount", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "115566"}, {"displayName": "Max Admissible amount", "value": "0", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": false, "claimValue": "115566"}, {"displayName": "Exculding Procedures", "value": "3119,3163,3165,3467,3861,4121,4430,4436,4513,4560,4591,4599,4671", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": true, "claimValue": "4564"}, {"displayName": "Hospital Network/Non-Network", "value": "Not applicable", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": true, "claimValue": "True"}, {"displayName": "Copay Type", "value": "MinOfPercentageOfAdmissibleAndCappedAmount", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Copay Per<PERSON>age", "value": "10", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Capped Value", "value": "10000", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Skip For Emergency Admission", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Applicable on claim event basis", "value": "Applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Is Family Floater", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Applicable on claim Member basis", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "No. of Days", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}, {"displayName": "Check PED Applicable For Beneficiary", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "isCopayDueToAnyoneIllness", "value": "Not applicable", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "ExculdingAilmentsCategoryOrAilment", "value": "IVF_FOR_INFERTILITY,INFERTILITY,PSYCHIATRIC,GENETICDISORDER,M<PERSON><PERSON><PERSON>LE_SCLEROSIS,MATERNITY_COMPLICATIONS,MATERNITY_MULTIPLE_DELIVERY,MATER<PERSON><PERSON>_NORMAL,MATE<PERSON><PERSON>TY_LSCS,TWIN_DELIVERY,TRIPLET_DELIVERY,MORE_THAN_TRIPLET_DELIVERY,PRE_AND_POST_NATAL,Mental_disorder", "isFilterApplicable": true, "defaultValue": "", "isToBeDisplayed": true, "claimValue": "INFECTIOUS_BACTERIAL_VIRAL_MEDICAL_MGMT"}, {"displayName": "Dedution Remarks", "value": "MANDATORY_COPAY", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": true, "claimValue": null}, {"displayName": "Sum Insured Utilization", "value": "0", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}, {"displayName": "BenefitScope", "value": "Policy", "isFilterApplicable": false, "defaultValue": "", "isToBeDisplayed": false, "claimValue": null}]}, {"category": "Copay", "subCategory": "RelationGradeAdmissibleAmount", "subCategoryDisplayName": "Copay By Admissible Amount", "logMessages": [{"message": "Copay By Admissible Amount - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"category": "Copay", "subCategory": "VariantNotConfigured", "subCategoryDisplayName": "Condition To Be Reviewed", "logMessages": [{"message": "Condition To Be Reviewed - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"category": "Copay", "subCategory": "RelationGradeAilment", "subCategoryDisplayName": "Ailment Category", "logMessages": [{"message": "Ailment Category - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"category": "AilmentCapping", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "logMessages": [{"message": "Standard (IRDAI) - Execution starts"}, {"message": "Standard (IRDAI) - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 96163.96, "preExecutionTotalBillAmount": 115565.9, "preExectionTotalDeductionAmount": 19402.04, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"category": "AilmentCapping", "subCategory": "AilmentCappingByGrade", "subCategoryDisplayName": "Ailment Category", "logMessages": [{"message": "Ailment Category - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"category": "AilmentCapping", "subCategory": "ProcedureCappingByApproachAndPostion", "subCategoryDisplayName": "Position and Approach", "logMessages": [{"message": "Position and Approach - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"category": "AilmentCapping", "subCategory": "AilmentProcedureCappingBySIType", "subCategoryDisplayName": "Ailment Category and Procedure Combination", "logMessages": [{"message": "Ailment Category and Procedure Combination - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"category": "AilmentCapping", "subCategory": "ProcedureCappingByGrade", "subCategoryDisplayName": "Procedure", "logMessages": [{"message": "Procedure - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"category": "AilmentCapping", "subCategory": "AilmentCappingByClaimType", "subCategoryDisplayName": "Ailment Capping by <PERSON><PERSON><PERSON>", "logMessages": [{"message": "Ailment Capping by <PERSON><PERSON>m Type - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"category": "AilmentCapping", "subCategory": "MaternityWellnessBabyCapping", "subCategoryDisplayName": "Maternity, wellness baby care & Pre-Post natal", "logMessages": [{"message": "Maternity, wellness baby care & Pre-Post natal - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"category": "AilmentCapping", "subCategory": "VariantNotConfigured", "subCategoryDisplayName": "Condition To Be Reviewed", "logMessages": [{"message": "Condition To Be Reviewed - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"category": "SIRestriction", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "logMessages": [{"message": "Standard - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"category": "SIRestriction", "subCategory": "OtherHealthCareSIRestriction", "subCategoryDisplayName": "SI Restriction By Health Care Types", "logMessages": [{"message": "SI Restriction By Health Care Types - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"category": "PolicyBenefits", "subCategory": "NotApplicable", "subCategoryDisplayName": "Not Applicable", "logMessages": [{"message": "Not Applicable - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}, {"category": "UCRDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "logMessages": [{"message": "*****Calculation On SOC Started*****"}, {"message": "NO SOC Details Found"}, {"message": "*****Calculation On SOC Ends*****"}, {"message": "Standard - Execution ends"}], "matchingConfig": [], "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 96163.96, "postExecutionTotalBillAmount": 115565.9, "postExectionTotalDeductionAmount": 19402.04, "configRuleLogObject": []}], "_will_be_processed": true}], "_cleaning_metadata": {"fields_removed": ["logCreatedDate (from logs)", "data (from logs)", "timeOfOccurrence (from data entries and rule logs)", "loggerType (from data entries and rule logs)"], "cleaning_timestamp": "2025-06-13T20:13:17.725718", "purpose": "Remove unnecessary fields to focus on essential claim processing information"}, "_processing_metadata": {"filtered_for_claim_logs_only": true, "processing_timestamp": "2025-06-13T20:13:17.732922", "total_logs": 2, "claim_logs_count": 2}}]