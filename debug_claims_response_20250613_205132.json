[{"_debug_info": {"total_claims": 1, "filtering_rule": "Only logs with typeOfLog: \"Claim\" will be processed", "alteration_timestamp": "2025-06-13T20:51:32.209871"}}, {"claimNo": 131492639, "logs": [{"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": [{"category": "BaseConfiguration", "subCategory": "DefautChecks", "subCategoryDisplayName": "Default Configuration", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 0.0, "postExectionTotalDeductionAmount": -105857.85, "reasons": [{"message": "Claim types considered for discount override: PreHosp,PostHosp,HospBenefit,HealthCheckUp,RI,ReOpenClaim"}, {"message": "Matching Config Count: 1"}, {"message": "Matching Config Count: 1"}, {"message": "Override Discount By Claim Type - Execution starts"}, {"message": "Discount overridden for Claim Type PostHosp"}, {"message": "Default Configuration - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 0.0, "next_bill_amount": 105857.85, "bill_reduction": -105857.85, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 1, "total_rules_in_sequence": 5, "explanation": "Bill reduced from 0.0 to 105857.85"}}, {"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 105857.85, "postExectionTotalDeductionAmount": 8007.760000000009, "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "Ignoring Discount calculation or prepost bills TotalBillCount:27, PrepostBills:25"}, {"message": "MMOU Discount Calculated: 0.00"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 0.00"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: "}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 105857.85, "next_bill_amount": 97850.09, "bill_reduction": 8007.760000000009, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 2, "total_rules_in_sequence": 5, "explanation": "Bill reduced from 105857.85 to 97850.09"}}, {"category": "NonMedicalExpense", "subCategory": "CapAmbulanceChargesByAmountComparision", "subCategoryDisplayName": "Ambulance Limit By Sum Insured & Amount Comparison", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 97850.09, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Matching Config Count: 1"}, {"message": "Comparision between 1% and flat amount 2500 is 2500"}, {"message": "Deduction amount is 2500 as payable amount is less than confiuration value 2500"}, {"message": "Ambulance Limit By Sum Insured & Amount Comparison - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 97850.09, "next_bill_amount": 97850.09, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 3, "total_rules_in_sequence": 5, "explanation": "Bill reduced from 97850.09 to 97850.09"}}, {"category": "NonMedicalExpense", "subCategory": "RIPrePostNMEDeductions", "subCategoryDisplayName": "Reimbursement Pre-Post Deductions", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 0.0, "postExecutionTotalBillAmount": 97850.09, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Matching Config Count: 1"}, {"message": "Pre-hospitalisation days restriction is 30, Post-hospitalisation days restriction is 60"}, {"message": "Limit applicable by: PercentageOfTotalSI"}, {"message": "Limit applied based on 100% of sum insured 1000000.0 is 1000000.0"}, {"message": "Bills are within Pre/Post Hospitalization limit days"}, {"message": "Bills are within Pre/Post Hospitalization limit days"}, {"message": "Bills are within Pre/Post Hospitalization limit days"}, {"message": "Bills are within Pre/Post Hospitalization limit days"}, {"message": "Bills are within Pre/Post Hospitalization limit days"}, {"message": "Bills are within Pre/Post Hospitalization limit days"}, {"message": "Bills are within Pre/Post Hospitalization limit days"}, {"message": "Bills are within Pre/Post Hospitalization limit days"}, {"message": "Bills are within Pre/Post Hospitalization limit days"}, {"message": "Bills are within Pre/Post Hospitalization limit days"}, {"message": "Bills are within Pre/Post Hospitalization limit days"}, {"message": "Bills are within Pre/Post Hospitalization limit days"}, {"message": "Bills are within Pre/Post Hospitalization limit days"}, {"message": "Bills are within Pre/Post Hospitalization limit days"}, {"message": "Bills are within Pre/Post Hospitalization limit days"}, {"message": "Bills are within Pre/Post Hospitalization limit days"}, {"message": "Bills are within Pre/Post Hospitalization limit days"}, {"message": "Bills are within Pre/Post Hospitalization limit days"}, {"message": "Bills are within Pre/Post Hospitalization limit days"}, {"message": "Bills are within Pre/Post Hospitalization limit days"}, {"message": "Bills are within Pre/Post Hospitalization limit days"}, {"message": "Bills are within Pre/Post Hospitalization limit days"}, {"message": "Bills are within Pre/Post Hospitalization limit days"}, {"message": "Bills are within Pre/Post Hospitalization limit days"}, {"message": "Bills are within Pre/Post Hospitalization limit days"}, {"message": "Reimbursement Pre-Post Deductions - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 97850.09, "next_bill_amount": 97850.09, "bill_reduction": 0.0, "calculation_method": "current_bill_minus_next_bill", "position_in_sequence": 4, "total_rules_in_sequence": 5, "explanation": "Bill reduced from 97850.09 to 97850.09"}}, {"category": "PropotinateDeduction", "subCategory": "FixedLimitWithAilmentExclusion", "subCategoryDisplayName": "By Room Value With Ailment Exclusion", "preExecutionApprovedAmount": 0.0, "preExecutionTotalBillAmount": 0.0, "preExectionTotalDeductionAmount": 0.0, "postExecutionApprovedAmount": 97851.0, "postExecutionTotalBillAmount": 97850.09, "postExectionTotalDeductionAmount": 0.0, "reasons": [{"message": "Matching Config Count: 2"}, {"message": "Matching Config Count: 2"}, {"message": "Room Rent limit applied based on 1% of sum insured 1000000.0 is 10000.0"}, {"message": "Room Rent limit applied based on 2% of sum insured 1000000.0 is 20000.0"}, {"message": "*****Calulating Eligibile Room Category By RoomRent Started*****"}, {"message": "Rule Suggested, Eligible Room Rent 10000.0"}, {"message": "Rule Suggested, Eligible Room Rent Tolerance 105"}, {"message": "Rule Suggested, Eligible Room Rent ToleranceType Percentage"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Hospital room rent master details"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:15000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:9000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:5000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:3000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:5000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:7000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Hospital room rent master details After filtering ICU"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:15000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:9000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:5000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:3000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:5000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:7000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "No excat room rent details for the eligible room rent :10000.0"}, {"message": "Now trying to find room rent details by applying tolerance by :Percentage"}, {"message": "Tolerance Amount:10500.000"}, {"message": "Finding room rent details from master , taking max price which is less than tolerance amount 10500.000"}, {"message": "Max room rent price 9000.0"}, {"message": "Room Rent master details which is used to calculate eligible room rent"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Rule Calculated EligibleRoomCategory : DELUXE"}, {"message": "*****Calulating Eligibile Room Category By RoomRent END*****"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 10000.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: 20000.0"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 0.0"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0.0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Room Value With Ailment Exclusion - Execution ends"}], "_deduction_calculation": {"current_bill_amount": 97850.09, "next_bill_amount": null, "bill_reduction": 0.0, "calculation_method": "final_rule_no_reduction", "position_in_sequence": 5, "total_rules_in_sequence": 5, "explanation": "Bill reduced from 97850.09 to final"}}], "_will_be_processed": true}], "_cleaning_metadata": {"fields_removed": ["logCreatedDate (from logs)", "data (from logs)", "timeOfOccurrence (from data entries and rule logs)", "loggerType (from data entries and rule logs)", "configRuleLogObject (from rule engine logs)", "matchingConfig (from rule engine logs)"], "fields_renamed": ["logMessages → reasons (in rule engine logs)"], "filtering_applied": ["Only rule engine logs with non-empty matchingConfig arrays are included"], "calculations_applied": ["postExectionTotalDeductionAmount calculated as bill amount reduction (current_bill - next_bill)"], "cleaning_timestamp": "2025-06-13T20:51:32.209127", "purpose": "Remove unnecessary fields, rename for clarity, filter for relevant rule logs, and calculate incremental deductions"}, "_processing_metadata": {"filtered_for_claim_logs_only": true, "processing_timestamp": "2025-06-13T20:51:32.209847", "total_logs": 1, "claim_logs_count": 1}}]