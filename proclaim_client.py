import requests
import json
import logging
from typing import List, Dict, Any, Optional
from config import settings

logger = logging.getLogger(__name__)

class ProclaimAPIClient:
    def __init__(self):
        self.api_url = settings.PROCLAIM_API_URL
        self.access_token = settings.PROCLAIM_ACCESS_TOKEN
        self.payer = settings.PROCLAIM_PAYER
        
        # Default headers for all requests
        self.headers = {
            'Content-Type': 'application/json',
            'AccessToken': self.access_token,
            'Payer': self.payer,
            'Cookie': 'Qr9P4DDcnXKIGgFQ4V3s=v1DN6GSQSDK86'
        }
    
    def fetch_claims_data(self, claim_numbers: List[int], 
                         login_name: str = "avnish.garg",
                         login_email: str = "<EMAIL>",
                         login_user_id: int = 18608) -> Optional[Dict[str, Any]]:
        """
        Fetch claims data from Proclaim API
        
        Args:
            claim_numbers: List of claim numbers to fetch
            login_name: Login name for the API
            login_email: Login email for the API
            login_user_id: Login user ID for the API
            
        Returns:
            Claims data as dictionary or None if failed
        """
        try:
            # Prepare request payload
            payload = {
                "claimNo": claim_numbers,
                "LoginName": login_name,
                "LoginEmailId": login_email,
                "LoginUserId": login_user_id,
                "AdditionalUserInfo": {}
            }
            
            logger.info(f"Fetching data for claims: {claim_numbers}")
            
            # Make API request
            response = requests.post(
                self.api_url,
                headers=self.headers,
                json=payload,
                timeout=60  # 60 second timeout for large responses
            )
            
            # Check response status
            if response.status_code == 200:
                try:
                    data = response.json()
                    logger.info(f"Successfully fetched data for {len(claim_numbers)} claims")
                    return data
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse JSON response: {e}")
                    return None
            else:
                logger.error(f"API request failed with status {response.status_code}: {response.text}")
                return None
                
        except requests.exceptions.Timeout:
            logger.error("API request timed out")
            return None
        except requests.exceptions.RequestException as e:
            logger.error(f"API request failed: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error fetching claims data: {e}")
            return None
    
    def fetch_single_claim(self, claim_number: int, **kwargs) -> Optional[Dict[str, Any]]:
        """
        Fetch data for a single claim
        
        Args:
            claim_number: Single claim number to fetch
            **kwargs: Additional arguments for fetch_claims_data
            
        Returns:
            Claims data as dictionary or None if failed
        """
        return self.fetch_claims_data([claim_number], **kwargs)
    
    def validate_configuration(self) -> bool:
        """
        Validate that the API configuration is properly set up
        
        Returns:
            True if configuration is valid, False otherwise
        """
        if not self.access_token or self.access_token == "":
            logger.error("PROCLAIM_ACCESS_TOKEN is not configured")
            return False
        
        if not self.api_url:
            logger.error("PROCLAIM_API_URL is not configured")
            return False
        
        if not self.payer:
            logger.error("PROCLAIM_PAYER is not configured")
            return False
        
        logger.info("Proclaim API configuration is valid")
        return True
    
    def test_connection(self) -> bool:
        """
        Test the connection to Proclaim API with a sample request
        
        Returns:
            True if connection is successful, False otherwise
        """
        if not self.validate_configuration():
            return False
        
        try:
            # Test with a sample claim number
            test_claim = [131420765]  # Using the sample claim from your data
            
            logger.info("Testing Proclaim API connection...")
            result = self.fetch_claims_data(test_claim)
            
            if result is not None:
                logger.info("✓ Proclaim API connection test successful")
                return True
            else:
                logger.error("✗ Proclaim API connection test failed")
                return False
                
        except Exception as e:
            logger.error(f"✗ Proclaim API connection test failed: {e}")
            return False
    
    def get_api_info(self) -> Dict[str, Any]:
        """
        Get information about the API configuration
        
        Returns:
            Dictionary with API configuration info
        """
        return {
            "api_url": self.api_url,
            "has_access_token": bool(self.access_token),
            "payer": self.payer,
            "configuration_valid": self.validate_configuration()
        }
