import requests
import json
import logging
from typing import List, Dict, Any, Optional
from config import settings

logger = logging.getLogger(__name__)

class ProclaimAPIClient:
    def __init__(self):
        self.api_url = settings.PROCLAIM_API_URL
        self.access_token = settings.PROCLAIM_ACCESS_TOKEN
        self.payer = settings.PROCLAIM_PAYER

        # New claim details API endpoint
        self.claim_details_url = "https://proclaim-api-prod.mediassist.in//Claim/Details/new.json"

        # Default headers for all requests
        self.headers = {
            'Content-Type': 'application/json',
            'AccessToken': self.access_token,
            'Payer': self.payer,
            'Cookie': 'Qr9P4DDcnXKIGgFQ4V3s=v1DN6GSQSDK86'
        }

        # Enhanced headers for claim details API (based on your curl command)
        self.claim_details_headers = {
            'sec-ch-ua-platform': '"macOS"',
            'Referer': 'https://matrix.mediassist.in/',
            'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            'AccessToken': self.access_token,
            'sec-ch-ua-mobile': '?0',
            'Pcvt': 'JrmJpQFO9ug9G81lokdFQy/wq88X1lzmKrU00Qp7zZY3387ZVd2TiFkxvhAmDURGh4gfrbSyQUllBbmaafprJNeNMKUOA/wYCQqy/Zkgv//fKu2kvUOiQmTJRG1Qy3xZTGwRJFfQBtIN2zGcrzlxY6Ivv4xbTOsjoX3IbiK3GTV6XM3oCTjwCK9KwAmc5ZN7lWMubU5WU/Mt34bBHeHpVsVccs0erFKRSORAWlAUbjwT2b3JJkLmBmH7K4J6OVkCspj2PYfgrPERxmJHwx+Z0Q==',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Content-Type': 'application/json',
            'Payer': self.payer
        }
    
    def fetch_claims_data(self, claim_numbers: List[int], 
                         login_name: str = "avnish.garg",
                         login_email: str = "<EMAIL>",
                         login_user_id: int = 18608) -> Optional[Dict[str, Any]]:
        """
        Fetch claims data from Proclaim API
        
        Args:
            claim_numbers: List of claim numbers to fetch
            login_name: Login name for the API
            login_email: Login email for the API
            login_user_id: Login user ID for the API
            
        Returns:
            Claims data as dictionary or None if failed
        """
        try:
            # Prepare request payload
            payload = {
                "claimNo": claim_numbers,
                "LoginName": login_name,
                "LoginEmailId": login_email,
                "LoginUserId": login_user_id,
                "AdditionalUserInfo": {}
            }
            
            logger.info(f"Fetching data for claims: {claim_numbers}")
            
            # Make API request
            response = requests.post(
                self.api_url,
                headers=self.headers,
                json=payload,
                timeout=60  # 60 second timeout for large responses
            )
            
            # Check response status
            if response.status_code == 200:
                try:
                    data = response.json()
                    logger.info(f"Successfully fetched data for {len(claim_numbers)} claims")
                    return data
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse JSON response: {e}")
                    return None
            else:
                logger.error(f"API request failed with status {response.status_code}: {response.text}")
                return None
                
        except requests.exceptions.Timeout:
            logger.error("API request timed out")
            return None
        except requests.exceptions.RequestException as e:
            logger.error(f"API request failed: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error fetching claims data: {e}")
            return None
    
    def fetch_single_claim(self, claim_number: int, **kwargs) -> Optional[Dict[str, Any]]:
        """
        Fetch data for a single claim

        Args:
            claim_number: Single claim number to fetch
            **kwargs: Additional arguments for fetch_claims_data

        Returns:
            Claims data as dictionary or None if failed
        """
        return self.fetch_claims_data([claim_number], **kwargs)

    def fetch_claim_details(self, claim_number: int,
                           login_name: str = "avnish.garg",
                           login_email: str = "<EMAIL>",
                           login_user_id: int = 18608) -> Optional[Dict[str, Any]]:
        """
        Fetch claim details from the new API endpoint to get related pre-auth IDs

        Args:
            claim_number: Claim number to fetch details for
            login_name: Login name for the API
            login_email: Login email for the API
            login_user_id: Login user ID for the API

        Returns:
            Claim details as dictionary or None if failed
        """
        try:
            # Prepare request payload
            payload = {
                "claimNumber": claim_number,
                "dbType": "MAUHS",
                "isIntimationDetailsRequired": True,
                "isPADetailsRequired": True,
                "LoginName": login_name,
                "LoginEmailId": login_email,
                "LoginUserId": login_user_id,
                "AdditionalUserInfo": {}
            }

            logger.info(f"Fetching claim details for claim: {claim_number}")

            # Make API request
            response = requests.post(
                self.claim_details_url,
                headers=self.claim_details_headers,
                json=payload,
                timeout=60
            )

            # Check response status
            if response.status_code == 200:
                try:
                    data = response.json()
                    logger.info(f"Successfully fetched claim details for claim {claim_number}")
                    return data
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse JSON response: {e}")
                    return None
            else:
                logger.error(f"Claim details API request failed with status {response.status_code}: {response.text}")
                return None

        except requests.exceptions.Timeout:
            logger.error("Claim details API request timed out")
            return None
        except requests.exceptions.RequestException as e:
            logger.error(f"Claim details API request failed: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error fetching claim details: {e}")
            return None

    def extract_related_preauth_ids(self, claim_details: Dict[str, Any]) -> List[int]:
        """
        Extract related pre-auth IDs from claim details response

        Args:
            claim_details: Response from fetch_claim_details

        Returns:
            List of related pre-auth IDs
        """
        try:
            preauth_ids = []

            # Navigate through the response structure
            claim_details_list = claim_details.get('claimDetails', [])

            for claim_detail in claim_details_list:
                related_preauths = claim_detail.get('relatedPreAuths', [])

                for preauth in related_preauths:
                    preauth_id = preauth.get('id')
                    if preauth_id:
                        preauth_ids.append(preauth_id)

            logger.info(f"Extracted {len(preauth_ids)} related pre-auth IDs: {preauth_ids}")
            return preauth_ids

        except Exception as e:
            logger.error(f"Error extracting related pre-auth IDs: {e}")
            return []

    def fetch_and_process_claim(self, claim_number: int,
                               login_name: str = "avnish.garg",
                               login_email: str = "<EMAIL>",
                               login_user_id: int = 18608) -> Optional[Dict[str, Any]]:
        """
        Complete workflow: Fetch claim details, extract related pre-auth IDs,
        then fetch rule engine logs for those IDs

        Args:
            claim_number: Main claim number to process
            login_name: Login name for the API
            login_email: Login email for the API
            login_user_id: Login user ID for the API

        Returns:
            Combined response with claim details and rule engine logs or None if failed
        """
        try:
            logger.info(f"Starting complete claim processing for claim: {claim_number}")

            # Step 1: Fetch claim details
            claim_details = self.fetch_claim_details(
                claim_number, login_name, login_email, login_user_id
            )

            if not claim_details:
                logger.error(f"Failed to fetch claim details for claim {claim_number}")
                return None

            # Step 2: Extract related pre-auth IDs
            preauth_ids = self.extract_related_preauth_ids(claim_details)

            if not preauth_ids:
                logger.warning(f"No related pre-auth IDs found for claim {claim_number}")
                # Return just the claim details if no pre-auths found
                return {
                    "claim_details": claim_details,
                    "rule_engine_logs": None,
                    "preauth_ids": [],
                    "main_claim_number": claim_number
                }

            # Step 3: Fetch rule engine logs for the pre-auth IDs
            logger.info(f"Fetching rule engine logs for pre-auth IDs: {preauth_ids}")
            rule_engine_logs = self.fetch_claims_data(
                preauth_ids, login_name, login_email, login_user_id
            )

            # Step 4: Combine the results
            combined_result = {
                "claim_details": claim_details,
                "rule_engine_logs": rule_engine_logs,
                "preauth_ids": preauth_ids,
                "main_claim_number": claim_number
            }

            logger.info(f"Successfully completed claim processing for claim {claim_number}")
            return combined_result

        except Exception as e:
            logger.error(f"Error in complete claim processing for claim {claim_number}: {e}")
            return None
    
    def validate_configuration(self) -> bool:
        """
        Validate that the API configuration is properly set up
        
        Returns:
            True if configuration is valid, False otherwise
        """
        if not self.access_token or self.access_token == "":
            logger.error("PROCLAIM_ACCESS_TOKEN is not configured")
            return False
        
        if not self.api_url:
            logger.error("PROCLAIM_API_URL is not configured")
            return False
        
        if not self.payer:
            logger.error("PROCLAIM_PAYER is not configured")
            return False
        
        logger.info("Proclaim API configuration is valid")
        return True
    
    def test_connection(self) -> bool:
        """
        Test the connection to Proclaim API with a sample request
        
        Returns:
            True if connection is successful, False otherwise
        """
        if not self.validate_configuration():
            return False
        
        try:
            # Test with a sample claim number
            test_claim = [131420765]  # Using the sample claim from your data
            
            logger.info("Testing Proclaim API connection...")
            result = self.fetch_claims_data(test_claim)
            
            if result is not None:
                logger.info("✓ Proclaim API connection test successful")
                return True
            else:
                logger.error("✗ Proclaim API connection test failed")
                return False
                
        except Exception as e:
            logger.error(f"✗ Proclaim API connection test failed: {e}")
            return False
    
    def get_api_info(self) -> Dict[str, Any]:
        """
        Get information about the API configuration
        
        Returns:
            Dictionary with API configuration info
        """
        return {
            "api_url": self.api_url,
            "has_access_token": bool(self.access_token),
            "payer": self.payer,
            "configuration_valid": self.validate_configuration()
        }
